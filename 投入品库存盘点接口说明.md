# 投入品库存盘点接口说明

## 接口地址
`POST /nsInputStockTaking/nsInputStockTakingSave`

## 接口功能
- 完全更新投入品库存数量
- 生成库存盘点记录
- 支持新增和更新库存项目

## 请求参数格式

```json
{
  "inputStockId": 2074,
  "houseId": 2055,
  "takingOperator": "admin",
  "takingDate": "2025-09-21",
  "stockItems": [
    {
      "inputStockId": 2062,
      "businessId": 2074,
      "inputType": 2,
      "spec": "袋",
      "unit": "袋",
      "stockQuantity": 10,
      "takingStockQuantity": 10
    },
    {
      "inputStockId": null,
      "businessId": 2074,
      "inputType": 2,
      "spec": "包",
      "stockQuantity": 0,
      "takingStockQuantity": 20
    }
  ],
  "regionDeptId": "100"
}
```

## 参数说明

### 主要参数
- `inputStockId`: 投入品库存ID（可选，用于标识）
- `houseId`: 仓库ID（必填）
- `takingOperator`: 盘点人（必填）
- `takingDate`: 盘点日期，格式：yyyy-MM-dd（必填）
- `regionDeptId`: 农场ID（必填）
- `stockItems`: 库存项目列表（必填）

### stockItems 数组中每个对象的参数
- `inputStockId`: 投入品库存ID（可选，新增时为null）
- `businessId`: 业务ID（必填）
- `inputType`: 投入品类型（必填）
  - 2: 肥料
  - 3: 植保剂
  - 4: 种子
  - 5: 农膜
  - 6: 其他
- `spec`: 规格（可选）
- `unit`: 单位（可选）
- `stockQuantity`: 原库存数量（用于记录）
- `takingStockQuantity`: 盘点数量（必填，实际库存将更新为此值）

## 业务逻辑

1. **库存更新**：
   - 对于已存在的库存项目，直接将库存数量更新为 `takingStockQuantity`
   - 对于新的库存项目（`inputStockId` 为 null），如果 `takingStockQuantity` > 0，则新增库存记录

2. **盘点记录生成**：
   - 为每个库存项目生成一条盘点记录
   - 记录原库存数量和盘点数量
   - 自动计算盘点结果：
     - 0: 正常（原库存 = 盘点数量）
     - 1: 盘亏（原库存 > 盘点数量）
     - 2: 盘盈（原库存 < 盘点数量）

3. **预警状态更新**：
   - 根据库存预警线自动更新预警状态
   - 库存量 < 预警线：预警
   - 库存量 >= 预警线：正常

## 返回结果

### 成功响应
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": null
}
```

### 错误响应
```json
{
  "code": 500,
  "msg": "错误信息",
  "data": null
}
```

## 注意事项

1. 此接口会完全替换现有库存数量，请确保传入的盘点数量准确
2. 新增库存项目时，系统会自动填充投入品的基本信息（名称、分类等）
3. 盘点日期格式必须为 yyyy-MM-dd
4. 所有数量字段不能为负数
5. 接口执行成功后，库存数量将立即生效

## 权限要求
需要 `business:nsInputStockTaking:edit` 权限
