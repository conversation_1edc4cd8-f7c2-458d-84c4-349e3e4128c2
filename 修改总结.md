# 投入品库存盘点接口修改总结

## 修改概述

根据您的需求，我已经完成了投入品库存盘点接口的重构，主要实现了以下功能：

1. **完全更新库存**：支持您提供的新入参格式
2. **生成盘点记录**：为每个操作生成详细的盘点记录
3. **支持删除功能**：可以删除不再需要的库存项目

## 主要修改内容

### 1. 表单类修改 (`SaveInputStockTakingForm.java`)

**新增内容**：
- `InputStockTakingForm` 内部类：支持新的入参格式
- `StockItemForm` 内部类：处理库存项目数据
- `fullSync` 字段：控制是否完全同步（删除未提交的库存）
- `deleteStockIds` 字段：指定要删除的库存项目ID列表

### 2. 服务类修改 (`NsInputStockTakingService.java`)

**删除的方法**：
- 旧的 `save` 方法

**新增的方法**：
- `saveInputStockTaking`：主要的盘点保存方法
- `getInputTypeAndIdDtosFromForm`：从表单提取投入品类型和ID
- `updateWarningStatus`：更新库存预警状态
- `createNewInputStock`：创建新的投入品库存
- `fillInputStockInfo`：填充投入品基本信息
- `createTakingRecord`：创建盘点记录

**核心业务逻辑**：
- 支持更新现有库存数量
- 支持新增库存项目
- 支持两种删除模式：完全同步和指定删除
- 为所有操作生成盘点记录
- 自动计算盘点结果（正常/盘亏/盘盈）

### 3. API控制器修改 (`NsInputStockTakingApi.java`)

**修改内容**：
- 重写了 `nsInputStockTakingSave` 接口
- 使用新的入参格式
- 添加了新的参数验证逻辑
- 删除了旧的验证方法

## 功能特性

### 1. 库存更新
- **现有库存**：直接更新为盘点数量
- **新增库存**：当 `inputStockId` 为 null 且盘点数量 > 0 时新增
- **预警状态**：根据预警线自动更新

### 2. 删除功能
- **完全同步模式**（`fullSync: true`）：
  - 删除数据库中存在但未在 `stockItems` 中提交的库存项目
  - 适用于完全重置某个范围的库存

- **指定删除模式**（`deleteStockIds`）：
  - 只删除指定ID的库存项目
  - 更精确的控制

### 3. 盘点记录
- 为每个库存操作生成记录
- 记录原数量和盘点数量
- 自动计算盘点结果：
  - 0：正常（原库存 = 盘点数量）
  - 1：盘亏（原库存 > 盘点数量）
  - 2：盘盈（原库存 < 盘点数量）

## 接口使用方式

### 基本用法
```json
{
  "houseId": 2055,
  "takingOperator": "admin",
  "takingDate": "2025-09-21",
  "stockItems": [...],
  "regionDeptId": "100"
}
```

### 指定删除
```json
{
  "houseId": 2055,
  "takingOperator": "admin",
  "takingDate": "2025-09-21",
  "stockItems": [...],
  "deleteStockIds": [2063, 2064],
  "regionDeptId": "100"
}
```

### 完全同步
```json
{
  "houseId": 2055,
  "takingOperator": "admin",
  "takingDate": "2025-09-21",
  "fullSync": true,
  "stockItems": [...],
  "regionDeptId": "100"
}
```

## 安全性考虑

1. **权限控制**：需要 `business:nsInputStockTaking:edit` 权限
2. **参数验证**：严格验证所有必填参数
3. **事务控制**：使用 `@Transactional` 确保数据一致性
4. **软删除**：删除操作使用软删除，数据可恢复
5. **操作记录**：所有操作都有详细的盘点记录

## 测试建议

1. **基本功能测试**：更新、新增库存项目
2. **删除功能测试**：两种删除模式
3. **边界条件测试**：空数据、负数等
4. **权限测试**：无权限用户访问
5. **事务测试**：异常情况下的回滚

## 注意事项

1. **数据备份**：删除功能不可逆，建议操作前备份
2. **完全同步**：使用 `fullSync: true` 时要特别小心
3. **性能考虑**：大量数据操作时注意性能影响
4. **日志监控**：关注操作日志，及时发现异常

## 文档输出

1. **接口说明文档**：`投入品库存盘点接口说明.md`
2. **测试用例文档**：`测试用例.md`
3. **修改总结文档**：`修改总结.md`（本文档）

所有修改已完成，接口现在支持您要求的完全更新库存和删除功能。
