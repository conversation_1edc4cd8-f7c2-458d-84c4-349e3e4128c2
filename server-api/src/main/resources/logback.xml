<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <!--定义日志保存的路径-->
    <property name="LOG_HOME" value="./logs"/>
    <!-- 单个日志文件最多50MB, 60天的日志周期，最大不能超过500MB -->
    <property name="maxHistory" value="60"/>
    <!-- 每个日志文件最大大小，超过该值后会新创建一个日志文件 -->
    <property name="maxFileSize" value="50MB"/>
    <property name="totalSizeCap" value="2048MB"/>

    <!--定义一个控制台输出器，名为console-->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <!--按pattern指定的格式输出日志，编码为UTF-8-->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{MM-dd HH:mm:ss.S} %-5level [%thread] %logger{30}:%line - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    <!--定义一个日滚动（每天生成一份）的日志文件-->
    <appender name="file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss} %-5level [%thread] %logger{30}:%line - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 定义保存的文件名 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--%d{yyyy-MM-dd}代表每天生成一个新的日志-->
            <fileNamePattern>${LOG_HOME}/log_%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>

            <maxHistory>${maxHistory}</maxHistory>
            <totalSizeCap>${totalSizeCap}</totalSizeCap>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${maxFileSize}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <!-- 在日滚动文件中，强制只保存错误INFO级别以上信息
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter> -->
    </appender>

    <!-- DEBUG, INFO -->
    <root level="INFO">
        <appender-ref ref="console"/>
        <appender-ref ref="file"/>
    </root>

    <!-- 特殊处理某些包的日志级别 -->
    <logger name="org.springframework"  level="WARN" />
    <logger name="com.jorchi"           level="DEBUG" />
    <logger name="pdfc"                 level="DEBUG" />
    <logger name="p.f.mybatis.util.SQLHelper"      level="INFO" />
    <logger name="springfox.documentation.spring"  level="WARN" />


</configuration>
