spring:
  servlet:
    multipart:
      max-file-size: 20MB
      max-request-size: 20MB
  application:
    name: server-api
  messages:
    basename: i18n/messages
    encoding: UTF-8
  profiles:
    active: test
  main:
    allow-circular-references: true

  jackson:
    locale: zh
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
    deserialization:
      accept-empty-string-as-null-object: true

  web:
    resources:
      static-locations: file:${document-fileUrl}
  weatherKey: 261ad66ccfb64bde8460edd2fb781bf4

mybatis:
  mapper-locations: classpath*:mapper/**/*.xml
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false

server:
  servlet:
    context-parameters:
      enable: true
      maxShowParam: 1
      minTime: -2
  tomcat:
    max-threads: 400
    min-spare-threads: 20
    max-connections: 10000

system:
  aliyun-sms:
    access-key-id: Z4Vhwm2Oo6ZxyOEl
    access-key-secret: Rit1z6PBCVYb6PYd3Lw4feOO6w9etS
    endpoint: dysmsapi.aliyuncs.com
  wechat:
    appId: wx98a6f8989c2a0269
    appSecret: 734f390a2a2370e53178daa9bfe9af55

  webRcHead: j2027C999
  webRcPwd: zheng@2021j0Cns

