# 附件存储
document-fileUrl: /home/<USER>/
system.uploadFolder: ${document-fileUrl}

# system.imageUri: http://************:7088
server:
  port: 7088
#  servlet:
#    context-path: /server-api

framework:
  aoplog:
    enabled: true

spring:
  datasource:
    url: ******************************************************************************************************************************
    username: admintask4farmdev
    password: ENC(aLkLKVqO6Jvmpidj9KIpH5uyIp4FktNg/FOvbGObzfE=)
    hikari:
      minimum-idle: 5
      maximum-pool-size: 10
      auto-commit: true
      connection-timeout: 30000
      idle-timeout: 60000
      max-lifetime: 180000

  redis:
    host:  *************
    port: 16379
    database: 3
    password: ENC(n4t46Xb4wcC4I9iAfoa+8/jYnMDyUiLm)
    timeout: 10s
    lettuce:
      pool:
        min-idle: 0
        max-idle: 8
        max-active: 8
        max-wait: -1ms

resources:
  static-locations: classpath:/META-INF/resources/,classpath:/resources/,classpath:/static/,file:${document-fileUrl}


mqtt:
  enabled: false
  host: tcp://mqtt.zhiyunnongfu.com
  clientId: 77c25c65-7eff-41bc-bad2-2f0347a44b02
  topic: device/#
  username: mqtt
  password: xxx
  timeout: 10
  keepalive: 600
  qos: 1

# rsa publicKey(base64)
system:
  rsa:
    publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCCgTl9CprglMzw3n3oSJyO5rSl+Cpd0CQlPd0OIgNKibrVVSf2Sv35o59H+mpj0ZWenG4fkfceT09jPk3PHVi/q1OeLqu/dRCZiHtUcZaXO5ZCjGrWTJcGT3LYTqi0MPrDMbjDGioZ71MpnomqxjGIN/rxZqkNmHMPdheklBK54wIDAQAB
    privateKey: MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAIKBOX0KmuCUzPDefehInI7mtKX4Kl3QJCU93Q4iA0qJutVVJ/ZK/fmjn0f6amPRlZ6cbh+R9x5PT2M+Tc8dWL+rU54uq791EJmIe1Rxlpc7lkKMatZMlwZPcthOqLQw+sMxuMMaKhnvUymeiarGMYg3+vFmqQ2Ycw92F6SUErnjAgMBAAECgYAspjWa+EPgzkgHqLd+/0jBA2GO3wbvrcSQEUhOC4kAuXp4fnf7pdCa1kYqhBxeJliAOZjbqD7Z2dyZYbmRQMMZ3Z6PmDkOZD/G0l6eZ5kWGxOALgNJromOX1hX9n4gOEv5CiVF29BvWAkfHkf8WYKa9GRrC/+dVJouh+LYVfWjQQJBAPcrvHcgRKwltwCto7w6D2A2JGe7Yd1jLHpB2SRyfYVO0Cd3/kt1dOp2tLabu/Kxq7v4VERGX5bYokmwSf9yABMCQQCHKqF/LmvQLVJZtOIHaZKcOBjUD+OqDmoXnAIUA/I66WHg33hSUlPzS8NCOmYwYVNxIA3dU2NbavCthLGj4bjxAkAvojgh5RAW9PW9pbkGT1IjQovlidEkbzZlIUeNpJrYFAY9dPyG4k3z2YieIIkqDl+ATC1BMya81WcBOH4qQngNAkEAhKiZghpMZC/nD/YQlJfP9ZS1AYRqvB+TjoJmI8DPv3QIxUxuadU1YaatB9YLL1aGL5P3J47NCA8IEjUWp7JE8QJBAPNn5FWihBmlltDWBBLThOohBA9WUIQqDQhfDr6Mb8q//lglUmILZkYyYPpZsOmu0KvsimH2X6lWmL3XWwPLXSw=
  webRcHead: RkekjJkjIuyHijKJhUy6Ugd486M2jNmjhYuh435087A94JkIolPKoHyGtVrDcNjI8R5K7J8k
  webRcPwd: r2j8IK96yR3vM4k7Jh2Gy4UhB5g6V8f8f6G5b2v2L
