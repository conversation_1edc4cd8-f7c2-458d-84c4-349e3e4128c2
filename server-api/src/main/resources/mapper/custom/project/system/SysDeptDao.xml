<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ==================可直接使用Base配置文件中定义的节点！=================== -->
<!-- ============================================================== -->
<mapper namespace="com.jorchi.project.system.dao.SysDeptDao">
	<!-- 请在下方添加自定义配置-->
	<resultMap type="com.jorchi.project.system.domain.SysDept" id="SysDeptResult">
		<id     property="deptId"     column="dept_id"     />
		<result property="parentId"   column="parent_id"   />
		<result property="ancestors"  column="ancestors"   />
		<result property="accessDeptIds" column="access_dept_ids" />
		<result property="deptName"   column="dept_name"   />
		<result property="orderNum"   column="order_num"   />
		<result property="leader"     column="leader"      />
		<result property="phone"      column="phone"       />
		<result property="email"      column="email"       />
		<result property="status"     column="status"      />
		<result property="delFlag"    column="del_flag"    />
		<!--<result property="parentName" column="parent_name" />-->
		<result property="createBy"   column="create_by"   />
		<result property="createTime" column="create_time" />
		<result property="updateBy"   column="update_by"   />
		<result property="updateTime" column="update_time" />
		<result property="sno"        column="sno" />
		<result column="pin_yin_1"    property="pinYin1" />
		<result column="pin_yin_2"    property="pinYin2" />
		<result property="blockArea"        column="block_area" />
		<result column="latitude"     property="latitude" />
		<result column="longitude"    property="longitude" />
		<result column="license"      property="license" />
	</resultMap>

	<sql id="selectDeptVo">
		select d.dept_id, d.parent_id, d.ancestors, d.access_dept_ids, d.dept_name, d.order_num, d.leader, d.phone, d.email, d.status,
			   d.del_flag, d.create_by, d.create_time,d.sno,d.block_area,d.latitude,d.longitude,d.license
		from ns_sys_dept d
	</sql>

	<select id="selectDeptList" parameterType="com.jorchi.project.system.domain.SysDept" resultMap="SysDeptResult">
		<include refid="selectDeptVo"/>
		where d.del_flag = '0'
		<if test="dept.parentId != null and dept.parentId != 0">
			AND parent_id = #{dept.parentId}
		</if>
		<if test="dept.deptName != null and dept.deptName != ''">
			AND
			(
			dept_name like concat('%', #{dept.deptName}, '%') or
			sno like concat(#{deptName}, '%') or
			pin_yin_1 like concat('%', lower(#{dept.deptName}), '%') or
			pin_yin_2 like concat('%', lower(#{dept.deptName}), '%')
			)
		</if>
		<if test="dept.status != null and dept.status != ''">
			AND status = #{dept.status}
		</if>
		<if test="dept.sno != null and dept.sno != ''">
			AND sno like #{dept.sno} || '%'
		</if>
-- 		and d.dept_id IN ( SELECT dept_id FROM sys_dept WHERE dept_id = d.dept_id or find_in_set( d.dept_id , ancestors ) )
		<!-- 数据范围过滤 -->
		<!--<if test="deptId == null or deptId != -1">
			${params.dataScope}
		</if>-->
		<if test="deptIds !=null and deptIds.size > 0">
		and dept_id in
			<foreach collection="deptIds" item="item" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
		order by d.parent_id, d.order_num, d.dept_id
	</select>

	<!--
	省市区查询
    省：parent_id = 100
    市：parent_id = 省code
    区： parent_id = 市code
	 -->
	<select id="selectDeptByParentId" parameterType="com.jorchi.project.system.domain.SysDept" resultMap="SysDeptResult">
		<include refid="selectDeptVo"/>
		where d.parent_id = #{parentId} AND d.del_flag = '0'
		<!-- 数据范围过滤 -->
		<!--${params.dataScope}-->
		order by d.parent_id, d.order_num, d.dept_id
	</select>

	<select id="selectDeptListByRoleId" resultType="Long">
		select d.dept_id
		from ns_sys_dept d
		left join ns_sys_role_dept rd on d.dept_id = rd.dept_id
		where rd.role_id = #{roleId}
		<if test="deptCheckStrictly">
			and d.dept_id not in (select d.parent_id from ns_sys_dept d inner join ns_sys_role_dept rd on d.dept_id = rd.dept_id and rd.role_id = #{roleId})
		</if>
		order by d.parent_id, d.order_num
	</select>

	<select id="selectDeptById" parameterType="Long" resultMap="SysDeptResult">
		<include refid="selectDeptVo"/>
		where dept_id = #{deptId}
	</select>

	<select id="checkDeptExistUser" parameterType="Long" resultType="int">
		select count(1) from ns_sys_user where dept_id = #{deptId} and del_flag = '0'
	</select>

	<select id="hasChildByDeptId" parameterType="Long" resultType="int">
		select count(1) from ns_sys_dept
		where del_flag = '0' and parent_id = #{deptId} limit 1
	</select>

	<select id="selectChildrenDeptById" parameterType="Long" resultMap="SysDeptResult">
		select *
		FROM ns_sys_dept
		WHERE ancestors like concat('%', #{deptId}, '%')
	</select>

	<select id="selectNormalChildrenDeptById" parameterType="Long" resultType="int">
		/*select count(*) from ns_sys_dept where status = '0' and del_flag = '0' and FIND_IN_SET(#{deptId}, ancestors)>0*/
		/*select count(*) from ns_sys_dept where status = '0' and del_flag = '0' and instr(','||ancestors||',',','+#{deptId}+',') >0*/
		select count(*) from ns_sys_dept where status = '0' and del_flag = '0' and ancestors like concat('%', #{deptId}, '%')

	</select>

	<select id="checkDeptNameUnique" resultMap="SysDeptResult">
		<include refid="selectDeptVo"/>
		where dept_name=#{deptName} and parent_id = #{parentId} and del_flag = '0' limit 1
	</select>

	<insert id="insertDept" parameterType="com.jorchi.project.system.domain.SysDept">
		insert into ns_sys_dept(
		dept_id,
		<if test="parentId != null and parentId != 0">parent_id,</if>
		<if test="deptName != null and deptName != ''">dept_name,</if>
		<if test="ancestors != null and ancestors != ''">ancestors,</if>
		<if test="accessDeptIds !=null and accessDeptIds !=''"> access_dept_ids, </if>
		<if test="orderNum != null and orderNum != ''">order_num,</if>
		<if test="leader != null and leader != ''">leader,</if>
		<if test="phone != null and phone != ''">phone,</if>
		<if test="email != null and email != ''">email,</if>
		<if test="status != null">status,</if>
		<if test="createBy != null and createBy != ''">create_by,</if>
		<if test="blockArea != null and blockArea != ''">block_area,</if>
		<if test="latitude !=null and latitude!=''">latitude,</if>
		<if test="longitude !=null and longitude!=''"> longitude,</if>
		<if test="license !=null and license!=''"> license,</if>
		sno,pin_yin_1,pin_yin_2,
		create_time
		)values(
		#{deptId},
		<if test="parentId != null and parentId != 0">#{parentId},</if>
		<if test="deptName != null and deptName != ''">#{deptName},</if>
		<if test="ancestors != null and ancestors != ''">#{ancestors},</if>
		<if test="accessDeptIds !=null and accessDeptIds !=''">#{accessDeptIds}, </if>
		<if test="orderNum != null and orderNum != ''">#{orderNum},</if>
		<if test="leader != null and leader != ''">#{leader},</if>
		<if test="phone != null and phone != ''">#{phone},</if>
		<if test="email != null and email != ''">#{email},</if>
		<if test="status != null">#{status},</if>
		<if test="createBy != null and createBy != ''">#{createBy},</if>
		<if test="blockArea != null and blockArea != ''">#{blockArea},</if>
		<if test="latitude !=null and latitude!=''">
			#{latitude},
		</if>
		<if test="longitude !=null and longitude!=''">
			#{longitude},
		</if>
		<if test="license !=null and license!=''">
			#{license},
		</if>
		#{sno},#{pinYin1},#{pinYin2},
		now()
		)
	</insert>
	<update id="updateDept" parameterType="com.jorchi.project.system.domain.SysDept">
		update ns_sys_dept
		<set>
			<if test="parentId != null and parentId != 0">parent_id = #{parentId},</if>
			<if test="deptName != null and deptName != ''">dept_name = #{deptName},</if>
			<if test="ancestors != null and ancestors != ''">ancestors = #{ancestors},</if>
		    <if test="accessDeptIds !=null and accessDeptIds !=''">access_dept_ids = #{accessDeptIds}, </if>
			order_num = #{orderNum},
			<if test="leader != null">leader = #{leader},</if>
			<if test="phone != null">phone = #{phone},</if>
			<if test="email != null">email = #{email},</if>
			<if test="status != null and status != ''">status = #{status},</if>
			<if test="sno != null and sno != ''">sno = #{sno},</if>
			pin_yin_1 = #{pinYin1},pin_yin_2 = #{pinYin2},
			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
		    <if test="blockArea != null and blockArea != ''">block_area = #{blockArea},</if>
		    <if test="latitude != null and latitude != ''">latitude = #{latitude},</if>
		    <if test="longitude != null and longitude != ''">longitude = #{longitude},</if>
		    <if test="license != null and license != ''">license = #{license},</if>
			update_time = now()
		</set>
		where dept_id = #{deptId}
	</update>

	<update id="updateDeptChildren" parameterType="java.util.List">
		update ns_sys_dept set ancestors =
		<foreach collection="depts" item="item" index="index"
				 separator=" " open="case dept_id" close="end">
			when #{item.deptId} then #{item.ancestors}
		</foreach>
		where dept_id in
		<foreach collection="depts" item="item" index="index"
				 separator="," open="(" close=")">
			#{item.deptId}
		</foreach>
	</update>

	<update id="updateDeptStatus" parameterType="com.jorchi.project.system.domain.SysDept">
		update ns_sys_dept
		<set>
			<if test="status != null and status != ''">status = #{status},</if>
			<if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
			update_time = now()
		</set>
		<where>
			dept_id in
			<if test="ancestors != null and ancestors !='' ">
				<foreach item="item" index="index" collection="ancestorsList" open="(" separator="," close=")">
					#{item}
				</foreach>
			</if>
		</where>
	</update>

	<delete id="deleteDeptById" parameterType="Long">
		update ns_sys_dept set del_flag = '2', dept_id = ${deletedId} where dept_id = #{deptId}
	</delete>

    <select id="selectDeptNameByDeptId" resultType="java.lang.String">
        select dept_name
        from ns_sys_dept
        where dept_id = #{deptId}
        and del_flag = '0'
    </select>

	<select id="deptNameList" resultType="com.jorchi.project.system.vo.SysDeptVo">
        SELECT
			dept_id,
			dept_name,
			sno
		FROM
			ns_sys_dept
		WHERE
			parent_id = '100'
			AND status = '0'
			AND del_flag = '0'
    </select>

	<select id="selectByIds" resultType="com.jorchi.project.system.domain.SysDept">
		<include refid="selectDeptVo"/>
		where d.dept_id in
		<foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
			#{item}
		</foreach>

	</select>



</mapper>
