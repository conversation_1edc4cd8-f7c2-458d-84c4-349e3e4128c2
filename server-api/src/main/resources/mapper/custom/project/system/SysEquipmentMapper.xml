<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jorchi.mqtt.mapper.SysEquipmentMapper">
  <resultMap id="BaseResultMap" type="com.jorchi.mqtt.entity.SysEquipment">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <constructor>
      <idArg column="id" javaType="java.lang.Long" jdbcType="BIGINT" />
      <arg column="device_name" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="device_node_id" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="device_type" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="linkage_type" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="node_type" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="rssi" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="battery" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="online_status" javaType="java.lang.String" jdbcType="CHAR" />
      <arg column="online_time" javaType="java.util.Date" jdbcType="TIMESTAMP" />
      <arg column="hard_version" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="soft_version" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="longitude" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="latitude" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="switchs" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="gateway" javaType="java.lang.Long" jdbcType="BIGINT" />
      <arg column="binding_house" javaType="java.lang.String" jdbcType="CHAR" />
      <arg column="binding" javaType="java.lang.String" jdbcType="CHAR" />
      <arg column="warning" javaType="java.lang.String" jdbcType="CHAR" />
      <arg column="b_lower_limit" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="t_lower_limit" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="t_upper_limit" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="q_lower_limit" javaType="java.math.BigDecimal" jdbcType="DECIMAL" />
      <arg column="q_upper_limit" javaType="java.math.BigDecimal" jdbcType="DECIMAL" />
      <arg column="l_lower_limit" javaType="java.math.BigDecimal" jdbcType="DECIMAL" />
      <arg column="l_upper_limit" javaType="java.math.BigDecimal" jdbcType="DECIMAL" />
      <arg column="e_lower_limit" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="e_upper_limit" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="a_lower_limit" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="a_upper_limit" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="m_lower_limit" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="m_upper_limit" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="ph_lower_limit" javaType="java.math.BigDecimal" jdbcType="DECIMAL" />
      <arg column="ph_upper_limit" javaType="java.math.BigDecimal" jdbcType="DECIMAL" />
      <arg column="co2_lower_limit" javaType="java.math.BigDecimal" jdbcType="DECIMAL" />
      <arg column="co2_upper_limit" javaType="java.math.BigDecimal" jdbcType="DECIMAL" />
      <arg column="s_lower_limit" javaType="java.math.BigDecimal" jdbcType="DECIMAL" />
      <arg column="s_upper_limit" javaType="java.math.BigDecimal" jdbcType="DECIMAL" />
      <arg column="w_lower_limit" javaType="java.math.BigDecimal" jdbcType="DECIMAL" />
      <arg column="w_upper_limit" javaType="java.math.BigDecimal" jdbcType="DECIMAL" />
      <arg column="r_lower_limit" javaType="java.math.BigDecimal" jdbcType="DECIMAL" />
      <arg column="r_upper_limit" javaType="java.math.BigDecimal" jdbcType="DECIMAL" />
      <arg column="del_flag" javaType="java.lang.String" jdbcType="CHAR" />
      <arg column="create_by" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="create_time" javaType="java.util.Date" jdbcType="TIMESTAMP" />
      <arg column="update_by" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="update_time" javaType="java.util.Date" jdbcType="TIMESTAMP" />
      <arg column="house_id" javaType="java.lang.Long" jdbcType="BIGINT" />
      <arg column="port" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="command" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="p_lower_limit" javaType="java.math.BigDecimal" jdbcType="DECIMAL" />
      <arg column="p_upper_limit" javaType="java.math.BigDecimal" jdbcType="DECIMAL" />
      <arg column="p2_lower_limit" javaType="java.math.BigDecimal" jdbcType="DECIMAL" />
      <arg column="p2_upper_limit" javaType="java.math.BigDecimal" jdbcType="DECIMAL" />
      <arg column="d" javaType="java.lang.String" jdbcType="VARCHAR" />
    </constructor>
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, device_name, device_node_id, device_type, linkage_type, node_type, rssi, battery, 
    online_status, online_time, hard_version, soft_version, longitude, latitude, switchs, 
    gateway, binding_house, binding, warning, b_lower_limit, t_lower_limit, t_upper_limit, 
    q_lower_limit, q_upper_limit, l_lower_limit, l_upper_limit, e_lower_limit, e_upper_limit, 
    a_lower_limit, a_upper_limit, m_lower_limit, m_upper_limit, ph_lower_limit, ph_upper_limit, 
    co2_lower_limit, co2_upper_limit, s_lower_limit, s_upper_limit, w_lower_limit, w_upper_limit, 
    r_lower_limit, r_upper_limit, del_flag, create_by, create_time, update_by, update_time, 
    house_id, port, command, p_lower_limit, p_upper_limit, p2_lower_limit, p2_upper_limit, 
    d
  </sql>
  <select id="selectByExample" parameterType="com.jorchi.mqtt.entity.SysEquipmentExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sys_equipment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from sys_equipment
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from sys_equipment
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.jorchi.mqtt.entity.SysEquipmentExample">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from sys_equipment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.jorchi.mqtt.entity.SysEquipment">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sys_equipment (device_name, device_node_id, device_type, 
      linkage_type, node_type, rssi, 
      battery, online_status, online_time, 
      hard_version, soft_version, longitude, 
      latitude, switchs, gateway, 
      binding_house, binding, warning, 
      b_lower_limit, t_lower_limit, t_upper_limit, 
      q_lower_limit, q_upper_limit, l_lower_limit, 
      l_upper_limit, e_lower_limit, e_upper_limit, 
      a_lower_limit, a_upper_limit, m_lower_limit, 
      m_upper_limit, ph_lower_limit, ph_upper_limit, 
      co2_lower_limit, co2_upper_limit, s_lower_limit, 
      s_upper_limit, w_lower_limit, w_upper_limit, 
      r_lower_limit, r_upper_limit, del_flag, 
      create_by, create_time, update_by, 
      update_time, house_id, port, 
      command, p_lower_limit, p_upper_limit, 
      p2_lower_limit, p2_upper_limit, d
      )
    values (#{deviceName,jdbcType=VARCHAR}, #{deviceNodeId,jdbcType=VARCHAR}, #{deviceType,jdbcType=INTEGER}, 
      #{linkageType,jdbcType=VARCHAR}, #{nodeType,jdbcType=INTEGER}, #{rssi,jdbcType=INTEGER}, 
      #{battery,jdbcType=INTEGER}, #{onlineStatus,jdbcType=CHAR}, #{onlineTime,jdbcType=TIMESTAMP}, 
      #{hardVersion,jdbcType=VARCHAR}, #{softVersion,jdbcType=VARCHAR}, #{longitude,jdbcType=VARCHAR}, 
      #{latitude,jdbcType=VARCHAR}, #{switchs,jdbcType=VARCHAR}, #{gateway,jdbcType=BIGINT}, 
      #{bindingHouse,jdbcType=CHAR}, #{binding,jdbcType=CHAR}, #{warning,jdbcType=CHAR}, 
      #{bLowerLimit,jdbcType=INTEGER}, #{tLowerLimit,jdbcType=INTEGER}, #{tUpperLimit,jdbcType=INTEGER}, 
      #{qLowerLimit,jdbcType=DECIMAL}, #{qUpperLimit,jdbcType=DECIMAL}, #{lLowerLimit,jdbcType=DECIMAL}, 
      #{lUpperLimit,jdbcType=DECIMAL}, #{eLowerLimit,jdbcType=INTEGER}, #{eUpperLimit,jdbcType=INTEGER}, 
      #{aLowerLimit,jdbcType=INTEGER}, #{aUpperLimit,jdbcType=INTEGER}, #{mLowerLimit,jdbcType=INTEGER}, 
      #{mUpperLimit,jdbcType=INTEGER}, #{phLowerLimit,jdbcType=DECIMAL}, #{phUpperLimit,jdbcType=DECIMAL}, 
      #{co2LowerLimit,jdbcType=DECIMAL}, #{co2UpperLimit,jdbcType=DECIMAL}, #{sLowerLimit,jdbcType=DECIMAL}, 
      #{sUpperLimit,jdbcType=DECIMAL}, #{wLowerLimit,jdbcType=DECIMAL}, #{wUpperLimit,jdbcType=DECIMAL}, 
      #{rLowerLimit,jdbcType=DECIMAL}, #{rUpperLimit,jdbcType=DECIMAL}, #{delFlag,jdbcType=CHAR}, 
      #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{houseId,jdbcType=BIGINT}, #{port,jdbcType=INTEGER}, 
      #{command,jdbcType=VARCHAR}, #{pLowerLimit,jdbcType=DECIMAL}, #{pUpperLimit,jdbcType=DECIMAL}, 
      #{p2LowerLimit,jdbcType=DECIMAL}, #{p2UpperLimit,jdbcType=DECIMAL}, #{d,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.jorchi.mqtt.entity.SysEquipment">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sys_equipment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="deviceName != null">
        device_name,
      </if>
      <if test="deviceNodeId != null">
        device_node_id,
      </if>
      <if test="deviceType != null">
        device_type,
      </if>
      <if test="linkageType != null">
        linkage_type,
      </if>
      <if test="nodeType != null">
        node_type,
      </if>
      <if test="rssi != null">
        rssi,
      </if>
      <if test="battery != null">
        battery,
      </if>
      <if test="onlineStatus != null">
        online_status,
      </if>
      <if test="onlineTime != null">
        online_time,
      </if>
      <if test="hardVersion != null">
        hard_version,
      </if>
      <if test="softVersion != null">
        soft_version,
      </if>
      <if test="longitude != null">
        longitude,
      </if>
      <if test="latitude != null">
        latitude,
      </if>
      <if test="switchs != null">
        switchs,
      </if>
      <if test="gateway != null">
        gateway,
      </if>
      <if test="bindingHouse != null">
        binding_house,
      </if>
      <if test="binding != null">
        binding,
      </if>
      <if test="warning != null">
        warning,
      </if>
      <if test="bLowerLimit != null">
        b_lower_limit,
      </if>
      <if test="tLowerLimit != null">
        t_lower_limit,
      </if>
      <if test="tUpperLimit != null">
        t_upper_limit,
      </if>
      <if test="qLowerLimit != null">
        q_lower_limit,
      </if>
      <if test="qUpperLimit != null">
        q_upper_limit,
      </if>
      <if test="lLowerLimit != null">
        l_lower_limit,
      </if>
      <if test="lUpperLimit != null">
        l_upper_limit,
      </if>
      <if test="eLowerLimit != null">
        e_lower_limit,
      </if>
      <if test="eUpperLimit != null">
        e_upper_limit,
      </if>
      <if test="aLowerLimit != null">
        a_lower_limit,
      </if>
      <if test="aUpperLimit != null">
        a_upper_limit,
      </if>
      <if test="mLowerLimit != null">
        m_lower_limit,
      </if>
      <if test="mUpperLimit != null">
        m_upper_limit,
      </if>
      <if test="phLowerLimit != null">
        ph_lower_limit,
      </if>
      <if test="phUpperLimit != null">
        ph_upper_limit,
      </if>
      <if test="co2LowerLimit != null">
        co2_lower_limit,
      </if>
      <if test="co2UpperLimit != null">
        co2_upper_limit,
      </if>
      <if test="sLowerLimit != null">
        s_lower_limit,
      </if>
      <if test="sUpperLimit != null">
        s_upper_limit,
      </if>
      <if test="wLowerLimit != null">
        w_lower_limit,
      </if>
      <if test="wUpperLimit != null">
        w_upper_limit,
      </if>
      <if test="rLowerLimit != null">
        r_lower_limit,
      </if>
      <if test="rUpperLimit != null">
        r_upper_limit,
      </if>
      <if test="delFlag != null">
        del_flag,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="houseId != null">
        house_id,
      </if>
      <if test="port != null">
        port,
      </if>
      <if test="command != null">
        command,
      </if>
      <if test="pLowerLimit != null">
        p_lower_limit,
      </if>
      <if test="pUpperLimit != null">
        p_upper_limit,
      </if>
      <if test="p2LowerLimit != null">
        p2_lower_limit,
      </if>
      <if test="p2UpperLimit != null">
        p2_upper_limit,
      </if>
      <if test="d != null">
        d,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="deviceName != null">
        #{deviceName,jdbcType=VARCHAR},
      </if>
      <if test="deviceNodeId != null">
        #{deviceNodeId,jdbcType=VARCHAR},
      </if>
      <if test="deviceType != null">
        #{deviceType,jdbcType=INTEGER},
      </if>
      <if test="linkageType != null">
        #{linkageType,jdbcType=VARCHAR},
      </if>
      <if test="nodeType != null">
        #{nodeType,jdbcType=INTEGER},
      </if>
      <if test="rssi != null">
        #{rssi,jdbcType=INTEGER},
      </if>
      <if test="battery != null">
        #{battery,jdbcType=INTEGER},
      </if>
      <if test="onlineStatus != null">
        #{onlineStatus,jdbcType=CHAR},
      </if>
      <if test="onlineTime != null">
        #{onlineTime,jdbcType=TIMESTAMP},
      </if>
      <if test="hardVersion != null">
        #{hardVersion,jdbcType=VARCHAR},
      </if>
      <if test="softVersion != null">
        #{softVersion,jdbcType=VARCHAR},
      </if>
      <if test="longitude != null">
        #{longitude,jdbcType=VARCHAR},
      </if>
      <if test="latitude != null">
        #{latitude,jdbcType=VARCHAR},
      </if>
      <if test="switchs != null">
        #{switchs,jdbcType=VARCHAR},
      </if>
      <if test="gateway != null">
        #{gateway,jdbcType=BIGINT},
      </if>
      <if test="bindingHouse != null">
        #{bindingHouse,jdbcType=CHAR},
      </if>
      <if test="binding != null">
        #{binding,jdbcType=CHAR},
      </if>
      <if test="warning != null">
        #{warning,jdbcType=CHAR},
      </if>
      <if test="bLowerLimit != null">
        #{bLowerLimit,jdbcType=INTEGER},
      </if>
      <if test="tLowerLimit != null">
        #{tLowerLimit,jdbcType=INTEGER},
      </if>
      <if test="tUpperLimit != null">
        #{tUpperLimit,jdbcType=INTEGER},
      </if>
      <if test="qLowerLimit != null">
        #{qLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="qUpperLimit != null">
        #{qUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="lLowerLimit != null">
        #{lLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="lUpperLimit != null">
        #{lUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="eLowerLimit != null">
        #{eLowerLimit,jdbcType=INTEGER},
      </if>
      <if test="eUpperLimit != null">
        #{eUpperLimit,jdbcType=INTEGER},
      </if>
      <if test="aLowerLimit != null">
        #{aLowerLimit,jdbcType=INTEGER},
      </if>
      <if test="aUpperLimit != null">
        #{aUpperLimit,jdbcType=INTEGER},
      </if>
      <if test="mLowerLimit != null">
        #{mLowerLimit,jdbcType=INTEGER},
      </if>
      <if test="mUpperLimit != null">
        #{mUpperLimit,jdbcType=INTEGER},
      </if>
      <if test="phLowerLimit != null">
        #{phLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="phUpperLimit != null">
        #{phUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="co2LowerLimit != null">
        #{co2LowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="co2UpperLimit != null">
        #{co2UpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="sLowerLimit != null">
        #{sLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="sUpperLimit != null">
        #{sUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="wLowerLimit != null">
        #{wLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="wUpperLimit != null">
        #{wUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="rLowerLimit != null">
        #{rLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="rUpperLimit != null">
        #{rUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="delFlag != null">
        #{delFlag,jdbcType=CHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="houseId != null">
        #{houseId,jdbcType=BIGINT},
      </if>
      <if test="port != null">
        #{port,jdbcType=INTEGER},
      </if>
      <if test="command != null">
        #{command,jdbcType=VARCHAR},
      </if>
      <if test="pLowerLimit != null">
        #{pLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="pUpperLimit != null">
        #{pUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="p2LowerLimit != null">
        #{p2LowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="p2UpperLimit != null">
        #{p2UpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="d != null">
        #{d,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.jorchi.mqtt.entity.SysEquipmentExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from sys_equipment
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update sys_equipment
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.deviceName != null">
        device_name = #{record.deviceName,jdbcType=VARCHAR},
      </if>
      <if test="record.deviceNodeId != null">
        device_node_id = #{record.deviceNodeId,jdbcType=VARCHAR},
      </if>
      <if test="record.deviceType != null">
        device_type = #{record.deviceType,jdbcType=INTEGER},
      </if>
      <if test="record.linkageType != null">
        linkage_type = #{record.linkageType,jdbcType=VARCHAR},
      </if>
      <if test="record.nodeType != null">
        node_type = #{record.nodeType,jdbcType=INTEGER},
      </if>
      <if test="record.rssi != null">
        rssi = #{record.rssi,jdbcType=INTEGER},
      </if>
      <if test="record.battery != null">
        battery = #{record.battery,jdbcType=INTEGER},
      </if>
      <if test="record.onlineStatus != null">
        online_status = #{record.onlineStatus,jdbcType=CHAR},
      </if>
      <if test="record.onlineTime != null">
        online_time = #{record.onlineTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.hardVersion != null">
        hard_version = #{record.hardVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.softVersion != null">
        soft_version = #{record.softVersion,jdbcType=VARCHAR},
      </if>
      <if test="record.longitude != null">
        longitude = #{record.longitude,jdbcType=VARCHAR},
      </if>
      <if test="record.latitude != null">
        latitude = #{record.latitude,jdbcType=VARCHAR},
      </if>
      <if test="record.switchs != null">
        switchs = #{record.switchs,jdbcType=VARCHAR},
      </if>
      <if test="record.gateway != null">
        gateway = #{record.gateway,jdbcType=BIGINT},
      </if>
      <if test="record.bindingHouse != null">
        binding_house = #{record.bindingHouse,jdbcType=CHAR},
      </if>
      <if test="record.binding != null">
        binding = #{record.binding,jdbcType=CHAR},
      </if>
      <if test="record.warning != null">
        warning = #{record.warning,jdbcType=CHAR},
      </if>
      <if test="record.bLowerLimit != null">
        b_lower_limit = #{record.bLowerLimit,jdbcType=INTEGER},
      </if>
      <if test="record.tLowerLimit != null">
        t_lower_limit = #{record.tLowerLimit,jdbcType=INTEGER},
      </if>
      <if test="record.tUpperLimit != null">
        t_upper_limit = #{record.tUpperLimit,jdbcType=INTEGER},
      </if>
      <if test="record.qLowerLimit != null">
        q_lower_limit = #{record.qLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.qUpperLimit != null">
        q_upper_limit = #{record.qUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.lLowerLimit != null">
        l_lower_limit = #{record.lLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.lUpperLimit != null">
        l_upper_limit = #{record.lUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.eLowerLimit != null">
        e_lower_limit = #{record.eLowerLimit,jdbcType=INTEGER},
      </if>
      <if test="record.eUpperLimit != null">
        e_upper_limit = #{record.eUpperLimit,jdbcType=INTEGER},
      </if>
      <if test="record.aLowerLimit != null">
        a_lower_limit = #{record.aLowerLimit,jdbcType=INTEGER},
      </if>
      <if test="record.aUpperLimit != null">
        a_upper_limit = #{record.aUpperLimit,jdbcType=INTEGER},
      </if>
      <if test="record.mLowerLimit != null">
        m_lower_limit = #{record.mLowerLimit,jdbcType=INTEGER},
      </if>
      <if test="record.mUpperLimit != null">
        m_upper_limit = #{record.mUpperLimit,jdbcType=INTEGER},
      </if>
      <if test="record.phLowerLimit != null">
        ph_lower_limit = #{record.phLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.phUpperLimit != null">
        ph_upper_limit = #{record.phUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.co2LowerLimit != null">
        co2_lower_limit = #{record.co2LowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.co2UpperLimit != null">
        co2_upper_limit = #{record.co2UpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.sLowerLimit != null">
        s_lower_limit = #{record.sLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.sUpperLimit != null">
        s_upper_limit = #{record.sUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.wLowerLimit != null">
        w_lower_limit = #{record.wLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.wUpperLimit != null">
        w_upper_limit = #{record.wUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.rLowerLimit != null">
        r_lower_limit = #{record.rLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.rUpperLimit != null">
        r_upper_limit = #{record.rUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.delFlag != null">
        del_flag = #{record.delFlag,jdbcType=CHAR},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=VARCHAR},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.houseId != null">
        house_id = #{record.houseId,jdbcType=BIGINT},
      </if>
      <if test="record.port != null">
        port = #{record.port,jdbcType=INTEGER},
      </if>
      <if test="record.command != null">
        command = #{record.command,jdbcType=VARCHAR},
      </if>
      <if test="record.pLowerLimit != null">
        p_lower_limit = #{record.pLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.pUpperLimit != null">
        p_upper_limit = #{record.pUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.p2LowerLimit != null">
        p2_lower_limit = #{record.p2LowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.p2UpperLimit != null">
        p2_upper_limit = #{record.p2UpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="record.d != null">
        d = #{record.d,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update sys_equipment
    set id = #{record.id,jdbcType=BIGINT},
      device_name = #{record.deviceName,jdbcType=VARCHAR},
      device_node_id = #{record.deviceNodeId,jdbcType=VARCHAR},
      device_type = #{record.deviceType,jdbcType=INTEGER},
      linkage_type = #{record.linkageType,jdbcType=VARCHAR},
      node_type = #{record.nodeType,jdbcType=INTEGER},
      rssi = #{record.rssi,jdbcType=INTEGER},
      battery = #{record.battery,jdbcType=INTEGER},
      online_status = #{record.onlineStatus,jdbcType=CHAR},
      online_time = #{record.onlineTime,jdbcType=TIMESTAMP},
      hard_version = #{record.hardVersion,jdbcType=VARCHAR},
      soft_version = #{record.softVersion,jdbcType=VARCHAR},
      longitude = #{record.longitude,jdbcType=VARCHAR},
      latitude = #{record.latitude,jdbcType=VARCHAR},
      switchs = #{record.switchs,jdbcType=VARCHAR},
      gateway = #{record.gateway,jdbcType=BIGINT},
      binding_house = #{record.bindingHouse,jdbcType=CHAR},
      binding = #{record.binding,jdbcType=CHAR},
      warning = #{record.warning,jdbcType=CHAR},
      b_lower_limit = #{record.bLowerLimit,jdbcType=INTEGER},
      t_lower_limit = #{record.tLowerLimit,jdbcType=INTEGER},
      t_upper_limit = #{record.tUpperLimit,jdbcType=INTEGER},
      q_lower_limit = #{record.qLowerLimit,jdbcType=DECIMAL},
      q_upper_limit = #{record.qUpperLimit,jdbcType=DECIMAL},
      l_lower_limit = #{record.lLowerLimit,jdbcType=DECIMAL},
      l_upper_limit = #{record.lUpperLimit,jdbcType=DECIMAL},
      e_lower_limit = #{record.eLowerLimit,jdbcType=INTEGER},
      e_upper_limit = #{record.eUpperLimit,jdbcType=INTEGER},
      a_lower_limit = #{record.aLowerLimit,jdbcType=INTEGER},
      a_upper_limit = #{record.aUpperLimit,jdbcType=INTEGER},
      m_lower_limit = #{record.mLowerLimit,jdbcType=INTEGER},
      m_upper_limit = #{record.mUpperLimit,jdbcType=INTEGER},
      ph_lower_limit = #{record.phLowerLimit,jdbcType=DECIMAL},
      ph_upper_limit = #{record.phUpperLimit,jdbcType=DECIMAL},
      co2_lower_limit = #{record.co2LowerLimit,jdbcType=DECIMAL},
      co2_upper_limit = #{record.co2UpperLimit,jdbcType=DECIMAL},
      s_lower_limit = #{record.sLowerLimit,jdbcType=DECIMAL},
      s_upper_limit = #{record.sUpperLimit,jdbcType=DECIMAL},
      w_lower_limit = #{record.wLowerLimit,jdbcType=DECIMAL},
      w_upper_limit = #{record.wUpperLimit,jdbcType=DECIMAL},
      r_lower_limit = #{record.rLowerLimit,jdbcType=DECIMAL},
      r_upper_limit = #{record.rUpperLimit,jdbcType=DECIMAL},
      del_flag = #{record.delFlag,jdbcType=CHAR},
      create_by = #{record.createBy,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_by = #{record.updateBy,jdbcType=VARCHAR},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      house_id = #{record.houseId,jdbcType=BIGINT},
      port = #{record.port,jdbcType=INTEGER},
      command = #{record.command,jdbcType=VARCHAR},
      p_lower_limit = #{record.pLowerLimit,jdbcType=DECIMAL},
      p_upper_limit = #{record.pUpperLimit,jdbcType=DECIMAL},
      p2_lower_limit = #{record.p2LowerLimit,jdbcType=DECIMAL},
      p2_upper_limit = #{record.p2UpperLimit,jdbcType=DECIMAL},
      d = #{record.d,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.jorchi.mqtt.entity.SysEquipment">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update sys_equipment
    <set>
      <if test="deviceName != null">
        device_name = #{deviceName,jdbcType=VARCHAR},
      </if>
      <if test="deviceNodeId != null">
        device_node_id = #{deviceNodeId,jdbcType=VARCHAR},
      </if>
      <if test="deviceType != null">
        device_type = #{deviceType,jdbcType=INTEGER},
      </if>
      <if test="linkageType != null">
        linkage_type = #{linkageType,jdbcType=VARCHAR},
      </if>
      <if test="nodeType != null">
        node_type = #{nodeType,jdbcType=INTEGER},
      </if>
      <if test="rssi != null">
        rssi = #{rssi,jdbcType=INTEGER},
      </if>
      <if test="battery != null">
        battery = #{battery,jdbcType=INTEGER},
      </if>
      <if test="onlineStatus != null">
        online_status = #{onlineStatus,jdbcType=CHAR},
      </if>
      <if test="onlineTime != null">
        online_time = #{onlineTime,jdbcType=TIMESTAMP},
      </if>
      <if test="hardVersion != null">
        hard_version = #{hardVersion,jdbcType=VARCHAR},
      </if>
      <if test="softVersion != null">
        soft_version = #{softVersion,jdbcType=VARCHAR},
      </if>
      <if test="longitude != null">
        longitude = #{longitude,jdbcType=VARCHAR},
      </if>
      <if test="latitude != null">
        latitude = #{latitude,jdbcType=VARCHAR},
      </if>
      <if test="switchs != null">
        switchs = #{switchs,jdbcType=VARCHAR},
      </if>
      <if test="gateway != null">
        gateway = #{gateway,jdbcType=BIGINT},
      </if>
      <if test="bindingHouse != null">
        binding_house = #{bindingHouse,jdbcType=CHAR},
      </if>
      <if test="binding != null">
        binding = #{binding,jdbcType=CHAR},
      </if>
      <if test="warning != null">
        warning = #{warning,jdbcType=CHAR},
      </if>
      <if test="bLowerLimit != null">
        b_lower_limit = #{bLowerLimit,jdbcType=INTEGER},
      </if>
      <if test="tLowerLimit != null">
        t_lower_limit = #{tLowerLimit,jdbcType=INTEGER},
      </if>
      <if test="tUpperLimit != null">
        t_upper_limit = #{tUpperLimit,jdbcType=INTEGER},
      </if>
      <if test="qLowerLimit != null">
        q_lower_limit = #{qLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="qUpperLimit != null">
        q_upper_limit = #{qUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="lLowerLimit != null">
        l_lower_limit = #{lLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="lUpperLimit != null">
        l_upper_limit = #{lUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="eLowerLimit != null">
        e_lower_limit = #{eLowerLimit,jdbcType=INTEGER},
      </if>
      <if test="eUpperLimit != null">
        e_upper_limit = #{eUpperLimit,jdbcType=INTEGER},
      </if>
      <if test="aLowerLimit != null">
        a_lower_limit = #{aLowerLimit,jdbcType=INTEGER},
      </if>
      <if test="aUpperLimit != null">
        a_upper_limit = #{aUpperLimit,jdbcType=INTEGER},
      </if>
      <if test="mLowerLimit != null">
        m_lower_limit = #{mLowerLimit,jdbcType=INTEGER},
      </if>
      <if test="mUpperLimit != null">
        m_upper_limit = #{mUpperLimit,jdbcType=INTEGER},
      </if>
      <if test="phLowerLimit != null">
        ph_lower_limit = #{phLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="phUpperLimit != null">
        ph_upper_limit = #{phUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="co2LowerLimit != null">
        co2_lower_limit = #{co2LowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="co2UpperLimit != null">
        co2_upper_limit = #{co2UpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="sLowerLimit != null">
        s_lower_limit = #{sLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="sUpperLimit != null">
        s_upper_limit = #{sUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="wLowerLimit != null">
        w_lower_limit = #{wLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="wUpperLimit != null">
        w_upper_limit = #{wUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="rLowerLimit != null">
        r_lower_limit = #{rLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="rUpperLimit != null">
        r_upper_limit = #{rUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="delFlag != null">
        del_flag = #{delFlag,jdbcType=CHAR},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="houseId != null">
        house_id = #{houseId,jdbcType=BIGINT},
      </if>
      <if test="port != null">
        port = #{port,jdbcType=INTEGER},
      </if>
      <if test="command != null">
        command = #{command,jdbcType=VARCHAR},
      </if>
      <if test="pLowerLimit != null">
        p_lower_limit = #{pLowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="pUpperLimit != null">
        p_upper_limit = #{pUpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="p2LowerLimit != null">
        p2_lower_limit = #{p2LowerLimit,jdbcType=DECIMAL},
      </if>
      <if test="p2UpperLimit != null">
        p2_upper_limit = #{p2UpperLimit,jdbcType=DECIMAL},
      </if>
      <if test="d != null">
        d = #{d,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jorchi.mqtt.entity.SysEquipment">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update sys_equipment
    set device_name = #{deviceName,jdbcType=VARCHAR},
      device_node_id = #{deviceNodeId,jdbcType=VARCHAR},
      device_type = #{deviceType,jdbcType=INTEGER},
      linkage_type = #{linkageType,jdbcType=VARCHAR},
      node_type = #{nodeType,jdbcType=INTEGER},
      rssi = #{rssi,jdbcType=INTEGER},
      battery = #{battery,jdbcType=INTEGER},
      online_status = #{onlineStatus,jdbcType=CHAR},
      online_time = #{onlineTime,jdbcType=TIMESTAMP},
      hard_version = #{hardVersion,jdbcType=VARCHAR},
      soft_version = #{softVersion,jdbcType=VARCHAR},
      longitude = #{longitude,jdbcType=VARCHAR},
      latitude = #{latitude,jdbcType=VARCHAR},
      switchs = #{switchs,jdbcType=VARCHAR},
      gateway = #{gateway,jdbcType=BIGINT},
      binding_house = #{bindingHouse,jdbcType=CHAR},
      binding = #{binding,jdbcType=CHAR},
      warning = #{warning,jdbcType=CHAR},
      b_lower_limit = #{bLowerLimit,jdbcType=INTEGER},
      t_lower_limit = #{tLowerLimit,jdbcType=INTEGER},
      t_upper_limit = #{tUpperLimit,jdbcType=INTEGER},
      q_lower_limit = #{qLowerLimit,jdbcType=DECIMAL},
      q_upper_limit = #{qUpperLimit,jdbcType=DECIMAL},
      l_lower_limit = #{lLowerLimit,jdbcType=DECIMAL},
      l_upper_limit = #{lUpperLimit,jdbcType=DECIMAL},
      e_lower_limit = #{eLowerLimit,jdbcType=INTEGER},
      e_upper_limit = #{eUpperLimit,jdbcType=INTEGER},
      a_lower_limit = #{aLowerLimit,jdbcType=INTEGER},
      a_upper_limit = #{aUpperLimit,jdbcType=INTEGER},
      m_lower_limit = #{mLowerLimit,jdbcType=INTEGER},
      m_upper_limit = #{mUpperLimit,jdbcType=INTEGER},
      ph_lower_limit = #{phLowerLimit,jdbcType=DECIMAL},
      ph_upper_limit = #{phUpperLimit,jdbcType=DECIMAL},
      co2_lower_limit = #{co2LowerLimit,jdbcType=DECIMAL},
      co2_upper_limit = #{co2UpperLimit,jdbcType=DECIMAL},
      s_lower_limit = #{sLowerLimit,jdbcType=DECIMAL},
      s_upper_limit = #{sUpperLimit,jdbcType=DECIMAL},
      w_lower_limit = #{wLowerLimit,jdbcType=DECIMAL},
      w_upper_limit = #{wUpperLimit,jdbcType=DECIMAL},
      r_lower_limit = #{rLowerLimit,jdbcType=DECIMAL},
      r_upper_limit = #{rUpperLimit,jdbcType=DECIMAL},
      del_flag = #{delFlag,jdbcType=CHAR},
      create_by = #{createBy,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      house_id = #{houseId,jdbcType=BIGINT},
      port = #{port,jdbcType=INTEGER},
      command = #{command,jdbcType=VARCHAR},
      p_lower_limit = #{pLowerLimit,jdbcType=DECIMAL},
      p_upper_limit = #{pUpperLimit,jdbcType=DECIMAL},
      p2_lower_limit = #{p2LowerLimit,jdbcType=DECIMAL},
      p2_upper_limit = #{p2UpperLimit,jdbcType=DECIMAL},
      d = #{d,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>