<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jorchi.mqtt.mapper.SysEquipmentActionLogMapper">
  <resultMap id="BaseResultMap" type="com.jorchi.mqtt.entity.SysEquipmentActionLog">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <constructor>
      <idArg column="id" javaType="java.lang.Long" jdbcType="BIGINT" />
      <arg column="device_node_id" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="create_time" javaType="java.util.Date" jdbcType="TIMESTAMP" />
      <arg column="create_by" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="duration" javaType="java.lang.Long" jdbcType="BIGINT" />
      <arg column="port" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="is_response" javaType="java.lang.String" jdbcType="CHAR" />
      <arg column="response_time" javaType="java.util.Date" jdbcType="TIMESTAMP" />
      <arg column="order_type" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="command" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="device_id" javaType="java.lang.Long" jdbcType="BIGINT" />
      <arg column="device_type" javaType="java.lang.Integer" jdbcType="INTEGER" />
    </constructor>
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, device_node_id, create_time, create_by, duration, port, is_response, response_time, 
    order_type, command, device_id, device_type
  </sql>
  <select id="selectByExample" parameterType="com.jorchi.mqtt.entity.SysEquipmentActionLogExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sys_equipment_action_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from sys_equipment_action_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from sys_equipment_action_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.jorchi.mqtt.entity.SysEquipmentActionLogExample">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from sys_equipment_action_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.jorchi.mqtt.entity.SysEquipmentActionLog">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sys_equipment_action_log (device_node_id, create_time, create_by, 
      duration, port, is_response, 
      response_time, order_type, command, 
      device_id, device_type)
    values (#{deviceNodeId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR}, 
      #{duration,jdbcType=BIGINT}, #{port,jdbcType=INTEGER}, #{isResponse,jdbcType=CHAR}, 
      #{responseTime,jdbcType=TIMESTAMP}, #{orderType,jdbcType=INTEGER}, #{command,jdbcType=VARCHAR}, 
      #{deviceId,jdbcType=BIGINT}, #{deviceType,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.jorchi.mqtt.entity.SysEquipmentActionLog">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sys_equipment_action_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="deviceNodeId != null">
        device_node_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="duration != null">
        duration,
      </if>
      <if test="port != null">
        port,
      </if>
      <if test="isResponse != null">
        is_response,
      </if>
      <if test="responseTime != null">
        response_time,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="command != null">
        command,
      </if>
      <if test="deviceId != null">
        device_id,
      </if>
      <if test="deviceType != null">
        device_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="deviceNodeId != null">
        #{deviceNodeId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="duration != null">
        #{duration,jdbcType=BIGINT},
      </if>
      <if test="port != null">
        #{port,jdbcType=INTEGER},
      </if>
      <if test="isResponse != null">
        #{isResponse,jdbcType=CHAR},
      </if>
      <if test="responseTime != null">
        #{responseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="command != null">
        #{command,jdbcType=VARCHAR},
      </if>
      <if test="deviceId != null">
        #{deviceId,jdbcType=BIGINT},
      </if>
      <if test="deviceType != null">
        #{deviceType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.jorchi.mqtt.entity.SysEquipmentActionLogExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from sys_equipment_action_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update sys_equipment_action_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.deviceNodeId != null">
        device_node_id = #{record.deviceNodeId,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.duration != null">
        duration = #{record.duration,jdbcType=BIGINT},
      </if>
      <if test="record.port != null">
        port = #{record.port,jdbcType=INTEGER},
      </if>
      <if test="record.isResponse != null">
        is_response = #{record.isResponse,jdbcType=CHAR},
      </if>
      <if test="record.responseTime != null">
        response_time = #{record.responseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.orderType != null">
        order_type = #{record.orderType,jdbcType=INTEGER},
      </if>
      <if test="record.command != null">
        command = #{record.command,jdbcType=VARCHAR},
      </if>
      <if test="record.deviceId != null">
        device_id = #{record.deviceId,jdbcType=BIGINT},
      </if>
      <if test="record.deviceType != null">
        device_type = #{record.deviceType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update sys_equipment_action_log
    set id = #{record.id,jdbcType=BIGINT},
      device_node_id = #{record.deviceNodeId,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      create_by = #{record.createBy,jdbcType=VARCHAR},
      duration = #{record.duration,jdbcType=BIGINT},
      port = #{record.port,jdbcType=INTEGER},
      is_response = #{record.isResponse,jdbcType=CHAR},
      response_time = #{record.responseTime,jdbcType=TIMESTAMP},
      order_type = #{record.orderType,jdbcType=INTEGER},
      command = #{record.command,jdbcType=VARCHAR},
      device_id = #{record.deviceId,jdbcType=BIGINT},
      device_type = #{record.deviceType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.jorchi.mqtt.entity.SysEquipmentActionLog">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update sys_equipment_action_log
    <set>
      <if test="deviceNodeId != null">
        device_node_id = #{deviceNodeId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="duration != null">
        duration = #{duration,jdbcType=BIGINT},
      </if>
      <if test="port != null">
        port = #{port,jdbcType=INTEGER},
      </if>
      <if test="isResponse != null">
        is_response = #{isResponse,jdbcType=CHAR},
      </if>
      <if test="responseTime != null">
        response_time = #{responseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=INTEGER},
      </if>
      <if test="command != null">
        command = #{command,jdbcType=VARCHAR},
      </if>
      <if test="deviceId != null">
        device_id = #{deviceId,jdbcType=BIGINT},
      </if>
      <if test="deviceType != null">
        device_type = #{deviceType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jorchi.mqtt.entity.SysEquipmentActionLog">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update sys_equipment_action_log
    set device_node_id = #{deviceNodeId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR},
      duration = #{duration,jdbcType=BIGINT},
      port = #{port,jdbcType=INTEGER},
      is_response = #{isResponse,jdbcType=CHAR},
      response_time = #{responseTime,jdbcType=TIMESTAMP},
      order_type = #{orderType,jdbcType=INTEGER},
      command = #{command,jdbcType=VARCHAR},
      device_id = #{deviceId,jdbcType=BIGINT},
      device_type = #{deviceType,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>