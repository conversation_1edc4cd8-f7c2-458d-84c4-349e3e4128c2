<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jorchi.mqtt.mapper.SysFertilizerReportMapper">
  <resultMap id="BaseResultMap" type="com.jorchi.mqtt.entity.SysFertilizerReport">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <constructor>
      <idArg column="id" javaType="java.lang.Integer" jdbcType="INTEGER" />
      <arg column="device_id" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="upload_time" javaType="java.lang.String" jdbcType="VARCHAR" />
      <arg column="create_time" javaType="java.util.Date" jdbcType="TIMESTAMP" />
      <arg column="ec" javaType="java.math.BigDecimal" jdbcType="DECIMAL" />
      <arg column="ph" javaType="java.math.BigDecimal" jdbcType="DECIMAL" />
      <arg column="press_in" javaType="java.math.BigDecimal" jdbcType="DECIMAL" />
      <arg column="flow" javaType="java.math.BigDecimal" jdbcType="DECIMAL" />
    </constructor>
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, device_id, upload_time, create_time, ec, ph, press_in, flow
  </sql>
  <select id="selectByExample" parameterType="com.jorchi.mqtt.entity.SysFertilizerReportExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from sys_fertilizer_report
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from sys_fertilizer_report
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from sys_fertilizer_report
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.jorchi.mqtt.entity.SysFertilizerReportExample">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from sys_fertilizer_report
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.jorchi.mqtt.entity.SysFertilizerReport">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sys_fertilizer_report (device_id, upload_time, create_time, 
      ec, ph, press_in, flow
      )
    values (#{deviceId,jdbcType=VARCHAR}, #{uploadTime,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{ec,jdbcType=DECIMAL}, #{ph,jdbcType=DECIMAL}, #{pressIn,jdbcType=DECIMAL}, #{flow,jdbcType=DECIMAL}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.jorchi.mqtt.entity.SysFertilizerReport">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sys_fertilizer_report
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="deviceId != null">
        device_id,
      </if>
      <if test="uploadTime != null">
        upload_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="ec != null">
        ec,
      </if>
      <if test="ph != null">
        ph,
      </if>
      <if test="pressIn != null">
        press_in,
      </if>
      <if test="flow != null">
        flow,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="deviceId != null">
        #{deviceId,jdbcType=VARCHAR},
      </if>
      <if test="uploadTime != null">
        #{uploadTime,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ec != null">
        #{ec,jdbcType=DECIMAL},
      </if>
      <if test="ph != null">
        #{ph,jdbcType=DECIMAL},
      </if>
      <if test="pressIn != null">
        #{pressIn,jdbcType=DECIMAL},
      </if>
      <if test="flow != null">
        #{flow,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.jorchi.mqtt.entity.SysFertilizerReportExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from sys_fertilizer_report
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update sys_fertilizer_report
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.deviceId != null">
        device_id = #{record.deviceId,jdbcType=VARCHAR},
      </if>
      <if test="record.uploadTime != null">
        upload_time = #{record.uploadTime,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.ec != null">
        ec = #{record.ec,jdbcType=DECIMAL},
      </if>
      <if test="record.ph != null">
        ph = #{record.ph,jdbcType=DECIMAL},
      </if>
      <if test="record.pressIn != null">
        press_in = #{record.pressIn,jdbcType=DECIMAL},
      </if>
      <if test="record.flow != null">
        flow = #{record.flow,jdbcType=DECIMAL},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update sys_fertilizer_report
    set id = #{record.id,jdbcType=INTEGER},
      device_id = #{record.deviceId,jdbcType=VARCHAR},
      upload_time = #{record.uploadTime,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      ec = #{record.ec,jdbcType=DECIMAL},
      ph = #{record.ph,jdbcType=DECIMAL},
      press_in = #{record.pressIn,jdbcType=DECIMAL},
      flow = #{record.flow,jdbcType=DECIMAL}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.jorchi.mqtt.entity.SysFertilizerReport">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update sys_fertilizer_report
    <set>
      <if test="deviceId != null">
        device_id = #{deviceId,jdbcType=VARCHAR},
      </if>
      <if test="uploadTime != null">
        upload_time = #{uploadTime,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ec != null">
        ec = #{ec,jdbcType=DECIMAL},
      </if>
      <if test="ph != null">
        ph = #{ph,jdbcType=DECIMAL},
      </if>
      <if test="pressIn != null">
        press_in = #{pressIn,jdbcType=DECIMAL},
      </if>
      <if test="flow != null">
        flow = #{flow,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.jorchi.mqtt.entity.SysFertilizerReport">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update sys_fertilizer_report
    set device_id = #{deviceId,jdbcType=VARCHAR},
      upload_time = #{uploadTime,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      ec = #{ec,jdbcType=DECIMAL},
      ph = #{ph,jdbcType=DECIMAL},
      press_in = #{pressIn,jdbcType=DECIMAL},
      flow = #{flow,jdbcType=DECIMAL}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>