package com.jorchi.business.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jorchi.business.dao.*;
import com.jorchi.business.dto.*;
import com.jorchi.business.form.NsProductStockForm;
import com.jorchi.business.form.NsSalesOrderForm;
import com.jorchi.business.form.ScreenQueryForm;
import com.jorchi.business.po.*;
import com.jorchi.business.vo.NsProductionPlanVo;
import com.jorchi.business.vo.NsTaskVo;
import com.jorchi.common.BaseService;
import com.jorchi.common.constant.ApprovalStatus;
import com.jorchi.common.utils.CommonUtil;
import com.jorchi.common.utils.JsonUtils;
import com.jorchi.common.utils.StringUtils;
import com.jorchi.project.system.dao.SysDictDataDao;
import com.jorchi.project.system.domain.SysDept;
import com.jorchi.project.system.service.SysDeptServiceImpl;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: xubinbin
 * @Date: 2023/12/5 9:56
 * @Describe:
 */
@Service
@Slf4j
public class LargeScreenDataService extends BaseService {

    @Resource
    NsHouseDao nsHouseDao;

    @Resource
    NsCropBreedsDao nsCropBreedsDao;

    @Resource
    NsProductionPlanDao nsProductionPlanDao;

    @Resource
    private SysDictDataDao dictDataMapper;

    @Resource
    NsTaskDao nsTaskDao;

    @Autowired
    RestTemplate restTemplate;
    // 获取和风天气的key
    @Value("${spring.weatherKey}")
    String weatherKey;

    @Resource
    NsSalesOrderDao nsSalesOrderDao;

    @Resource
    NsSalesOrderReturnDao nsSalesOrderReturnDao;

    @Resource
    NsSalesOrderService nsSalesOrderService;

    @Resource
    NsCustomerService nsCustomerService;

    @Resource
    private NsCustomerDao nsCustomerDao;


    @Resource
    private NsProductStockService nsProductStockService;

    @Resource
    private SysDeptServiceImpl sysDeptService;

    /**
     * 农场概览数据
     *
     * @return
     */
    public FarmOverview getFarmOverview(Long regionDeptId) {
        FarmOverview farmOverview = new FarmOverview();
        // 大棚数据
        Map<String, Object> houseMap = nsHouseDao.selectCount(regionDeptId);
        // 大棚数量
        farmOverview.setHouseNum(Integer.parseInt(houseMap.getOrDefault("houseNum", "0").toString()));
        // 大棚面积
        BigDecimal houseArea = new BigDecimal(houseMap.getOrDefault("houseArea", "0").toString());
        // 转成亩
        houseArea = houseArea.multiply(BigDecimal.valueOf(3)).divide(BigDecimal.valueOf(2000)).setScale(2, RoundingMode.HALF_UP);
        farmOverview.setHouseArea(houseArea);
        // 蔬菜数据
        Map<String, Long> vegetablesMap = nsCropBreedsDao.selectCount(regionDeptId);
        // 蔬菜大类
        farmOverview.setVegetablesCategory(vegetablesMap.get("vegetablesCategory"));
        // 蔬菜品种
        farmOverview.setVegetablesVariety(vegetablesMap.get("vegetablesVariety"));
        // 年均产量
        List<Map<String, Object>> totalList = nsProductionPlanDao.getTotalOutput(regionDeptId);
        if (totalList != null && totalList.size() > 0) {
            BigDecimal outputTotal = new BigDecimal(0);
            for (Map<String, Object> map : totalList) {
                if (map != null) {
                    String year = map.get("year").toString();
                    if (year != null) {
                        String totalOutput = map.get("totalOutput").toString();
                        if (totalOutput != null) {
                            outputTotal = outputTotal.add(new BigDecimal(totalOutput));
                        }
                    }
                }
            }
            // 单位：吨
            farmOverview.setAvgOutput(outputTotal.divide(new BigDecimal(totalList.size() * 1000)).setScale(2, RoundingMode.HALF_UP));
        } else {
            farmOverview.setAvgOutput(new BigDecimal(0));
        }
        // 年均总值
        BigDecimal avgMoney = dictDataMapper.selectAvgMoney();
        if (avgMoney != null) {
            farmOverview.setAvgMoney(avgMoney);
        } else {
            farmOverview.setAvgMoney(new BigDecimal(0));
        }
        return farmOverview;
    }

    /**
     * 种植分布数据
     *
     * @return
     */
    public List<PlantingDistribution> getPlantingDistribution(Long regionDeptId) {
        // 查询所有种植数量
/*        Map<String,Long> vegetablesMap = nsCropBreedsDao.selectCount();
        Long total = vegetablesMap.get("vegetablesCategory");*/
        // 查询当前种植计划
        Long plantNum = nsProductionPlanDao.getPlantNum(regionDeptId);
        // 查询种植数量最多的前五种作物
        List<Map<String, Object>> cropList = nsProductionPlanDao.selectCropCount(regionDeptId);
        List<PlantingDistribution> list = new ArrayList<>();
        if (cropList != null && cropList.size() > 0) {
            // 用以计算前五的作物共占比多少
            double sum = 0.00;
            for (Map<String, Object> map : cropList) {
                PlantingDistribution plantingDistribution = new PlantingDistribution();
                // 作物名称
                plantingDistribution.setCropName(map.get("cropName").toString());
                // 作物所占比率
                long num = Long.parseLong(map.get("num").toString());
                String rate = String.format("%.2f", (((double) num / plantNum.doubleValue()) * 100));
                plantingDistribution.setProportion(Double.parseDouble(rate));
                sum = sum + Double.parseDouble(rate);
                list.add(plantingDistribution);
            }
            // 添加上“其他”
            PlantingDistribution plantingDistribution = new PlantingDistribution();
            plantingDistribution.setCropName("其他");
            String rate = String.format("%.2f", ((double) 100 - sum));
            plantingDistribution.setProportion(Double.parseDouble(rate));
            list.add(plantingDistribution);
        }
        return list;
    }

    /**
     * 查询今年各月份的产量值（单位：吨）
     *
     * @return
     */
    public List<Map<String, Object>> getProductionSchedule(Long regionDeptId) {
        // 以月份分组统计各月份产量
        return nsProductionPlanDao.getMonthOutput(regionDeptId);

    }

    /**
     * 天气预测
     *
     * @return
     */
    public List<Weather> forecastWeather(Long regionDeptId) {
        SysDept sysDept = sysDeptService.selectDeptById(regionDeptId);

        // 嘉兴地区天气预报
        String defaultLocation = "101210301";
        if (StringUtils.isNotEmpty(sysDept.getLatitude())
                && StringUtils.isNotEmpty(sysDept.getLongitude())) {
            //经度，纬度
            defaultLocation = sysDept.getLongitude() + "," + sysDept.getLatitude();
        }
        String url = CommonUtil.append("https://devapi.qweather.com/v7/weather/7d?location=" + defaultLocation + "&key=" + weatherKey);
        log.info("天气预报url:" + url);
        // 远程调用获取结果
        ResponseEntity<String> entity = restTemplate.getForEntity(url, String.class);
        // 解析
        JSONObject result = JSONObject.fromObject(entity.getBody());
        // 获取json数组
        JSONArray jsonArray = result.getJSONArray("daily");
        // 转成list对象
        return JsonUtils.jsonArrayToList(jsonArray, Weather.class);

    }

    /**
     * 获取任务列表
     *
     * @return
     */
    public TaskInfo getTaskList(NsTaskVo nsTaskVo) {
        TaskInfo taskInfo = new TaskInfo();
        // 查询今日任务量以及完成信息
        Map<String, Long> map = nsTaskDao.getTodayTask(nsTaskVo.getRegionDeptId());
        taskInfo.setTaskNum(map.get("taskNum"));
        taskInfo.setHasDone(map.get("hasDone"));

        // 查询计划列表
        PageHelper.startPage(nsTaskVo.getPageNum(), nsTaskVo.getPageSize());
        List<NsProductionPlanVo> planList = nsProductionPlanDao.selectPlanList(nsTaskVo.getRegionDeptId());
        if (planList != null && !planList.isEmpty()) {
            for (NsProductionPlanVo nsProductionPlanVo : planList) {
                // 获取该计划的总任务数和已完成任务数
                Map<String, Long> taskMap = nsTaskDao.selectPlanTask(nsProductionPlanVo.getId());
                // 总任务数
                Long num1 = taskMap.get("num1");
                if (num1 == null || num1 == 0L) {
                    nsProductionPlanVo.setCurrent("0.00%");
                    continue;
                }
                // 已完成任务数
                Long num2 = taskMap.get("num2");
                nsProductionPlanVo.setCurrent(String.format("%.2f", (((double) num2 / num1) * 100)) + "%");
            }
            taskInfo.setTotal(planList.size());
        }
        taskInfo.setTaskList(planList);
        return taskInfo;
    }

    /**
     * 大棚状态统计
     *
     * @return
     */
    public List<Map<String, Object>> getHouseStatus(Long regionDeptId) {
        List<Map<String, Object>> list = nsHouseDao.selectHouseStatics(regionDeptId);
        if (list != null && list.size() > 0) {
            for (Map<String, Object> map : list) {
                // 面积转成亩
                BigDecimal area = new BigDecimal(map.get("area").toString());
                area = area2mu(area);
                map.put("area", area);
            }
        }
        return list;

    }

    private BigDecimal area2mu(BigDecimal area) {
        return area.multiply(BigDecimal.valueOf(3)).divide(BigDecimal.valueOf(2000)).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 获取大棚当前状态
     *
     * @return
     */
    public HouseCurrentStatus getHouseCurrentStatus(NsHouse nsHouse) {
        // 大棚id
        Long houseId = nsHouse.getHouseId();

        NsProductionPlan nsProductionPlan = nsProductionPlanDao.selectStatusByHouseId(houseId);
        if (nsProductionPlan != null) {
            // 有生产种植计划
            // 地块位置
            String address = CommonUtil.append(nsProductionPlan.getPlantName(), "-", nsProductionPlan.getHouseName());
            // 面积（单位：亩）
            BigDecimal area = new BigDecimal(nsProductionPlan.getHouseArea());
            area = area.multiply(BigDecimal.valueOf(3)).divide(BigDecimal.valueOf(2000)).setScale(2, RoundingMode.HALF_UP);
            // 亩产
            if (nsProductionPlan.getEstimatedTotalOutput() == null) {
                nsProductionPlan.setEstimatedTotalOutput("0");
            }
            BigDecimal output = new BigDecimal(nsProductionPlan.getEstimatedTotalOutput()).divide(area, RoundingMode.CEILING);
            return HouseCurrentStatus.builder()
                    .region(address)
                    .area(area)
                    .cropName(nsProductionPlan.getCropName())
                    .cropId(nsProductionPlan.getCropId())
                    .breeds(nsProductionPlan.getBreeds())
                    .planDate(nsProductionPlan.getPlantDate())
                    .harvestDate(nsProductionPlan.getHarvestDate())
                    .output(output)
                    .build();
        } else {
            // 当前无生产计划
            NsHouse bean = nsHouseDao.selectByPrimaryKey(houseId);
            // 地块位置
            String address = CommonUtil.append(bean.getHouseRegionName(), "-", bean.getHouseName());
            // 面积（单位：亩）
            BigDecimal area = new BigDecimal(bean.getHouseArea());
            area = area.multiply(BigDecimal.valueOf(3)).divide(BigDecimal.valueOf(2000)).setScale(2, RoundingMode.HALF_UP);
            return HouseCurrentStatus.builder()
                    .region(address)
                    .area(area)
                    .build();

        }

    }

    /**
     * 更新生产计划状态
     *
     * @return
     */
    public Boolean updatePlanStatus() {
        // 查找计划周期状态值为空的任务id
        List<Long> list = nsProductionPlanDao.selectNull();
        for (Long id : list) {
            // 查看该计划下是否还有任务
            Integer num = nsTaskDao.selectTasks(id);
            if (num != null && num > 0) {
                // 未完成
                nsProductionPlanDao.updateTaskStatusNone(id);
            } else {
                // 已完成
                nsProductionPlanDao.updateTaskStatusDone(id);
            }
        }
        return true;
    }


    /**
     * 将种植项中的context字段赋值到任务表中的描述中
     *
     * @return
     */
    public String assignment() {
        // 查找所有的计划id
        List<Long> ids = nsProductionPlanDao.selectAllPlan();
        // 遍历计划id,找到每个计划中的种植项
        for (Long id : ids) {
            List<NsPlantingClausePlan> list = nsProductionPlanDao.selectPlanClauseList(id);
            if (list != null && list.size() > 0) {
                for (NsPlantingClausePlan nsPlantingClausePlan : list) {
                    // 更新计划表中的相关信息
                    nsTaskDao.updateDescription(id, nsPlantingClausePlan.getFarmingProcessId(), nsPlantingClausePlan.getContext());
                }
            }
        }
        return null;
    }

    /**
     * 分页获取订单列表
     */

    public Page<CockpitOrderDto> pageOrder(ScreenQueryForm pageDomain) {

        NsSalesOrderForm form = new NsSalesOrderForm();
        form.setPageNum(pageDomain.getPageNum());
        form.setPageSize(pageDomain.getPageSize());
        form.setApplicationStatus(ApprovalStatus.APPLY_PASS.getStatus());
        form.setCreateTimeFrom(pageDomain.getStartDate());
        form.setCreateTimeTo(pageDomain.getEndDate());
        form.setRegionDeptId(pageDomain.getRegionDeptId());
        //分页查询订单
        List<NsSalesOrder> nsSalesOrders = nsSalesOrderService.listPage(form);

        if (CollectionUtils.isEmpty(nsSalesOrders)) {
            return new Page<>();
        }
        List<NsCustomer> byIds = nsCustomerService
                .findByIds(nsSalesOrders
                        .stream()
                        .map(NsSalesOrder::getCustomerId)
                        .collect(Collectors.toList()));
        //查询客户信息
        Map<Long, NsCustomer> map = byIds.stream().collect(Collectors.toMap(NsCustomer::getCustomerId, Function.identity()));

        // 封装成dto
        List<CockpitOrderDto> cockpitOrderDtos = nsSalesOrders.stream().map(e -> {
            CockpitOrderDto cockpitOrderDto = new CockpitOrderDto();
            NsCustomer nsCustomer = map.get(e.getCustomerId());

            if (nsCustomer != null) {
                cockpitOrderDto.setContact(nsCustomer.getContactPerson());
                cockpitOrderDto.setMobile(nsCustomer.getContactPhone());
                cockpitOrderDto.setCustomerName(nsCustomer.getCustomerName());
            }
            cockpitOrderDto.setOrderNo(e.getSalesOrderCode());
            cockpitOrderDto.setCreateTime(e.getCreateTime());
            return cockpitOrderDto;
        }).collect(Collectors.toList());
        Page<CockpitOrderDto> page = new Page<>();
        page.setPageNum(pageDomain.getPageNum());
        page.setPageSize(pageDomain.getPageSize());
        page.setOrderBy(pageDomain.getOrderBy());
        page.addAll(cockpitOrderDtos);
        page.setTotal(new PageInfo<>(nsSalesOrders).getTotal());
        return page;
    }

    /**
     * 查询销售概览
     *
     * @param form
     * @return
     */
    public SalesOverviewDto getSalesOverview(ScreenQueryForm form) {
        Long regionDeptId = form.getRegionDeptId();
        // 获取订单总数
        Integer orderNum = nsSalesOrderDao.selectOrderNum(form.getStartDate(), form.getEndDate(), regionDeptId);
        // 获取客户总数
        Integer customerNum = nsCustomerDao.selectCustomerNum(regionDeptId);
        // 获取客户城市总数
        Integer customerCityNum = nsSalesOrderDao.selectOrderCityNum(form.getStartDate(), form.getEndDate(), regionDeptId);
        // 获取订单退货总数
        Integer orderReturnNum = nsSalesOrderReturnDao.selectOrderReturnNum(form.getStartDate(), form.getEndDate(),
                regionDeptId);
        // 获取订单总金额
        BigDecimal orderTotal = nsSalesOrderDao.selectOrderTotal(form.getStartDate(), form.getEndDate(), regionDeptId);
        if (orderTotal == null) {
            orderTotal = BigDecimal.ZERO;
        }
        // 从元换算成万
        orderTotal = orderTotal.divide(BigDecimal.valueOf(10000), 2, RoundingMode.HALF_UP);
        SalesOverviewDto salesOverviewDto = new SalesOverviewDto();
        salesOverviewDto.setOrderNum(orderNum);
        salesOverviewDto.setCustomerNum(customerNum);
        salesOverviewDto.setCustomerCityNum(customerCityNum);
        salesOverviewDto.setOrderReturnNum(orderReturnNum);
        salesOverviewDto.setOrderTotal(orderTotal);
        return salesOverviewDto;
    }

    /**
     * 查询各个城市订单数量
     *
     * @param form
     * @return
     */
    public List<CityOrderNumDto> queryCityOrderNum(ScreenQueryForm form) {
        return nsSalesOrderDao.selectCityOrderNum(form.getStartDate(), form.getEndDate(),
                form.getRegionDeptId());
    }

    /**
     * 查询订单农产品价格趋势
     *
     * @param form
     * @return
     */
    public List<PriceTrendDto> queryPriceTrend(ScreenQueryForm form) {
        return nsSalesOrderDao.selectPriceTrend(form.getStartDate(), form.getEndDate(),
                form.getRegionDeptId());
    }

    /**
     * 查询一段时间内的农产品销量排行
     *
     * @param form
     * @return
     */
    public List<SalesRankDto> querySalesRank(ScreenQueryForm form) {
        List<SalesOrderItemDto> salesOrderItemDtos = nsSalesOrderDao
                .selectByDateRange(form.getStartDate(), form.getEndDate(), form.getRegionDeptId());

        if (CollectionUtils.isEmpty(salesOrderItemDtos)) {
            return Collections.emptyList();
        }
        Map<Long, SalesRankDto> map = new HashMap<>();

        salesOrderItemDtos
                .stream()
                .collect(Collectors.groupingBy(SalesOrderItemDto::getProductStockId))
                .forEach((k, v) -> {
                    SalesRankDto salesRankDto = map.get(k);
                    // 判断是否为空
                    if (salesRankDto == null) {
                        salesRankDto = new SalesRankDto();
                        salesRankDto.setProductStockId(k);
                        salesRankDto.setCropName(v.get(0).getCropName());
                        salesRankDto.setWeigh(BigDecimal.ZERO);
                        map.put(k, salesRankDto);
                    }
                    // 单位换算
                    for (SalesOrderItemDto salesOrderItemDto : v) {
                        if (salesOrderItemDto.getUnit().contains("KG")) {
                            salesRankDto
                                    .setWeigh(salesRankDto.getWeigh().add(kg2ton(salesOrderItemDto.getQuantity())));
                        } else if (salesOrderItemDto.getUnit().contains("TON")) {
                            salesRankDto
                                    .setWeigh(salesRankDto.getWeigh().add(salesOrderItemDto.getQuantity()));
                        }
                    }
                });
        return new ArrayList<>(map.values());
    }

    /**
     * 将KG转换为吨
     *
     * @param kg
     * @return
     */
    private BigDecimal kg2ton(BigDecimal kg) {
        if (kg == null) {
            return BigDecimal.ZERO;
        }
        return kg.divide(BigDecimal.valueOf(1000), 2, RoundingMode.HALF_UP);
    }

    /**
     * 查询农产品库存
     *
     * @return
     */
    public List<ProductStockDto> queryProductStock(Long regionDeptId) {
        NsProductStockForm nsProductStockForm = new NsProductStockForm();
        nsProductStockForm.setPageSize(7);
        nsProductStockForm.setPageNum(1);
        nsProductStockForm.setOrderByColumn("stock_quantity");
        nsProductStockForm.setIsAsc("desc");
        nsProductStockForm.setRegionDeptId(regionDeptId);
        List<NsProductStock> nsProductStocks = nsProductStockService.listPage(nsProductStockForm);

        if (CollectionUtils.isEmpty(nsProductStocks)) {
            return null;
        }
        List<Long> productStockIds = nsProductStocks
                .stream()
                .map(NsProductStock::getProductStockId)
                .collect(Collectors.toList());

        //转换到dto
        List<ProductStockDto> productStockDtos = nsProductStocks.stream().map(e -> {
            ProductStockDto productStockDto = new ProductStockDto();
            productStockDto.setProductStockId(e.getProductStockId());
            productStockDto.setCropName(e.getCropName());
            productStockDto.setQuantity(e.getStockQuantity());
            productStockDto.setWarningStatus(e.getWarningStatus());
            return productStockDto;
        }).collect(Collectors.toList());
        //填充种植面积
        //查询出所有种子
        List<NsCropBreeds> nsCropBreeds = nsCropBreedsDao
                .selectByProductStockIds(productStockIds);

        if (CollectionUtils.isEmpty(nsCropBreeds)) {
            return null;
        }
        List<Long> cropIds = nsCropBreeds
                .stream()
                .map(NsCropBreeds::getCropId)
                .collect(Collectors.toList());
        //查出所有在种植的生产计划
        List<NsProductionPlan> nsProductionPlans = nsProductionPlanDao
                .selectPlantingCrops(cropIds);

        fillHouseArea(nsProductionPlans, productStockDtos, nsCropBreeds);

        return productStockDtos;
    }

    /**
     * 填充种植面积
     *
     * @param nsProductionPlans 生产计划
     * @param productStockDtos  农产品库存
     * @param nsCropBreeds      种子
     */
    private void fillHouseArea(List<NsProductionPlan> nsProductionPlans, List<ProductStockDto> productStockDtos,
                               List<NsCropBreeds> nsCropBreeds) {
        if (CollectionUtils.isEmpty(nsProductionPlans)) {
            return;
        }
        // 将农产品库存和种子关联起来
        Map<Long, ProductStockDto> stockDtoMap = productStockDtos
                .stream()
                .collect(Collectors.toMap(ProductStockDto::getProductStockId, e -> e));

        Map<Long, NsCropBreeds> cropBreedsMap = nsCropBreeds
                .stream()
                .collect(Collectors.toMap(NsCropBreeds::getCropId, e -> e));

        // 将生产计划中的种植面积累加到农产品库存中
        for (NsProductionPlan nsProductionPlan : nsProductionPlans) {
            BigDecimal decimal = silent2BigDecimal(nsProductionPlan.getHouseArea());

            if (decimal.compareTo(BigDecimal.ZERO) > 0) {
                NsCropBreeds crop = cropBreedsMap.get(nsProductionPlan.getCropId());

                if (crop != null) {
                    ProductStockDto productStockDto = stockDtoMap.get(crop.getProductStockId());

                    if (productStockDto != null) {
                        BigDecimal plantingArea = productStockDto.getPlantingArea();

                        if (plantingArea != null) {
                            productStockDto.setPlantingArea(plantingArea.add(decimal));
                        } else {
                            //平方米转换成亩
                            productStockDto.setPlantingArea(area2mu(decimal));
                        }
                    }
                }
            }
        }
    }

    private static BigDecimal silent2BigDecimal(String s) {
        if (s == null || s.trim().isEmpty()) {
            return BigDecimal.ZERO;
        }
        try {
            return new BigDecimal(s);
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }

    }

}
