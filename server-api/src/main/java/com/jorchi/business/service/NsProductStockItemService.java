package com.jorchi.business.service;

import com.github.pagehelper.PageHelper;
import com.jorchi.business.dao.NsProductStockItemDao;
import com.jorchi.business.form.NsProductStockItemForm;
import com.jorchi.business.po.NsProductStockItem;
import com.jorchi.common.BaseService;
import com.jorchi.common.constant.CommonDef;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import pdfc.claim.common.ClientException;
import pdfc.claim.common.CommonUtil;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 表ns_product_stock_item的服务层对象<br/>
 * 对应表名：ns_product_stock_item，表备注：农产品库存项目表
 *
 * @author: 周建宇 at jorchi
 * @date: 2025-06-14 16:04:47
 */
@Slf4j
@Service
public class NsProductStockItemService extends BaseService {

    @Resource
    NsProductStockItemDao nsProductStockItemDao;

    @Resource
    MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询（农产品库存项目表）<br>
     *
     * @author: 周建宇 at jorchi
     * @date: 2025-06-14 16:04:47
     */
    public NsProductStockItem findById(Long primaryKey) {
        return nsProductStockItemDao.selectByPrimaryKey(primaryKey);
    }

    /**
     * 按名称查询（农产品库存项目表）<br>
     *
     * @author: 周建宇 at jorchi
     * @date: 2025-06-14 16:04:47
     */
    public NsProductStockItem findByName(String name) {
        // TODO : 设置查询条件：名称，删除标志位等
        NsProductStockItem query = NsProductStockItem.builder().cropName(name)
                .deleted(CommonDef.DELETE_FLAG_NORMAL)
                .build();

        // 查询
        List<NsProductStockItem> result = nsProductStockItemDao.selectPage(query);
        if (result == null || result.isEmpty())
            return null;
        return result.get(0);
    }

    /**
     * 分页查询（农产品库存项目表）<br>
     *
     * @author: 周建宇 at jorchi
     * @date: 2025-06-14 16:04:47
     */
    public List<NsProductStockItem> listPage(NsProductStockItemForm nsProductStockItemForm) {
        if (log.isDebugEnabled())
            log.debug(CommonUtil.append("listPage, nsProductStockItemForm:", nsProductStockItemForm));

        // 设置分页参数
        PageHelper.startPage(nsProductStockItemForm.getPageNum(), nsProductStockItemForm.getPageSize());
        return nsProductStockItemDao.selectPageByForm(nsProductStockItemForm);
    }

    /**
     * 删除（农产品库存项目表）<br>
     * TODO : 请根据实际情况更新为删除标志位或物理删除！！
     *
     * @author: 周建宇 at jorchi
     * @date: 2025-06-14 16:04:47
     */
    public boolean deleteNsProductStockItem(Long id) {
        if (id == null)
            throw ClientException.of("删除时主键不能为空！");

        // 物理删除
        // return nsProductStockItemDao.deleteByPrimaryKey(id) == 1 ? true : false;

        // 仅更新删除标志位
        NsProductStockItem nsProductStockItem = NsProductStockItem.builder()
                .itemId(id)
                .deleted(CommonDef.DELETE_FLAG_DELETE)
                // .status(status)
                .build();

        // 更新数据
        int res = nsProductStockItemDao.updateSelectiveByPrimaryKey(nsProductStockItem);
        return res == 1 ? true : false;
    }

    /**
     * 新增或保存（农产品库存项目表）<br>
     * ID 为空即为新增，否则为更新
     *
     * @author: 周建宇 at jorchi
     * @date: 2025-06-14 16:04:47
     */
    public NsProductStockItem saveNsProductStockItem(NsProductStockItem nsProductStockItem) {
        Long primaryKey = nsProductStockItem.getItemId();
        if (primaryKey == null || primaryKey == 0) { // do insert
            // 生成主键
            nsProductStockItem.setItemId(maxIdService.getAndIncrement("NsProductStockItem"));

            // set CreateTime
            nsProductStockItem.setCreateTime(new Date());
            nsProductStockItem.setDeleted(CommonDef.DELETE_FLAG_NORMAL);

            // do insert by not null properties
            nsProductStockItemDao.insertSelective(nsProductStockItem);
            return nsProductStockItem;
        } else { // do update

            // set UpdateTime
            nsProductStockItem.setUpdateTime(new Date());
            nsProductStockItem.setDeleted(CommonDef.DELETE_FLAG_NORMAL);
            // do update by not null properties
            int count = nsProductStockItemDao.updateSelectiveByPrimaryKey(nsProductStockItem);
            if (count <= 0)
                nsProductStockItemDao.insertSelective(nsProductStockItem);
            return nsProductStockItem;
        }
    }

    public List<NsProductStockItem> getByProductStockId(Long productStockId) {
        NsProductStockItemForm form = new NsProductStockItemForm();
        form.setProductStockId(productStockId);
        form.setDeleted(CommonDef.DELETE_FLAG_NORMAL);
        return nsProductStockItemDao.selectPageByForm(form);
    }

    //计算农产品总重量
    public BigDecimal calculateWeight(Long productStockId) {
        List<NsProductStockItem> byProductStockId = getByProductStockId(productStockId);

        if (byProductStockId.isEmpty()) {

            return BigDecimal.ZERO;
        }
        return byProductStockId
                .stream()
                .map(NsProductStockItem::getWeight)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public void saveBatch(List<NsProductStockItem> nsProductStockItems, Long productStockId) {


        //找到相同规格的库存项目进行累加，不存在就插入

        List<NsProductStockItem> exists = getByProductStockId(productStockId);


        for (NsProductStockItem exist : exists) {
            List<NsProductStockItem> itemToAdd = nsProductStockItems
                    .stream().filter(e -> e.getSpec().equals(exist.getSpec())).collect(Collectors.toList());

            //重量累加
            exist.setNum(exist.getNum().add(itemToAdd.stream().map(NsProductStockItem::getNum).reduce(BigDecimal.ZERO, BigDecimal::add)));
            exist.setWeight(exist.getWeight().add(itemToAdd.stream().map(NsProductStockItem::getWeight).reduce(BigDecimal.ZERO, BigDecimal::add)));
            nsProductStockItems
                    .removeAll(itemToAdd);

            if (exist.getWeight().compareTo(BigDecimal.ZERO) < 0) {
                throw ClientException.of("库存不足，无法继续操作");
            }
            nsProductStockItemDao.updateByPrimaryKey(exist);
        }

        //对于不存在的新建
        nsProductStockItems.forEach(e -> {
            e.setItemId(maxIdService.getAndIncrement("NsProductStockItem"));
            e.setCreateTime(new Date());
            e.setDeleted(CommonDef.DELETE_FLAG_NORMAL);

            if (e.getWeight().compareTo(BigDecimal.ZERO) < 0) {
                throw ClientException.of("库存不足，无法继续操作");
            }
        });
        if (!nsProductStockItems.isEmpty()) {
            nsProductStockItemDao.saveBatch(nsProductStockItems);
        }
    }

    /**
     * 重名检查(请按需使用)（农产品库存项目表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-06-14 16:04:47
    public void checkNameExisted(NsProductStockItem nsProductStockItem) {
    // 设置查询条件：名称，删除标志位等
    NsProductStockItem query = NsProductStockItem.builder().name(nsProductStockItem.getName()).build();
    List<NsProductStockItem> result = nsProductStockItemDao.selectPage(query);
    if (result == null || result.isEmpty())
    return;

    // 新增或主键不同
    if (nsProductStockItem.getItemId() == null || !result.get(0).getItemId().equals(nsProductStockItem.getItemId()))
    throw ClientException.of("该名称已存在！Code:400-", nsProductStockItem.getName(), "-k-", result.get(0).getItemId());
    }*/

}
