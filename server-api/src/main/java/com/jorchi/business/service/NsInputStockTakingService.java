package com.jorchi.business.service;

import com.github.pagehelper.PageHelper;
import com.jorchi.business.dao.*;
import com.jorchi.business.dto.InputTypeAndIdDto;
import com.jorchi.business.form.NsInputStockTakingForm;
import com.jorchi.business.form.SaveInputStockTakingForm;
import com.jorchi.business.po.*;
import com.jorchi.common.BaseService;
import com.jorchi.common.constant.CommonDef;
import com.jorchi.common.utils.SecurityUtils;
import com.jorchi.framework.security.LoginUser;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import pdfc.claim.common.ClientException;
import pdfc.claim.common.CommonUtil;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 表ns_input_stock_taking的服务层对象<br/>
 * 对应表名：ns_input_stock_taking，表备注：投入品库存盘点表
 *
 * @author: 周建宇 at jorchi
 * @date: 2025-01-09 09:35:47
 */
@Slf4j
@Service
public class NsInputStockTakingService extends BaseService {

    @Resource
    NsInputStockTakingDao nsInputStockTakingDao;

    @Resource
    MaxIdServiceImpl maxIdService;
    @Autowired
    private NsWarehouseDao nsWarehouseDao;
    @Autowired
    private NsInputStockService nsInputStockService;
    @Resource
    private NsFertilizerDao nsFertilizerDao;
    @Resource
    private NsAgriculturalMaterialsDao agriculturalMaterialsDao;
    @Resource
    private NsFarmFilmDao nsFarmFilmDao;
    @Resource
    private NsInputOtherDao nsInputOtherDao;
    @Resource
    private NsCropBreedsDao nsCropBreedsDao;

    /**
     * 按主键查询（投入品库存盘点表）<br>
     *
     * @author: 周建宇 at jorchi
     * @date: 2025-01-09 09:35:47
     */
    public NsInputStockTaking findById(Long primaryKey) {
        return nsInputStockTakingDao.selectByPrimaryKey(primaryKey);
    }

    /**
     * 按名称查询（投入品库存盘点表）<br>
     *
     * @author: 周建宇 at jorchi
     * @date: 2025-01-09 09:35:47
     */
    public NsInputStockTaking findByName(String name) {
        // TODO : 设置查询条件：名称，删除标志位等
        NsInputStockTaking query = NsInputStockTaking.builder().inputName(name)
                .deleted(CommonDef.DELETE_FLAG_NORMAL)
                .build();

        // 查询
        List<NsInputStockTaking> result = nsInputStockTakingDao.selectPage(query);
        if (result == null || result.isEmpty())
            return null;
        return result.get(0);
    }

    /**
     * 分页查询（投入品库存盘点表）<br>
     *
     * @author: 周建宇 at jorchi
     * @date: 2025-01-09 09:35:47
     */
    public List<NsInputStockTaking> listPage(NsInputStockTakingForm nsInputStockTakingForm) {
        if (log.isDebugEnabled())
            log.debug(CommonUtil.append("listPage, nsInputStockTakingForm:", nsInputStockTakingForm));

        // 设置分页参数
        PageHelper.startPage(nsInputStockTakingForm.getPageNum(), nsInputStockTakingForm.getPageSize(), "create_time desc");
        return nsInputStockTakingDao.selectPageByForm(nsInputStockTakingForm);
    }

    /**
     * 删除（投入品库存盘点表）<br>
     * TODO : 请根据实际情况更新为删除标志位或物理删除！！
     *
     * @author: 周建宇 at jorchi
     * @date: 2025-01-09 09:35:47
     */
    public boolean deleteNsInputStockTaking(Long id) {
        if (id == null)
            throw ClientException.of("删除时主键不能为空！");

        // 物理删除
        // return nsInputStockTakingDao.deleteByPrimaryKey(id) == 1 ? true : false;

        // 仅更新删除标志位
        NsInputStockTaking nsInputStockTaking = NsInputStockTaking.builder()
                .takingId(id)
                .deleted(CommonDef.DELETE_FLAG_DELETE)
                // .status(status)
                .build();

        // 更新数据
        int res = nsInputStockTakingDao.updateSelectiveByPrimaryKey(nsInputStockTaking);
        return res == 1 ? true : false;
    }



    /**
     * 根据盘点投入品获取投入品类型和对应ID列表
     *
     * @param nsInputStockTakings 盘点列表
     * @return
     */
    private List<InputTypeAndIdDto> getInputTypeAndIdDtos(List<NsInputStockTaking> nsInputStockTakings) {
        List<InputTypeAndIdDto> inputTypeAndIdDtos = new ArrayList<>();
        nsInputStockTakings
                .stream()
                .collect(Collectors.groupingBy(NsInputStockTaking::getInputType))
                .forEach((k, v) -> {
                    inputTypeAndIdDtos.add(InputTypeAndIdDto.builder()
                            .type(k)
                            .businessIds(v.stream().map(NsInputStockTaking::getBusinessId).collect(Collectors.toList()))
                            .build());
                });
        return inputTypeAndIdDtos;
    }

    /**
     * 新增或保存（投入品库存盘点表）<br>
     * ID 为空即为新增，否则为更新
     *
     * @author: 周建宇 at jorchi
     * @date: 2025-01-09 09:35:47
     */
    public NsInputStockTaking saveNsInputStockTaking(NsInputStockTaking nsInputStockTaking) {
        Long primaryKey = nsInputStockTaking.getTakingId();
        if (primaryKey == null || primaryKey == 0) { // do insert
            // 生成主键
            nsInputStockTaking.setTakingId(maxIdService.getAndIncrement("NsInputStockTaking"));

            // set CreateTime
            nsInputStockTaking.setCreateTime(new Date());
            nsInputStockTaking.setDeleted(CommonDef.DELETE_FLAG_NORMAL);

            // do insert by not null properties
            nsInputStockTakingDao.insertSelective(nsInputStockTaking);
            return nsInputStockTaking;
        } else { // do update

            // set UpdateTime
            nsInputStockTaking.setUpdateTime(new Date());
            nsInputStockTaking.setDeleted(CommonDef.DELETE_FLAG_NORMAL);
            // do update by not null properties
            int count = nsInputStockTakingDao.updateSelectiveByPrimaryKey(nsInputStockTaking);
            if (count <= 0)
                nsInputStockTakingDao.insertSelective(nsInputStockTaking);
            return nsInputStockTaking;
        }
    }

    /**
     * 重名检查(请按需使用)（投入品库存盘点表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-09 09:35:47
    public void checkNameExisted(NsInputStockTaking nsInputStockTaking) {
    // 设置查询条件：名称，删除标志位等
    NsInputStockTaking query = NsInputStockTaking.builder().name(nsInputStockTaking.getName()).build();
    List<NsInputStockTaking> result = nsInputStockTakingDao.selectPage(query);
    if (result == null || result.isEmpty())
    return;

    // 新增或主键不同
    if (nsInputStockTaking.getTakingId() == null || !result.get(0).getTakingId().equals(nsInputStockTaking.getTakingId()))
    throw ClientException.of("该名称已存在！Code:400-", nsInputStockTaking.getName(), "-k-", result.get(0).getTakingId());
    }*/

    /**
     * 库存盘点保存方法，支持完全更新库存
     * @param form 盘点保存表单
     * @author: 周建宇 at jorchi
     * @date: 2025-09-21
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveInputStockTaking(SaveInputStockTakingForm.InputStockTakingForm form) {
        LoginUser loginUser = SecurityUtils.getLoginUser();

        // 解析盘点日期
        Date takingDate;
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            takingDate = sdf.parse(form.getTakingDate());
        } catch (ParseException e) {
            throw ClientException.of("盘点日期格式错误，请使用 yyyy-MM-dd 格式！");
        }

        // 获取仓库信息
        NsWarehouse warehouse = nsWarehouseDao.selectByPrimaryKey(form.getHouseId());
        if (warehouse == null) {
            throw ClientException.of("仓库信息不存在！");
        }

        // 处理库存项目盘点，完全更新库存
        List<NsInputStock> stocksToUpdate = new ArrayList<>();
        List<NsInputStock> stocksToInsert = new ArrayList<>();
        List<NsInputStockTaking> takingRecords = new ArrayList<>();

        // 获取现有库存项目（按业务ID和投入品类型分组）
        List<InputTypeAndIdDto> inputTypeAndIdDtos = getInputTypeAndIdDtosFromForm(form.getStockItems());
        List<NsInputStock> existingStocks = nsInputStockService.selectByBusinessIds(inputTypeAndIdDtos);

        // 创建现有库存的映射 (inputType_businessId_spec -> NsInputStock)
        Map<String, NsInputStock> existingStockMap = existingStocks.stream()
                .collect(Collectors.toMap(
                    stock -> stock.getInputType() + "_" + stock.getBusinessId() + "_" + (stock.getSpec() != null ? stock.getSpec() : ""),
                    stock -> stock,
                    (existing, replacement) -> existing
                ));

        // 处理前端提交的库存项目
        for (SaveInputStockTakingForm.StockItemForm stockItemForm : form.getStockItems()) {
            String key = stockItemForm.getInputType() + "_" + stockItemForm.getBusinessId() + "_" +
                        (stockItemForm.getSpec() != null ? stockItemForm.getSpec() : "");

            NsInputStock existingStock = existingStockMap.get(key);

            if (existingStock != null) {
                // 更新现有库存
                BigDecimal originalQuantity = existingStock.getStockQuantity() != null ? existingStock.getStockQuantity() : BigDecimal.ZERO;
                existingStock.setStockQuantity(stockItemForm.getTakingStockQuantity());
                existingStock.setUpdateBy(loginUser.getUser().getUserId());
                existingStock.setUpdateTime(new Date());

                // 更新预警状态
                updateWarningStatus(existingStock);
                stocksToUpdate.add(existingStock);

                // 创建盘点记录
                NsInputStockTaking takingRecord = createTakingRecord(stockItemForm, originalQuantity,
                    form, warehouse, loginUser, takingDate, existingStock);
                takingRecords.add(takingRecord);

            } else if (stockItemForm.getInputStockId() == null && stockItemForm.getTakingStockQuantity().compareTo(BigDecimal.ZERO) > 0) {
                // 新增库存项目（只有当盘点数量大于0时才新增）
                NsInputStock newStock = createNewInputStock(stockItemForm, form, loginUser);
                stocksToInsert.add(newStock);

                // 创建盘点记录
                NsInputStockTaking takingRecord = createTakingRecord(stockItemForm, BigDecimal.ZERO,
                    form, warehouse, loginUser, takingDate, newStock);
                takingRecords.add(takingRecord);
            }
        }

        // 执行数据库操作
        // 1. 更新现有库存
        for (NsInputStock stock : stocksToUpdate) {
            nsInputStockService.saveNsInputStock(stock);
        }

        // 2. 新增库存项目
        for (NsInputStock stock : stocksToInsert) {
            nsInputStockService.saveNsInputStock(stock);
        }

        // 3. 保存盘点记录
        if (!takingRecords.isEmpty()) {
            nsInputStockTakingDao.saveBatch(takingRecords);
        }

        log.info("库存盘点完成，更新库存项目：{}个，新增库存项目：{}个，生成盘点记录：{}个",
                stocksToUpdate.size(), stocksToInsert.size(), takingRecords.size());
    }

    /**
     * 从表单中获取投入品类型和ID列表
     */
    private List<InputTypeAndIdDto> getInputTypeAndIdDtosFromForm(List<SaveInputStockTakingForm.StockItemForm> stockItems) {
        List<InputTypeAndIdDto> inputTypeAndIdDtos = new ArrayList<>();
        stockItems.stream()
                .collect(Collectors.groupingBy(SaveInputStockTakingForm.StockItemForm::getInputType))
                .forEach((k, v) -> {
                    inputTypeAndIdDtos.add(InputTypeAndIdDto.builder()
                            .type(k)
                            .businessIds(v.stream().map(SaveInputStockTakingForm.StockItemForm::getBusinessId).collect(Collectors.toList()))
                            .build());
                });
        return inputTypeAndIdDtos;
    }

    /**
     * 更新库存预警状态
     */
    private void updateWarningStatus(NsInputStock stock) {
        if (stock.getStockWarningLine() != null && stock.getStockQuantity() != null) {
            if (stock.getStockQuantity().compareTo(stock.getStockWarningLine()) < 0) {
                stock.setWarningStatus("预警");
            } else {
                stock.setWarningStatus("正常");
            }
        } else {
            stock.setWarningStatus("正常");
        }
    }

    /**
     * 创建新的投入品库存
     */
    private NsInputStock createNewInputStock(SaveInputStockTakingForm.StockItemForm stockItemForm,
                                           SaveInputStockTakingForm.InputStockTakingForm form,
                                           LoginUser loginUser) {
        NsInputStock newStock = new NsInputStock();
        newStock.setBusinessId(stockItemForm.getBusinessId());
        newStock.setInputType(stockItemForm.getInputType());
        newStock.setSpec(stockItemForm.getSpec());
        newStock.setStockQuantity(stockItemForm.getTakingStockQuantity());
        newStock.setRegionDeptId(Long.valueOf(form.getRegionDeptId()));
        newStock.setCreateBy(loginUser.getUser().getUserId());
        newStock.setCreateTime(new Date());
        newStock.setDeleted(0);

        // 根据投入品类型填充其他信息
        fillInputStockInfo(newStock);

        // 更新预警状态
        updateWarningStatus(newStock);

        return newStock;
    }

    /**
     * 填充投入品库存信息
     */
    private void fillInputStockInfo(NsInputStock stock) {
        switch (stock.getInputType()) {
            case 2:
                stock.setInputCategory("肥料");
                NsFertilizer nsFertilizer = nsFertilizerDao.selectByPrimaryKey(stock.getBusinessId());
                if (nsFertilizer != null) {
                    stock.setInputName(nsFertilizer.getDetails());
                    stock.setInputSubCategory(nsFertilizer.getFertilizerType());
                    stock.setStockWarningLine(nsFertilizer.getStockWarningLine());
                }
                break;
            case 3:
                stock.setInputCategory("植保剂");
                NsAgriculturalMaterials agriculturalMaterials = agriculturalMaterialsDao.selectByPrimaryKey(stock.getBusinessId());
                if (agriculturalMaterials != null) {
                    stock.setInputName(agriculturalMaterials.getBrandName());
                    stock.setInputSubCategory(agriculturalMaterials.getAgriculturalMaterialsType());
                    stock.setStockWarningLine(agriculturalMaterials.getStockWarningLine());
                }
                break;
            case 4:
                stock.setInputCategory("种子");
                NsCropBreeds nsCropBreeds = nsCropBreedsDao.selectByPrimaryKey(stock.getBusinessId());
                if (nsCropBreeds != null) {
                    stock.setInputName(nsCropBreeds.getCropName());
                    stock.setInputSubCategory(nsCropBreeds.getBreeds());
                    stock.setStockWarningLine(nsCropBreeds.getStockWarningLine());
                }
                break;
            case 5:
                stock.setInputCategory("农膜");
                NsFarmFilm nsFarmFilm = nsFarmFilmDao.selectByPrimaryKey(stock.getBusinessId());
                if (nsFarmFilm != null) {
                    stock.setInputName(nsFarmFilm.getDetails());
                    stock.setInputSubCategory(nsFarmFilm.getFilmType());
                    stock.setStockWarningLine(nsFarmFilm.getStockWarningLine());
                }
                break;
            case 6:
                stock.setInputCategory("其他");
                NsInputOther nsInputOther = nsInputOtherDao.selectByPrimaryKey(stock.getBusinessId());
                if (nsInputOther != null) {
                    stock.setInputName(nsInputOther.getDetails());
                    stock.setInputSubCategory(nsInputOther.getInputType());
                    stock.setStockWarningLine(nsInputOther.getStockWarningLine());
                }
                break;
            default:
                stock.setInputCategory("未知");
                break;
        }
    }

    /**
     * 创建盘点记录
     */
    private NsInputStockTaking createTakingRecord(SaveInputStockTakingForm.StockItemForm stockItemForm,
                                                BigDecimal originalQuantity,
                                                SaveInputStockTakingForm.InputStockTakingForm form,
                                                NsWarehouse warehouse,
                                                LoginUser loginUser,
                                                Date takingDate,
                                                NsInputStock stock) {
        NsInputStockTaking takingRecord = new NsInputStockTaking();
        takingRecord.setTakingId(maxIdService.getAndIncrement("NsInputStockTaking"));
        takingRecord.setBusinessId(stockItemForm.getBusinessId());
        takingRecord.setInputType(stockItemForm.getInputType());
        takingRecord.setInputCategory(stock.getInputCategory());
        takingRecord.setInputSubCategory(stock.getInputSubCategory());
        takingRecord.setInputName(stock.getInputName());
        takingRecord.setStockQuantity(originalQuantity);
        takingRecord.setTakingStockQuantity(stockItemForm.getTakingStockQuantity());
        takingRecord.setHouseId(form.getHouseId());
        takingRecord.setHouseName(warehouse.getWarehouseCode());
        takingRecord.setWarningStatus(stock.getWarningStatus());
        takingRecord.setTakingDate(takingDate);
        takingRecord.setTakingOperator(form.getTakingOperator());
        takingRecord.setTakingOperatorId(loginUser.getUser().getUserId());
        takingRecord.setRegionDeptId(Long.valueOf(form.getRegionDeptId()));
        takingRecord.setCreateBy(loginUser.getUser().getUserId());
        takingRecord.setCreateTime(new Date());
        takingRecord.setDeleted(0);

        // 设置盘点结果
        int compareResult = originalQuantity.compareTo(stockItemForm.getTakingStockQuantity());
        if (compareResult == 0) {
            takingRecord.setTakingResult(0); // 正常
        } else if (compareResult > 0) {
            takingRecord.setTakingResult(1); // 盘亏
        } else {
            takingRecord.setTakingResult(2); // 盘盈
        }

        return takingRecord;
    }

}
