package com.jorchi.business.service;

import com.github.pagehelper.PageHelper;
import com.jorchi.business.dao.NsWarehouseDao;
import com.jorchi.business.po.NsWarehouse;
import com.jorchi.business.form.NsWarehouseForm;
import com.jorchi.common.BaseService;
import com.jorchi.common.constant.CommonDef;
import com.jorchi.common.utils.DateDeserializer;
import com.jorchi.common.utils.SecurityUtils;
import com.jorchi.framework.security.LoginUser;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import pdfc.claim.common.ClientException;
import pdfc.claim.common.CommonUtil;

import javax.annotation.Resource;
import java.util.*;


/**
 * 表ns_warehouse的服务层对象<br/>
 * 对应表名：ns_warehouse，表备注：仓库表
 * @author: 周建宇 at jorchi
 * @date: 2024-12-01 11:14:24
 */
@Slf4j
@Service
public class NsWarehouseService extends BaseService {

    @Resource
    NsWarehouseDao nsWarehouseDao;

    @Resource
    MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询（仓库表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-01 11:14:24
     */
    public NsWarehouse findById(Long primaryKey) {
        return nsWarehouseDao.selectByPrimaryKey(primaryKey);
    }

    /**
     * 按名称查询（仓库表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-01 11:14:24
     */
    public NsWarehouse findByName(String name) {
        // TODO : 设置查询条件：名称，删除标志位等
        NsWarehouse query = NsWarehouse.builder().warehouseName(name)
            .deleted(CommonDef.DELETE_FLAG_NORMAL)
            .build();

        // 查询
        List<NsWarehouse> result = nsWarehouseDao.selectPage(query);
        if (result == null || result.isEmpty())
            return null;
        return result.get(0);
    }

    /**
     * 分页查询（仓库表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-01 11:14:24
     */
    public List<NsWarehouse> listPage(NsWarehouseForm nsWarehouseForm) {
        if (log.isDebugEnabled())
            log.debug(CommonUtil.append("listPage, nsWarehouseForm:", nsWarehouseForm));

        // 设置分页参数
        PageHelper.startPage(nsWarehouseForm.getPageNum(), nsWarehouseForm.getPageSize(),"create_time desc");
        return nsWarehouseDao.selectPageByForm(nsWarehouseForm);
    }

    /**
     * 删除（仓库表）<br>
     * TODO : 请根据实际情况更新为删除标志位或物理删除！！
     * @author: 周建宇 at jorchi
     * @date: 2024-12-01 11:14:24
     */
    public boolean deleteNsWarehouse(Long id) {
        if (id == null)
            throw ClientException.of("删除时主键不能为空！");

        // 物理删除
        // return nsWarehouseDao.deleteByPrimaryKey(id) == 1 ? true : false;

        // 仅更新删除标志位
        NsWarehouse nsWarehouse = NsWarehouse.builder()
            .warehouseId(id)
            .deleted(CommonDef.DELETE_FLAG_DELETE)
            // .status(status)
            .build();

        // 更新数据
        int res = nsWarehouseDao.updateSelectiveByPrimaryKey(nsWarehouse);
        return res == 1 ? true : false;
    }

    /**
     * 新增或保存（仓库表）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2024-12-01 11:14:24
     */
    public NsWarehouse saveNsWarehouse (NsWarehouse nsWarehouse) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long primaryKey = nsWarehouse.getWarehouseId();
        if (primaryKey == null || primaryKey == 0) { // do insert
            // 生成主键
            nsWarehouse.setWarehouseId(maxIdService.getAndIncrement("NsWarehouse"));

            // set CreateTime
            nsWarehouse.setCreateTime(new Date());
            nsWarehouse.setDeleted(CommonDef.DELETE_FLAG_NORMAL);

            // do insert by not null properties
            nsWarehouseDao.insertSelective(nsWarehouse);
            return nsWarehouse;
        } else { // do update

            // set UpdateTime
            nsWarehouse.setUpdateTime(new Date());
            nsWarehouse.setDeleted(CommonDef.DELETE_FLAG_NORMAL);
            // do update by not null properties
            int count = nsWarehouseDao.updateSelectiveByPrimaryKey(nsWarehouse);
            if (count <= 0)
                nsWarehouseDao.insertSelective(nsWarehouse);
            return nsWarehouse;
        }
    }

    /**
     * 重名检查(请按需使用)（仓库表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-01 11:14:24
    public void checkNameExisted(NsWarehouse nsWarehouse) {
        // 设置查询条件：名称，删除标志位等
        NsWarehouse query = NsWarehouse.builder().name(nsWarehouse.getName()).build();
        List<NsWarehouse> result = nsWarehouseDao.selectPage(query);
        if (result == null || result.isEmpty())
            return;

        // 新增或主键不同
        if (nsWarehouse.getWarehouseId() == null || !result.get(0).getWarehouseId().equals(nsWarehouse.getWarehouseId()))
            throw ClientException.of("该名称已存在！Code:400-", nsWarehouse.getName(), "-k-", result.get(0).getWarehouseId());
    }*/

}
