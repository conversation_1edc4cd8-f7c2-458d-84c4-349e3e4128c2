package com.jorchi.business.api;

import com.jorchi.business.po.NsSpec;
import com.jorchi.business.service.NsSpecService;
import com.jorchi.business.form.NsSpecForm;
import com.jorchi.common.utils.ServletUtils;
import com.jorchi.common.utils.spring.SpringUtils;
import com.jorchi.framework.aspectj.lang.annotation.Log;
import com.jorchi.framework.aspectj.lang.enums.BusinessType;
import com.jorchi.framework.security.LoginUser;
import com.jorchi.framework.security.service.TokenService;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.framework.web.page.TableDataInfo;
import com.jorchi.server.vo.IdNameVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import pdfc.claim.common.ClientException;
import pdfc.claim.common.CommonUtil;

import javax.annotation.Resource;

/**
 * 表ns_spec的控制层对象<br/>
 * 对应表名：ns_spec，表备注：规格表
 * @author: 周建宇 at jorchi
 * @date: 2025-06-14 10:50:47
 */
@Slf4j
@RestController
@RequestMapping("/nsSpec")
public class NsSpecApi extends BaseApi {

    @Resource
    NsSpecService nsSpecService;


    /**
     * 按主键查询（规格表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-06-14 10:50:47
     */
    @PreAuthorize("@ss.hasPermi('business:nsSpec:list')")
    @PostMapping("/nsSpecDtl") // {{baseApi}}/nsSpec/nsSpecDtl
    public AjaxResult findById(@RequestBody IdNameVo idNameVo) {
        try {
            Long specId = idNameVo.getId();
            // id can not be empty
            if (specId == null)
                throw ClientException.of("findById but specId can not be empty error!");

            NsSpec bean = nsSpecService.findById(specId);
            if (bean == null)
                throw ClientException.of("NsSpec not found error!");
            return AjaxResult.success(bean);
        } catch (Throwable t) {
            log.error("nsSpecDtl error!", t);
            throw t;
        }
    }

    /**
     * 分页查询（规格表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-06-14 10:50:47
     */
    @PreAuthorize("@ss.hasPermi('business:nsSpec:list')")
    @PostMapping("/nsSpecList") // {{baseApi}}/nsSpec/nsSpecList
    public TableDataInfo listPage(@RequestBody NsSpecForm nsSpecVo) {
        try {
            // 分页参数检查
            if (nsSpecVo.getPageNum() == null) {
                log.warn("/nsSpecList please set pageNum!!");
                nsSpecVo.setPageNum(1);
            }
            if (nsSpecVo.getPageSize() == null) {
                log.warn("/nsSpecList please set pageSize!!");
                nsSpecVo.setPageSize(10);
            }

            return getDataTable(nsSpecService.listPage(nsSpecVo));
        } catch (Throwable t) {
            log.error("nsSpecList listPage error!", t);
            throw t;
        }
    }

    /**
     * 删除（规格表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-06-14 10:50:47
     */
    @PreAuthorize("@ss.hasPermi('business:nsSpec:remove')")
    @PostMapping("/nsSpecDelete") // {{baseApi}}/nsSpec/nsSpecDelete
    @Log(title = "规格表", businessType = BusinessType.DELETE)
    public AjaxResult deleteNsSpec(@RequestBody IdNameVo idNameVo) {
        try {
            Long specId = idNameVo.getId();
            LoginUser loginUser = SpringUtils.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest());
            log.info(CommonUtil.append("/nsSpec/nsSpecDelete start, specId:", specId));
            return AjaxResult.success(nsSpecService.deleteNsSpec(specId));
        } catch (Throwable t) {
            log.error("nsSpecDelete error!", t);
            throw t;
        }
    }

    /**
     * 新增或保存（规格表）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2025-06-14 10:50:47
     */
    @PreAuthorize("@ss.hasPermi('business:nsSpec:edit')")
    @PostMapping("/nsSpecSave") // {{baseApi}}/nsSpec/nsSpecSave
    @Log(title = "规格表", businessType = BusinessType.UPDATE)
    public AjaxResult saveNsSpec (@RequestBody NsSpec nsSpec) {
        try {
            // 获取当前的用户
            LoginUser loginUser = SpringUtils.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest());
            log.info(CommonUtil.append("/nsSpec/nsSpecSave start, RequestBody:", nsSpec));

            // 检查待保存数据的合法性
            validatePo(nsSpec);

            // TODO : 参数非空性检查 ...

            // 名称唯一性检查?
            // nsSpecService.checkNameExisted(nsSpec);

            NsSpec result = nsSpecService.saveNsSpec(nsSpec);
            return AjaxResult.success(result);
        } catch (Throwable t) {
            log.error("nsSpecSave error!", t);
            throw t;
        }
    }

    /**
     * 检查待保存数据的合法性（规格表）<br>
     * @author:  周建宇 at jorchi
     * @date: 2025-06-14 10:50:47
     */
    protected void validatePo(NsSpec nsSpec) {

        // 农产品或者投入品名称-长度检查
        if (nsSpec.getName() != null && nsSpec.getName().length() > 16)
            throw ClientException.of("【农产品或者投入品名称】字段最多只能输入【16】个字符！");

        // 单位，例如kg-长度检查
        if (nsSpec.getUnit() != null && nsSpec.getUnit().length() > 16)
            throw ClientException.of("【单位，例如kg】字段最多只能输入【16】个字符！");

        // 打包单位,例如袋-长度检查
        if (nsSpec.getPackageUnit() != null && nsSpec.getPackageUnit().length() > 16)
            throw ClientException.of("【打包单位,例如袋】字段最多只能输入【16】个字符！");

        // 规格说明-长度检查
        if (nsSpec.getMark() != null && nsSpec.getMark().length() > 64)
            throw ClientException.of("【规格说明】字段最多只能输入【64】个字符！");

        // TODO : 其它必填项检查？
        // if (releaseVersion.getName() == null || releaseVersion.getName().trim().isEmpty())
        //     throw ClientException.of("【name】字段不能为空！");
    }

}
