package com.jorchi.business.api;


import com.google.gson.JsonArray;
import com.jorchi.business.po.NsAuditExecute;
import com.jorchi.business.service.NsAuditExecuteService;
import com.jorchi.business.form.NsAuditExecuteForm;
import com.jorchi.business.vo.ExcuteAuditVo;
import com.jorchi.common.utils.FsAssert;
import com.jorchi.common.utils.QRCodeUtil;
import com.jorchi.common.utils.ServletUtils;
import com.jorchi.common.utils.spring.SpringUtils;
import com.jorchi.framework.aspectj.lang.annotation.Log;
import com.jorchi.framework.aspectj.lang.enums.BusinessType;
import com.jorchi.framework.security.LoginUser;
import com.jorchi.framework.security.service.TokenService;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.framework.web.page.TableDataInfo;
import com.jorchi.project.image.service.ImageService;
import com.jorchi.server.vo.DetailTaskVO;
import com.jorchi.server.vo.IdNameVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.Get;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import pdfc.claim.common.ClientException;
import pdfc.claim.common.CommonUtil;
import pdfc.claim.common.FileUtil;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

/**
 * 表ns_audit_execute的控制层对象<br/>
 * 对应表名：ns_audit_execute，表备注：稽核执行
 * @author: xubinbin at jorchi
 * @date: 2022-11-17 09:42:32
 */
@Slf4j
@RestController
@RequestMapping("/nsAuditExecute")
public class NsAuditExecuteApi extends BaseApi {

    @Resource
    NsAuditExecuteService nsAuditExecuteService;

    @Autowired
    ImageService imageService;


    /**
     * 按主键查询（稽核执行）<br>
     * @author: xubinbin at jorchi
     * @date: 2022-11-17 09:42:32
     */
    @PreAuthorize("@ss.hasPermi('business:nsAuditExecute:list')")
    @PostMapping("/nsAuditExecuteDtl") // {{baseApi}}/nsAuditExecute/nsAuditExecuteDtl
    public AjaxResult findById(@RequestBody IdNameVo idNameVo) {
        try {
            Long auditId = idNameVo.getId();
            FsAssert.notNull(auditId,"ID不能为空");
            NsAuditExecute bean = nsAuditExecuteService.findById(auditId);
            FsAssert.notNull(bean,"暂无数据");
            return AjaxResult.success(bean);
        } catch (Throwable t) {
            log.error("nsAuditExecuteDtl error!", t);
            throw t;
        }
    }

    /**
     * 按主键查询稽核任务（小程序）
     * @Author: xubinbin
     * @Date: 2022年12月13日09:07:52
     */
    @PreAuthorize("@ss.hasPermi('business:nsAuditExecute:list')")
    @PostMapping("/nsAuditExecuteMini") // {{baseApi}}/nsAuditExecute/nsAuditExecuteMini
    public AjaxResult selectById(@RequestBody IdNameVo idNameVo) {
        try {
            Long auditId = idNameVo.getId();
            FsAssert.notNull(auditId,"ID不能为空");
            NsAuditExecute bean = nsAuditExecuteService.selectById(auditId);
            FsAssert.notNull(bean,"暂无数据");
            return AjaxResult.success(bean);
        } catch (Throwable t) {
            log.error("nsAuditExecuteMini error!", t);
            throw t;
        }
    }

    /**
     * 分页查询（稽核执行）<br>
     * @author: xubinbin at jorchi
     * @date: 2022-11-17 09:42:32
     */
    @PreAuthorize("@ss.hasPermi('business:nsAuditExecute:list')")
    @PostMapping("/nsAuditExecuteList") // {{baseApi}}/nsAuditExecute/nsAuditExecuteList
    public TableDataInfo listPage(@RequestBody NsAuditExecuteForm nsAuditExecuteVo) {
        try {
            // 分页参数检查
            if (nsAuditExecuteVo.getPageNum() == null) {
                log.warn("/nsAuditExecuteList please set pageNum!!");
                nsAuditExecuteVo.setPageNum(1);
            }
            if (nsAuditExecuteVo.getPageSize() == null) {
                log.warn("/nsAuditExecuteList please set pageSize!!");
                nsAuditExecuteVo.setPageSize(10);
            }

            return getDataTable(nsAuditExecuteService.listPage(nsAuditExecuteVo));
        } catch (Throwable t) {
            log.error("nsAuditExecuteList listPage error!", t);
            throw t;
        }
    }

    /**
     * @Author: xubinbin
     * @Date: 2022年12月9日14:36:53
     * @Describe: 今日新增-稽核任务查询（小程序）
     */
    @PreAuthorize("@ss.hasPermi('business:nsAuditExecute:list')")
    @PostMapping("/nsAuditTodayList") // {{baseApi}}/nsAuditExecute/nsAuditTodayList
    public TableDataInfo todayListPage(@RequestBody NsAuditExecuteForm nsAuditExecuteVo) {
        try {
            LoginUser loginUser = SpringUtils.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest());

            // 分页参数检查
            if (nsAuditExecuteVo.getPageNum() == null) {
                log.warn("/nsAuditExecuteList please set pageNum!!");
                nsAuditExecuteVo.setPageNum(1);
            }
            if (nsAuditExecuteVo.getPageSize() == null) {
                log.warn("/nsAuditExecuteList please set pageSize!!");
                nsAuditExecuteVo.setPageSize(10);
            }
            return getDataTable(nsAuditExecuteService.todayListPage(nsAuditExecuteVo,loginUser));
        }catch (Throwable t) {
            log.error("nsAuditTodayList listPage error!", t);
            throw t;
        }

    }


    /**
     * @Author: xubinbin
     * @Date: 2022年12月9日16:53:18
     * @Describe： 逾期稽核任务（小程序）
     */
    @PreAuthorize("@ss.hasPermi('business:nsAuditExecute:list')")
    @PostMapping("/nsAuditDelayList") // {{baseApi}}/nsAuditExecute/nsAuditDelayList
    public TableDataInfo delayListPage(@RequestBody NsAuditExecuteForm nsAuditExecuteVo) {
        try {
            LoginUser loginUser = SpringUtils.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest());

            // 分页参数检查
            if (nsAuditExecuteVo.getPageNum() == null) {
                log.warn("/nsAuditExecuteList please set pageNum!!");
                nsAuditExecuteVo.setPageNum(1);
            }
            if (nsAuditExecuteVo.getPageSize() == null) {
                log.warn("/nsAuditExecuteList please set pageSize!!");
                nsAuditExecuteVo.setPageSize(10);
            }
            return getDataTable(nsAuditExecuteService.delayListPage(nsAuditExecuteVo,loginUser));
        }catch (Throwable t) {
            log.error("nsAuditDelayList listPage error!", t);
            throw t;
        }
    }

    /**
     * @Author: xubinbin
     * @Date: 2022年12月12日09:11:41
     * @Describe： 今日已处理-稽核任务（小程序）
     */
    @PreAuthorize("@ss.hasPermi('business:nsAuditExecute:list')")
    @PostMapping("/nsTodayDetailList") // {{baseApi}}/nsAuditExecute/nsTodayDetailList
    public TableDataInfo todayDetailList(@RequestBody NsAuditExecuteForm nsAuditExecuteVo) {
        try {
            LoginUser loginUser = SpringUtils.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest());

            // 分页参数检查
            if (nsAuditExecuteVo.getPageNum() == null) {
                log.warn("/nsAuditExecuteList please set pageNum!!");
                nsAuditExecuteVo.setPageNum(1);
            }
            if (nsAuditExecuteVo.getPageSize() == null) {
                log.warn("/nsAuditExecuteList please set pageSize!!");
                nsAuditExecuteVo.setPageSize(10);
            }
            return getDataTable(nsAuditExecuteService.todayDetailList(nsAuditExecuteVo,loginUser));
        }catch (Throwable t) {
            log.error("nsAuditDelayList listPage error!", t);
            throw t;
        }
    }

    /**
     * @Author: xubinbin
     * @Date: 2022年12月12日11:11:45
     * @Describe: 稽核任务处理
     */
    @PreAuthorize("@ss.hasPermi('business:nsAuditExecute:edit')")
    @PostMapping("/nsAuditDetail") // {{baseApi}}/nsAuditExecute/nsAuditDetail
    public AjaxResult detailAuditTask(@RequestBody DetailTaskVO detailTaskVO) {
        try{
            // 稽核任务ID
            Long id = detailTaskVO.getAuditId();
            // 获取当前操作人
            LoginUser loginUser = SpringUtils.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest());
            // 图片
            String pictureUrl = detailTaskVO.getPictureUrl();
            // 获取处理状态
            int status = detailTaskVO.getStatus();
            if (status != 0 && status != 1){
                throw ClientException.of("处理状态有误！");
            }else {
                // 处理任务
                nsAuditExecuteService.detailTask(id, loginUser, status,pictureUrl);
            }
            return AjaxResult.success();
        }catch (Throwable t) {
            log.error("HistoryAuditList  error!", t);
            throw t;
        }
    }


    /**
     * @Author: xubinbin
     * @Date: 2022年12月6日16:08:11
     * @Describe： 历史稽核查询（小程序）
     */
    @PreAuthorize("@ss.hasPermi('business:nsAuditExecute:list')")
    @PostMapping("/HistoryAuditList") // {{baseApi}}/nsAuditExecute/HistoryAuditList
    public AjaxResult findHistoryAudit(@RequestBody IdNameVo idNameVo) {
        try{
            // 大棚编号
            String name = idNameVo.getName();
            // 获取当前用户
            LoginUser loginUser = SpringUtils.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest());

            List<NsAuditExecute> list = nsAuditExecuteService.selectHistoryAudit(name,loginUser);

            return AjaxResult.success(list);
        }catch (Throwable t) {
            log.error("HistoryAuditList  error!", t);
            throw t;
        }

    }

    /**
     * 删除（稽核执行）<br>
     * @author: xubinbin at jorchi
     * @date: 2022-11-17 09:42:32
     */
    @PreAuthorize("@ss.hasPermi('business:nsAuditExecute:remove')")
    @PostMapping("/nsAuditExecuteDelete") // {{baseApi}}/nsAuditExecute/nsAuditExecuteDelete
    @Log(title = "稽核执行", businessType = BusinessType.DELETE)
    public AjaxResult deleteNsAuditExecute(@RequestBody IdNameVo idNameVo) {
        try {
            Long auditId = idNameVo.getId();
            LoginUser loginUser = SpringUtils.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest());
            log.info(CommonUtil.append("/nsAuditExecute/nsAuditExecuteDelete start, auditId:", auditId));
            return AjaxResult.success(nsAuditExecuteService.deleteNsAuditExecute(loginUser,auditId));
        } catch (Throwable t) {
            log.error("nsAuditExecuteDelete error!", t);
            throw t;
        }
    }

    /**
     * 新增或保存（稽核执行）<br>
     * ID 为空即为新增，否则为更新
     * @author: xubinbin at jorchi
     * @date: 2022-11-17 09:42:32
     */
    @PreAuthorize("@ss.hasPermi('business:nsAuditExecute:edit')")
    @PostMapping("/nsAuditExecuteSave") // {{baseApi}}/nsAuditExecute/nsAuditExecuteSave
    @Log(title = "稽核执行", businessType = BusinessType.UPDATE)
    public AjaxResult saveNsAuditExecute (@RequestBody NsAuditExecute nsAuditExecute) {
        try {
            // 获取当前的用户
            LoginUser loginUser = SpringUtils.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest());
            log.info(CommonUtil.append("/nsAuditExecute/nsAuditExecuteSave start, RequestBody:", nsAuditExecute));

            // 检查待保存数据的合法性
            validatePo(nsAuditExecute);

            //新增或更新
            NsAuditExecute result = nsAuditExecuteService.saveNsAuditExecute(loginUser,nsAuditExecute);
            return AjaxResult.success(result);
        } catch (Throwable t) {
            log.error("nsAuditExecuteSave error!", t);
            throw t;
        }
    }

    /**
     * 新增或保存（稽核执行-小程序）<br>
     * @Author: xubinbin
     * @Date: 2022年12月2日10:55:02
     */
    @PreAuthorize("@ss.hasPermi('business:nsAuditExecute:edit')")
    @PostMapping("/nsAuditExecuteMiniSave") // {{baseApi}}/nsAuditExecute/nsAuditExecuteMiniSave
    @Log(title = "稽核执行", businessType = BusinessType.UPDATE)
    public AjaxResult saveNsAuditExecuteMini (@RequestBody ExcuteAuditVo excuteAuditVo) {
        try{
            // 获取当前的用户
            LoginUser loginUser = SpringUtils.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest());
            log.info(CommonUtil.append("/nsAuditExecute/nsAuditExecuteSave start, RequestBody:", excuteAuditVo));

            //新增
            int i = nsAuditExecuteService.saveNsAuditExecuteMini(loginUser,excuteAuditVo);

            return AjaxResult.success(i);

        }catch (Throwable t) {
            log.error("nsAuditExecuteMiniSave error!", t);
            throw t;
        }
    }


    /**
     * 批量保存稽核执行任务（稽核执行）<br>
     * @author:  xubinbin at jorchi
     * @date: 2022-11-17 09:42:32
     */
    @PreAuthorize("@ss.hasPermi('business:nsAuditExecute:pljh')")
    @PostMapping("/nsAuditExecuteSaveBatch") // {{baseApi}}/nsAuditExecute/nsAuditExecuteSaveBatch
    @Log(title = "稽核执行", businessType = BusinessType.UPDATE)
    public AjaxResult saveBatchNsAuditExecute (@RequestBody NsAuditExecute nsAuditExecute) throws ParseException {
        try {
            // 获取当前的用户
            LoginUser loginUser = SpringUtils.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest());
            //新增
            int i = nsAuditExecuteService.saveBatchNsAuditExecute(loginUser,nsAuditExecute);

            return AjaxResult.success(i);


        } catch (Throwable t) {
            log.error("nsAuditExecuteSave error!", t);
            throw t;
        }
    }


    /**
     * 检查待保存数据的合法性（稽核执行）<br>
     * @author:  xubinbin at jorchi
     * @date: 2022-11-17 09:42:32
     */
    protected void validatePo(NsAuditExecute nsAuditExecute) {

        // 稽核总项-长度检查
        if (nsAuditExecute.getAuditTotal() != null && nsAuditExecute.getAuditTotal().length() > 128)
            throw ClientException.of("【稽核总项】字段最多只能输入【128】个字符！");

        // 稽核事项-长度检查
        if (nsAuditExecute.getAuditName() != null && nsAuditExecute.getAuditName().length() > 128)
            throw ClientException.of("【稽核事项】字段最多只能输入【128】个字符！");

        // 备注信息-长度检查
        if (nsAuditExecute.getAuditRemarks() != null && nsAuditExecute.getAuditRemarks().length() > 128)
            throw ClientException.of("【备注信息】字段最多只能输入【128】个字符！");

        // 种植区-长度检查
        if (nsAuditExecute.getPlantingArea() != null && nsAuditExecute.getPlantingArea().length() > 128)
            throw ClientException.of("【种植区】字段最多只能输入【128】个字符！");

        // 大棚编号-长度检查
        if (nsAuditExecute.getHouseName() != null && nsAuditExecute.getHouseName().length() > 32)
            throw ClientException.of("【大棚编号】字段最多只能输入【32】个字符！");

    }


    /**
     * 生成大棚二维码(针对正式环境)
     * @return
     * @throws Exception
     */
    @GetMapping("/getQRCode")
    public void getWebQR(@RequestParam String id) throws Exception {
        // 存放在二维码中的内容
        try {
            /*// 嵌入二维码的图片路径
            String imgPath = "D:/Work/测试.jpg";
            // 生成的二维码的路径及名称
            String destPath = "D:/Work/jam.jpg";
            //生成二维码
            QRCodeUtil.encode(CommonUtil.append("https://admin.yunhezl.com/h5/index.html#/h5Pages/h5Pages?id",id), imgPath, destPath, true);
            // 解析二维码
            String str = QRCodeUtil.decode(destPath);*/

            // 查找大棚编号
            String houseName = nsAuditExecuteService.selectHouseName(id);
            // 用以显示大棚名字
            List<String> code = new ArrayList<>();
            code.add(CommonUtil.append("   大棚编号：",houseName));

            /*String logoImage = "D:/Work/测试.jpg";
            BufferedImage bufferedImage = QRCodeUtil.encode(CommonUtil.append("https://admin.yunhezl.com/h5/index.html#/h5Pages/h5Pages?id=",id), logoImage, true);*/
            BufferedImage bufferedImage = QRCodeUtil.createImage(code, CommonUtil.append("https://admin.yunhezl.com/h5/index.html#/h5Pages/h5Pages?id=", id), null, false);

            HttpServletResponse response = ServletUtils.getResponse();
            response.setContentType(CommonUtil.append("image/jpg"));
            response.setDateHeader("expries", -1);
            response.setHeader("Cache-Control", "no-cache");
            response.setHeader("Pragma", "no-cache");
            ImageIO.write(bufferedImage,"jpg", response.getOutputStream());
        } catch (Exception e){
            log.error(CommonUtil.append("getWebQR error!!! id:", id), e);
            throw e;
        }

    }

    /**
     * 生成设备二维码(针对正式环境)
     */
    @GetMapping("/getDeviceQRCode") //{{baseApi}}/nsAuditExecute/getDeviceQRCode
    public void getDeviceQR(@RequestParam String deviceId) throws Exception {

        // 存放在二维码中的内容
        try {
/*            // 嵌入二维码的图片路径
            String imgPath = "D:/Work/view.jpg";
            // 生成的二维码的路径及名称
            String destPath = "D:/Work/jam.jpg";
            //生成二维码
            QRCodeUtil.encode(CommonUtil.append("https://admin.yunhezl.com/h5/index.html#/h5Pages/h5Pages?deviceId=",deviceId), imgPath, destPath, true);
            // 解析二维码
            String str = QRCodeUtil.decode(destPath);*/
//            String logoImage = "D:/Work/view.jpg";
            // 用以显示设备文字
            List<String> code = new ArrayList<>();
            code.add(CommonUtil.append("设备编号：",deviceId));
//            BufferedImage bufferedImage = QRCodeUtil.encode(CommonUtil.append("https://admin.yunhezl.com/h5/index.html#/h5Pages/deviceDetail?deviceId=",deviceId), logoImage, true);
            BufferedImage bufferedImage = QRCodeUtil.createImage(code, CommonUtil.append("https://admin.yunhezl.com/h5/index.html#/h5Pages/deviceDetail?deviceId=", deviceId), null, false);
            HttpServletResponse response = ServletUtils.getResponse();
            response.setContentType(CommonUtil.append("image/jpeg"));
            response.setDateHeader("expries", -1);
            response.setHeader("Cache-Control", "no-cache");
            response.setHeader("Pragma", "no-cache");
            ImageIO.write(bufferedImage,"jpg", response.getOutputStream());
        } catch (Exception e){
            log.error(CommonUtil.append("getWebQR error!!! id:", deviceId), e);
            throw e;
        }
    }

}
