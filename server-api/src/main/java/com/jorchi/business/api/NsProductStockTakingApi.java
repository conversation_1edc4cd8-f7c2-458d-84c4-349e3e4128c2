package com.jorchi.business.api;

import com.jorchi.business.po.NsProductStockTaking;
import com.jorchi.business.service.NsProductStockTakingService;
import com.jorchi.business.form.NsProductStockTakingForm;
import com.jorchi.business.form.NsProductStockTakingSaveForm;
import com.jorchi.common.utils.ServletUtils;
import com.jorchi.common.utils.spring.SpringUtils;
import com.jorchi.framework.aspectj.lang.annotation.Log;
import com.jorchi.framework.aspectj.lang.enums.BusinessType;
import com.jorchi.framework.security.LoginUser;
import com.jorchi.framework.security.service.TokenService;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.framework.web.page.TableDataInfo;
import com.jorchi.server.vo.IdNameVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import pdfc.claim.common.ClientException;
import pdfc.claim.common.CommonUtil;

import javax.annotation.Resource;
import java.util.List;

/**
 * 表ns_product_stock_taking的控制层对象<br/>
 * 对应表名：ns_product_stock_taking，表备注：农产品库存盘点表
 * @author: 周建宇 at jorchi
 * @date: 2025-01-11 11:06:55
 */
@Slf4j
@RestController
@RequestMapping("/nsProductStockTaking")
public class NsProductStockTakingApi extends BaseApi {

    @Resource
    NsProductStockTakingService nsProductStockTakingService;


    /**
     * 按主键查询（农产品库存盘点表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-11 11:06:55
     */
    @PreAuthorize("@ss.hasPermi('business:nsProductStockTaking:list')")
    @PostMapping("/nsProductStockTakingDtl") // {{baseApi}}/nsProductStockTaking/nsProductStockTakingDtl
    public AjaxResult findById(@RequestBody IdNameVo idNameVo) {
        try {
            Long takingId = idNameVo.getId();
            // id can not be empty
            if (takingId == null)
                throw ClientException.of("findById but takingId can not be empty error!");

            NsProductStockTaking bean = nsProductStockTakingService.findById(takingId);
            if (bean == null)
                throw ClientException.of("NsProductStockTaking not found error!");
            return AjaxResult.success(bean);
        } catch (Throwable t) {
            log.error("nsProductStockTakingDtl error!", t);
            throw t;
        }
    }

    /**
     * 分页查询（农产品库存盘点表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-11 11:06:55
     */
    @PreAuthorize("@ss.hasPermi('business:nsProductStockTaking:list')")
    @PostMapping("/nsProductStockTakingList") // {{baseApi}}/nsProductStockTaking/nsProductStockTakingList
    public TableDataInfo listPage(@RequestBody NsProductStockTakingForm nsProductStockTakingVo) {
        try {
            // 分页参数检查
            if (nsProductStockTakingVo.getPageNum() == null) {
                log.warn("/nsProductStockTakingList please set pageNum!!");
                nsProductStockTakingVo.setPageNum(1);
            }
            if (nsProductStockTakingVo.getPageSize() == null) {
                log.warn("/nsProductStockTakingList please set pageSize!!");
                nsProductStockTakingVo.setPageSize(10);
            }

            return getDataTable(nsProductStockTakingService.listPage(nsProductStockTakingVo));
        } catch (Throwable t) {
            log.error("nsProductStockTakingList listPage error!", t);
            throw t;
        }
    }

    /**
     * 删除（农产品库存盘点表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-11 11:06:55
     */
    @PreAuthorize("@ss.hasPermi('business:nsProductStockTaking:remove')")
    @PostMapping("/nsProductStockTakingDelete") // {{baseApi}}/nsProductStockTaking/nsProductStockTakingDelete
    @Log(title = "农产品库存盘点表", businessType = BusinessType.DELETE)
    public AjaxResult deleteNsProductStockTaking(@RequestBody IdNameVo idNameVo) {
        try {
            Long takingId = idNameVo.getId();
            LoginUser loginUser = SpringUtils.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest());
            log.info(CommonUtil.append("/nsProductStockTaking/nsProductStockTakingDelete start, takingId:", takingId));
            return AjaxResult.success(nsProductStockTakingService.deleteNsProductStockTaking(takingId));
        } catch (Throwable t) {
            log.error("nsProductStockTakingDelete error!", t);
            throw t;
        }
    }

    /**
     * 新增或保存（农产品库存盘点表）<br>
     * 新的单个提交方式，支持库存项目级别的盘点
     * @author: 周建宇 at jorchi
     * @date: 2025-09-11
     */
    @PreAuthorize("@ss.hasPermi('business:nsProductStockTaking:edit')")
    @PostMapping("/nsProductStockTakingSave") // {{baseApi}}/nsProductStockTaking/nsProductStockTakingSave
    @Log(title = "农产品库存盘点表", businessType = BusinessType.UPDATE)
    public AjaxResult saveNsProductStockTaking (@RequestBody NsProductStockTakingSaveForm saveForm) {
        try {
            // 获取当前的用户
            LoginUser loginUser = SpringUtils.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest());
            log.info(CommonUtil.append("/nsProductStockTaking/nsProductStockTakingSave start, RequestBody:", saveForm));

            // 检查待保存数据的合法性
            validateSaveForm(saveForm);

            // 调用新的保存方法
            nsProductStockTakingService.saveStockTaking(saveForm);
            return AjaxResult.success();
        } catch (Throwable t) {
            log.error("nsProductStockTakingSave error!", t);
            throw t;
        }
    }

    /**
     * 检查新表单数据的合法性（农产品库存盘点表）<br>
     * @author:  周建宇 at jorchi
     * @date: 2025-09-11
     */
    protected void validateSaveForm(NsProductStockTakingSaveForm saveForm) {
        
        // 基本字段验证
        if (saveForm.getProductStockId() == null) {
            throw ClientException.of("【农产品库存ID】字段不能为空！");
        }

        if (saveForm.getHouseId() == null) {
            throw ClientException.of("【仓库ID】字段不能为空！");
        }

        if (saveForm.getTakingOperator() == null || saveForm.getTakingOperator().trim().isEmpty()) {
            throw ClientException.of("【盘点人】字段不能为空！");
        }

        if (saveForm.getTakingDate() == null || saveForm.getTakingDate().trim().isEmpty()) {
            throw ClientException.of("【盘点日期】字段不能为空！");
        }

        if (saveForm.getRegionDeptId() == null || saveForm.getRegionDeptId().trim().isEmpty()) {
            throw ClientException.of("【农场ID】字段不能为空！");
        }

        // 库存项目验证
        if (saveForm.getStockItems() == null || saveForm.getStockItems().isEmpty()) {
            throw ClientException.of("【库存项目】不能为空！");
        }

        for (NsProductStockTakingSaveForm.StockItemForm stockItem : saveForm.getStockItems()) {


            if (stockItem.getTakingNum() == null) {
                throw ClientException.of("【盘点数量】字段不能为空！");
            }


            // 字段长度检查
            if (stockItem.getCropType() != null && stockItem.getCropType().length() > 64)
                throw ClientException.of("【农产品类别】字段最多只能输入【64】个字符！");

            if (stockItem.getCropCategory() != null && stockItem.getCropCategory().length() > 64)
                throw ClientException.of("【科别】字段最多只能输入【64】个字符！");

            if (stockItem.getBreeds() != null && stockItem.getBreeds().length() > 64)
                throw ClientException.of("【品种】字段最多只能输入【64】个字符！");

            if (stockItem.getCropName() != null && stockItem.getCropName().length() > 64)
                throw ClientException.of("【农产品名称】字段最多只能输入【64】个字符！");
        }
    }

}
