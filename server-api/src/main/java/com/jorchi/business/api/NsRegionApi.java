package com.jorchi.business.api;

import com.jorchi.business.po.NsRegion;
import com.jorchi.business.service.NsRegionService;
import com.jorchi.business.form.NsRegionForm;
import com.jorchi.business.vo.NsRegionDto;
import com.jorchi.common.utils.ServletUtils;
import com.jorchi.common.utils.spring.SpringUtils;
import com.jorchi.framework.aspectj.lang.annotation.Log;
import com.jorchi.framework.aspectj.lang.enums.BusinessType;
import com.jorchi.framework.security.LoginUser;
import com.jorchi.framework.security.service.TokenService;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.framework.web.page.TableDataInfo;
import com.jorchi.server.vo.IdNameVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import pdfc.claim.common.ClientException;
import pdfc.claim.common.CommonUtil;

import javax.annotation.Resource;

/**
 * 表ns_region的控制层对象<br/>
 * 对应表名：ns_region，表备注：区块
 * @author: TanShunFu at jorchi
 * @date: 2022-11-09 14:14:51
 */
@Slf4j
@RestController
@RequestMapping("/nsRegion")
public class NsRegionApi extends BaseApi {

    @Resource
    private NsRegionService nsRegionService;


    /**
     * 按主键查询（区块）<br>
     * @author: TanShunFu at jorchi
     * @date: 2022-11-09 14:14:51
     */
    @PreAuthorize("@ss.hasPermi('business:nsRegion:list')")
    @PostMapping("/nsRegionDtl") // {{baseApi}}/nsRegion/nsRegionDtl
    public AjaxResult findById(@RequestBody IdNameVo idNameVo) {
        try {
            Long regionId = idNameVo.getId();
            // id can not be empty
            if (regionId == null)
                throw ClientException.of("findById but regionId can not be empty error!");

            NsRegionDto bean = nsRegionService.findById(regionId);
            if (bean == null)
                throw ClientException.of("NsRegion not found error!");
            return AjaxResult.success(bean);
        } catch (Throwable t) {
            log.error("nsRegionDtl error!", t);
            throw t;
        }
    }

    /**
     * 查询所有区块
     * @Author: xubinbin
     * @Date: 2023年3月21日11:29:06
     */
    @PreAuthorize("@ss.hasPermi('business:nsRegion:list')")
    @PostMapping("/nsAllRegionList") // {{baseApi}}/nsRegion/nsAllRegionList
    public AjaxResult listAllPage(@RequestBody NsRegionForm nsRegionVo){
        try{
            return AjaxResult.success(nsRegionService.getAllRegion(nsRegionVo));
        }catch (Throwable t) {
            log.error("nsAllRegionList listPage error!", t);
            throw t;
        }
    }
    /**
     * 分页查询（区块）<br>
     * @author: TanShunFu at jorchi
     * @date: 2022-11-09 14:14:51
     */
    @PreAuthorize("@ss.hasPermi('business:nsRegion:list')")
    @PostMapping("/nsRegionList") // {{baseApi}}/nsRegion/nsRegionList
    public TableDataInfo listPage(@RequestBody NsRegionForm nsRegionVo) {
        try {
            // 分页参数检查
            if (nsRegionVo.getPageNum() == null) {
                log.warn("/nsRegionList please set pageNum!!");
                nsRegionVo.setPageNum(1);
            }
            if (nsRegionVo.getPageSize() == null) {
                log.warn("/nsRegionList please set pageSize!!");
                nsRegionVo.setPageSize(10);
            }

            return getDataTable(nsRegionService.listPage(nsRegionVo));
        } catch (Throwable t) {
            log.error("nsRegionList listPage error!", t);
            throw t;
        }
    }

    /**
     * 删除（区块）<br>
     * @author: TanShunFu at jorchi
     * @date: 2022-11-09 14:14:51
     */
    @PreAuthorize("@ss.hasPermi('business:nsRegion:remove')")
    @PostMapping("/nsRegionDelete") // {{baseApi}}/nsRegion/nsRegionDelete
    @Log(title = "区块", businessType = BusinessType.DELETE)
    public AjaxResult deleteNsRegion(@RequestBody IdNameVo idNameVo) {
        try {
            Long regionId = idNameVo.getId();
            LoginUser loginUser = SpringUtils.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest());
            log.info(CommonUtil.append("/nsRegion/nsRegionDelete start, regionId:", regionId));
            return AjaxResult.success(nsRegionService.deleteNsRegion(regionId));
        } catch (Throwable t) {
            log.error("nsRegionDelete error!", t);
            throw t;
        }
    }

    /**
     * 新增或保存（区块）<br>
     * ID 为空即为新增，否则为更新
     * @author: TanShunFu at jorchi
     * @date: 2022-11-09 14:14:51
     */
    @PreAuthorize("@ss.hasPermi('business:nsRegion:edit')")
    @PostMapping("/nsRegionSave") // {{baseApi}}/nsRegion/nsRegionSave
    @Log(title = "区块", businessType = BusinessType.UPDATE)
    public AjaxResult saveNsRegion (@RequestBody NsRegion nsRegion) {
        try {
            // 获取当前的用户
            LoginUser loginUser = SpringUtils.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest());
            log.info(CommonUtil.append("/nsRegion/nsRegionSave start, RequestBody:", nsRegion));

            // 检查待保存数据的合法性
            validatePo(nsRegion);

            // TODO : 参数非空性检查 ...

            // 名称唯一性检查?
            // nsRegionService.checkNameExisted(nsRegion);

            NsRegion result = nsRegionService.saveNsRegion(nsRegion);
            return AjaxResult.success(result);
        } catch (Throwable t) {
            log.error("nsRegionSave error!", t);
            throw t;
        }
    }

    /**
     * 检查待保存数据的合法性（区块）<br>
     * @author:  TanShunFu at jorchi
     * @date: 2022-11-09 14:14:51
     */
    protected void validatePo(NsRegion nsRegion) {

        // 区块名称-长度检查
        if (nsRegion.getRegionName() != null && nsRegion.getRegionName().length() > 128)
            throw ClientException.of("【区块名称】字段最多只能输入【128】个字符！");

        // 区块面积-长度检查
        if (nsRegion.getRegionArea() != null && nsRegion.getRegionArea().length() > 32)
            throw ClientException.of("【区块面积】字段最多只能输入【32】个字符！");

        // 区域负责人姓名-长度检查
        if (nsRegion.getRegionLeaderName() != null && nsRegion.getRegionLeaderName().length() > 128)
            throw ClientException.of("【区域负责人姓名】字段最多只能输入【128】个字符！");

        // 区域坐标点-长度检查
        if (nsRegion.getRegionPoints() != null && nsRegion.getRegionPoints().length() > 1000)
            throw ClientException.of("【区域坐标点】字段最多只能输入【1000】个字符！");

        // TODO : 其它必填项检查？
        // if (releaseVersion.getName() == null || releaseVersion.getName().trim().isEmpty())
        //     throw ClientException.of("【name】字段不能为空！");
    }

}
