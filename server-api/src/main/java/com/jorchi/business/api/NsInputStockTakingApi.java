package com.jorchi.business.api;

import com.jorchi.business.form.SaveInputStockTakingForm;
import com.jorchi.business.po.NsInputStockTaking;
import com.jorchi.business.service.NsInputStockTakingService;
import com.jorchi.business.form.NsInputStockTakingForm;
import com.jorchi.common.utils.ServletUtils;
import com.jorchi.common.utils.spring.SpringUtils;
import com.jorchi.framework.aspectj.lang.annotation.Log;
import com.jorchi.framework.aspectj.lang.enums.BusinessType;
import com.jorchi.framework.security.LoginUser;
import com.jorchi.framework.security.service.TokenService;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.framework.web.page.TableDataInfo;
import com.jorchi.server.vo.IdNameVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import pdfc.claim.common.ClientException;
import pdfc.claim.common.CommonUtil;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * 表ns_input_stock_taking的控制层对象<br/>
 * 对应表名：ns_input_stock_taking，表备注：投入品库存盘点表
 * @author: 周建宇 at jorchi
 * @date: 2025-01-09 09:35:47
 */
@Slf4j
@RestController
@RequestMapping("/nsInputStockTaking")
public class NsInputStockTakingApi extends BaseApi {

    @Resource
    NsInputStockTakingService nsInputStockTakingService;


    /**
     * 按主键查询（投入品库存盘点表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-09 09:35:47
     */
    @PreAuthorize("@ss.hasPermi('business:nsInputStockTaking:list')")
    @PostMapping("/nsInputStockTakingDtl") // {{baseApi}}/nsInputStockTaking/nsInputStockTakingDtl
    public AjaxResult findById(@RequestBody IdNameVo idNameVo) {
        try {
            Long takingId = idNameVo.getId();
            // id can not be empty
            if (takingId == null)
                throw ClientException.of("findById but takingId can not be empty error!");

            NsInputStockTaking bean = nsInputStockTakingService.findById(takingId);
            if (bean == null)
                throw ClientException.of("NsInputStockTaking not found error!");
            return AjaxResult.success(bean);
        } catch (Throwable t) {
            log.error("nsInputStockTakingDtl error!", t);
            throw t;
        }
    }

    /**
     * 分页查询（投入品库存盘点表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-09 09:35:47
     */
    @PreAuthorize("@ss.hasPermi('business:nsInputStockTaking:list')")
    @PostMapping("/nsInputStockTakingList") // {{baseApi}}/nsInputStockTaking/nsInputStockTakingList
    public TableDataInfo listPage(@RequestBody NsInputStockTakingForm nsInputStockTakingVo) {
        try {
            // 分页参数检查
            if (nsInputStockTakingVo.getPageNum() == null) {
                log.warn("/nsInputStockTakingList please set pageNum!!");
                nsInputStockTakingVo.setPageNum(1);
            }
            if (nsInputStockTakingVo.getPageSize() == null) {
                log.warn("/nsInputStockTakingList please set pageSize!!");
                nsInputStockTakingVo.setPageSize(10);
            }

            return getDataTable(nsInputStockTakingService.listPage(nsInputStockTakingVo));
        } catch (Throwable t) {
            log.error("nsInputStockTakingList listPage error!", t);
            throw t;
        }
    }

    /**
     * 删除（投入品库存盘点表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-09 09:35:47
     */
    @PreAuthorize("@ss.hasPermi('business:nsInputStockTaking:remove')")
    @PostMapping("/nsInputStockTakingDelete") // {{baseApi}}/nsInputStockTaking/nsInputStockTakingDelete
    @Log(title = "投入品库存盘点表", businessType = BusinessType.DELETE)
    public AjaxResult deleteNsInputStockTaking(@RequestBody IdNameVo idNameVo) {
        try {
            Long takingId = idNameVo.getId();
            LoginUser loginUser = SpringUtils.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest());
            log.info(CommonUtil.append("/nsInputStockTaking/nsInputStockTakingDelete start, takingId:", takingId));
            return AjaxResult.success(nsInputStockTakingService.deleteNsInputStockTaking(takingId));
        } catch (Throwable t) {
            log.error("nsInputStockTakingDelete error!", t);
            throw t;
        }
    }

    /**
     * 库存盘点提交接口<br>
     * 支持完全更新库存并生成盘点记录
     * @author: 周建宇 at jorchi
     * @date: 2025-09-21
     */
    @PreAuthorize("@ss.hasPermi('business:nsInputStockTaking:edit')")
    @PostMapping("/nsInputStockTakingSave") // {{baseApi}}/nsInputStockTaking/nsInputStockTakingSave
    @Log(title = "投入品库存盘点表", businessType = BusinessType.UPDATE)
    public AjaxResult saveNsInputStockTaking(@RequestBody SaveInputStockTakingForm.InputStockTakingForm form) {
        try {
            // 获取当前的用户
            LoginUser loginUser = SpringUtils.getBean(TokenService.class).getLoginUser(ServletUtils.getRequest());
            log.info(CommonUtil.append("/nsInputStockTaking/nsInputStockTakingSave start, RequestBody:", form));

            if (form == null) {
                throw ClientException.of("请求参数不能为空！");
            }

            if (form.getHouseId() == null) {
                throw ClientException.of("仓库ID不能为空！");
            }

            if (form.getTakingOperator() == null || form.getTakingOperator().trim().isEmpty()) {
                throw ClientException.of("盘点人不能为空！");
            }

            if (form.getTakingDate() == null || form.getTakingDate().trim().isEmpty()) {
                throw ClientException.of("盘点日期不能为空！");
            }

            if (form.getRegionDeptId() == null || form.getRegionDeptId().trim().isEmpty()) {
                throw ClientException.of("农场ID不能为空！");
            }

            if (CollectionUtils.isEmpty(form.getStockItems())) {
                throw ClientException.of("库存项目不能为空！");
            }

            // 验证库存项目数据
            validateStockItems(form.getStockItems());

            nsInputStockTakingService.saveInputStockTaking(form);
            return AjaxResult.success();
        } catch (Throwable t) {
            log.error("nsInputStockTakingSave error!", t);
            throw t;
        }
    }





    /**
     * 验证库存项目数据的合法性
     */
    private void validateStockItems(List<SaveInputStockTakingForm.StockItemForm> stockItems) {
        for (SaveInputStockTakingForm.StockItemForm item : stockItems) {
            if (item.getBusinessId() == null) {
                throw ClientException.of("业务ID不能为空！");
            }

            if (item.getInputType() == null) {
                throw ClientException.of("投入品类型不能为空！");
            }

            if (item.getTakingStockQuantity() == null) {
                throw ClientException.of("盘点数量不能为空！");
            }

            if (item.getTakingStockQuantity().compareTo(BigDecimal.ZERO) < 0) {
                throw ClientException.of("盘点数量不能为负数！");
            }
        }
    }

}
