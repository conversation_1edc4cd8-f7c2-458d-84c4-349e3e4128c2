package com.jorchi.server.api;


import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.jorchi.common.constant.CommonDef;
import com.jorchi.framework.interceptor.DangerActionHandlerInterceptor;
import com.jorchi.project.system.dao.SystemVarDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import pdfc.claim.common.CommonUtil;

import javax.annotation.Resource;
import java.util.HashMap;


/**
 * 服务结点健康状态检测接口
 *
 * <AUTHOR> Sugar.Tan
 * @date : 2021-03-21 16:36
 */
@RestController
@Slf4j
public class HelloApi {

    /** 系统变量 dao 接口 */
    @Resource
    SystemVarDao systemVarDao;

    /** 本服务结点名称 */
    @Value("${spring.application.name:api}")
    String name;

    /** 本服务端口 */
    @Value("${server.port:8080}")
    String port;

    /** redis 工具类 */
    @Resource
    public RedisTemplate redisTemplate;


    @Autowired
    private DangerActionHandlerInterceptor dangerActionHandlerInterceptor;

    /** 网络请求工具 RestTemplate */
    RestTemplate restTemplate = restTemplate();
    public RestTemplate restTemplate(){
        RestTemplate restTemplate = new RestTemplate(new SimpleClientHttpRequestFactory(){{
            // 设置连接超时 (3's)
            setConnectTimeout(2000);
            // 设置读超时 (2's)
            setReadTimeout(2000);
            //  通过代理
            // setProxy(new java.net.Proxy(Type.HTTP, new InetSocketAddress("***********", 7070)));
        }});
        return restTemplate;
    }

    /**
     * 本内网集群其它服务结点的健康检测地址
     */
    private static HashMap<String, String> NODE_URL = new HashMap<>();
    static {
        // 移动端 h5
        NODE_URL.put("H5001", "http://***********:8081/ihome-h5/index.html");
        NODE_URL.put("H5002", "http://***********:8081/ihome-h5/index.html");
        NODE_URL.put("H5003", "http://***********:8081/ihome-h5/index.html");

        // 影像
        NODE_URL.put("IMG001", "http://************:8082/ihome-image/hello");

        // MQ
        NODE_URL.put("MQ002", "http://*************:8080/ihome-api/hello");
        NODE_URL.put("MQ003", "http://*************:8080/ihome-api/hello");
    }

    /**
     * 服务健康状态检查接口（该接口请在过滤器中放行）
     * // http://localhost:7088/hello
     * // http://localhost:7088/hello?nodeName=APP001
     * @param nodeName 如果 nodeName 非空，则认为是检测本集群中其它结点
     *
     * @author: tanshunfu
     * @date: 2023-04-15 15:34
     */
    @RequestMapping("/hello")
    public JsonObject hello(@RequestParam(required = false) String nodeName) {
        /*if (port.equals("8083")) {
            // 休眠4秒模拟本服务已宕机
            try { Thread.sleep(4500); } catch (Exception e) { }
        }*/

        // nodeName为空，则检测本结点
        if (nodeName == null || nodeName.trim().isEmpty()) {
            // 内存、磁盘、CPU 状态检查
            try {
                MonitorChecker.doHealthyCheck();
            } catch (Throwable e) {
                return error(e.getMessage());
            }

            // 访问一下数据库，确保数据库链接正常
            try {
                systemVarDao.selectByPrimaryKey(2);
            } catch (Throwable e) {
                return error("call db error!!!");
            }

            // 访问一下Redis，确保数据库链接正常
            try {
                redisTemplate.opsForValue().set("iFarm:isRedisHealthy", 1);
                redisTemplate.delete("iFarm:isRedisHealthy");
            } catch (Throwable e) {
                return error("call redis error!!!");
            }

            // 如果还有其它第3方应用，在这里添加 ...

            // 一切正常
            return success(CommonUtil.append("HELLO, my name is:", name, ", my port is:", port, " ", CommonDef.SERVER_VERSION));
        }

        // 通过内网访问 nodeName结点的健康状态的URL
        String internalUrl = NODE_URL.get(nodeName);
        if (internalUrl == null) {
            return error("nodeName not found");
        }

        // 防止死循环（内网的url不应该再包含有 nodeName）
        if (internalUrl.contains("nodeName"))
            return error("nodeUrl can not contains nodeName!!!");

        // 通过 restTemplate 访问内网的结点
        String nodeResponse;
        try {
            ResponseEntity<String> responseEntity = restTemplate.getForEntity(internalUrl, String.class);
            if (responseEntity.getStatusCode() != HttpStatus.OK) {
                log.warn(CommonUtil.append("hunt hello is not ok, statusCode:", responseEntity.getStatusCode(),
                        " res:", responseEntity.getBody()));
                return error(CommonUtil.append("errorCode:", responseEntity.getStatusCode()));
            }
            nodeResponse = responseEntity.getBody();
        } catch (Throwable e) {
            return error(CommonUtil.append("node io exception!! node:", nodeName));
        }

        JsonObject jsonData = new Gson().fromJson(nodeResponse, JsonObject.class);
        return jsonData;
    }

    /**
     * 结点状态运行正常的消息回应
     *
     * @author: tanshunfu
     * @date: 2023-04-15 15:50
     */
    private JsonObject success(String message) {
        JsonObject result = new JsonObject();
        result.addProperty("code", 200);
        result.addProperty("message", message);
        return result;
    }

    /**
     * 结点状态运行异常的消息回应
     *
     * @author: tanshunfu
     * @date: 2023-04-15 15:50
     */
    private JsonObject error(String message) {
        JsonObject result = new JsonObject();
        result.addProperty("code", 400);
        result.addProperty("message", message);
        return result;
    }

    /**
     * 日志打印：【每种策略，1分钟最高被访问了多少次，5分钟最高被访问了多少次，一天最高被访问了多少次】
     * // http://localhost:8082/ihome-api/test/getMaxCalledCount
     * @author: tanshunfu
     * @date: 2023-05-12 12:18
     */
    @RequestMapping("/test/getMaxCalledCount")
    public Object getMaxCalledCount() {
        log.info(dangerActionHandlerInterceptor.getMaxCalledCount());
        return success("ok");
    }
}
