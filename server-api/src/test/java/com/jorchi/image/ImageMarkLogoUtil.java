package com.jorchi.image;

import lombok.extern.slf4j.Slf4j;

import java.awt.AlphaComposite;
import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics2D;
import java.awt.Image;
import java.awt.RenderingHints;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;

import javax.imageio.ImageIO;
import javax.swing.ImageIcon;

/**
 * 图片水印工具类
 *
 * <AUTHOR> Sugar.Tan
 * @date : 2021-07-15 14:58
 */
@Slf4j
public class ImageMarkLogoUtil {

    /** 水印透明度 */
    private static float alpha = 0.9f;
    /** 水印横向位置 */
    private static int positionWidth = 50;
    /** 水印纵向位置 */
    private static int positionHeight = 50;
    /** 水印文字字体 */
    private static Font font = new Font("宋体", Font.BOLD, 30);
    /** 水印文字颜色 */
    private static Color color = Color.red;

    /**
     *
     * @param alpha
     *          水印透明度
     * @param positionWidth
     *          水印横向位置
     * @param positionHeight
     *          水印纵向位置
     * @param font
     *          水印文字字体
     * @param color
     *          水印文字颜色
     */
    public static void setImageMarkOptions(float alpha, int positionWidth, int positionHeight, Font font,Color color){
        if(alpha!=0.0f)ImageMarkLogoUtil.alpha = alpha;
        if(positionWidth!=0)ImageMarkLogoUtil.positionWidth = positionWidth;
        if(positionHeight!=0)ImageMarkLogoUtil.positionHeight = positionHeight;
        if(font!=null)ImageMarkLogoUtil.font = font;
        if(color!=null)ImageMarkLogoUtil.color = color;
    }

    /**
     * 给图片添加水印图片
     *
     * @param iconPath
     *            水印图片路径
     * @param srcImgPath
     *            源图片路径
     * @param targerPath
     *            目标图片路径
     */
    public static void markImageByIcon(String iconPath, String srcImgPath,
                                       String targerPath) {
        markImageByIcon(iconPath, srcImgPath, targerPath, null);
    }

    /**
     * 给图片添加水印图片、可设置水印图片旋转角度
     *
     * @param iconPath
     *            水印图片路径
     * @param srcImgPath
     *            源图片路径
     * @param targerPath
     *            目标图片路径
     * @param degree
     *            水印图片旋转角度
     */
    public static void markImageByIcon(String iconPath, String srcImgPath,
                                       String targerPath, Integer degree) {
        OutputStream os = null;
        try {

            Image srcImg = ImageIO.read(new File(srcImgPath));

            BufferedImage buffImg = new BufferedImage(srcImg.getWidth(null),
                    srcImg.getHeight(null), BufferedImage.TYPE_INT_RGB);

            // 1、得到画笔对象
            Graphics2D g = buffImg.createGraphics();

            // 2、设置对线段的锯齿状边缘处理
            g.setRenderingHint(RenderingHints.KEY_INTERPOLATION,RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            g.drawImage(srcImg.getScaledInstance(srcImg.getWidth(null), srcImg.getHeight(null), Image.SCALE_SMOOTH), 0, 0, null);
            // 3、设置水印旋转
            if (null != degree) {
                g.rotate(Math.toRadians(degree),(double) buffImg.getWidth() / 2, (double) buffImg.getHeight() / 2);
            }

            // 4、水印图片的路径 水印图片一般为gif或者png的，这样可设置透明度(本地地址可直接填写,服务器地址ImageIcon导包位置一定要正确)
            ImageIcon imgIcon = new ImageIcon(iconPath);

            // 5、得到Image对象。
            Image img = imgIcon.getImage();

            g.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_ATOP,alpha));

            // 6、水印图片的位置
            g.drawImage(img, positionWidth, positionHeight, null);
            g.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER));
            // 7、释放资源
            g.dispose();

            // 8、生成图片
            os = new FileOutputStream(targerPath);
            ImageIO.write(buffImg, "JPG", os);

            log.info("图片完成添加水印图片");
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (null != os)
                    os.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 给图片添加水印文字
     * @param logoText：水印文字
     * @param srcImgPath：源图片路径
     * @param targerPath ： 目标图片路径
     */
    public static void markImageByText(String logoText, String srcImgPath,
                                       String targerPath) {
        markImageByText(logoText, srcImgPath, targerPath, null);
    }

    /**
     * 给图片添加水印文字、可设置水印文字的旋转角度
     *
     * @param logoText
     * @param srcImgPath
     * @param targerPath
     * @param degree
     */
    public static void markImageByText(String logoText, String srcImgPath,
                                       String targerPath, Integer degree) {
        InputStream is = null;
        OutputStream os = null;
        try {
            // 1、源图片
            Image srcImg = ImageIO.read(new File(srcImgPath));
            BufferedImage buffImg = new BufferedImage(srcImg.getWidth(null),srcImg.getHeight(null), BufferedImage.TYPE_INT_RGB);

            // 2、得到画笔对象
            Graphics2D graphics2D = buffImg.createGraphics();
            // 3、设置对线段的锯齿状边缘处理
            graphics2D.setRenderingHint(RenderingHints.KEY_INTERPOLATION,RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            graphics2D.drawImage(srcImg.getScaledInstance(srcImg.getWidth(null), srcImg.getHeight(null), Image.SCALE_SMOOTH), 0, 0, null);
            // 4、设置水印旋转
            if (null != degree) {
                graphics2D.rotate(Math.toRadians(degree),(double) buffImg.getWidth() / 2, (double) buffImg.getHeight() / 2);
            }
            // 5、设置水印文字颜色
            graphics2D.setColor(color);
            // 6、设置水印文字Font
            graphics2D.setFont(font);
            // 7、设置水印文字透明度
            graphics2D.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_ATOP, alpha));
            // 8、第一参数->设置的内容，后面两个参数->文字在图片上的坐标位置(x,y)
            //g.drawString(logoText, positionWidth, positionHeight);
            // 第1行，在顶部
            graphics2D.drawString(logoText, 50, 50);
            // 第2行，在顶部
            graphics2D.drawString(logoText, 50, buffImg.getHeight() - 50);
            // 9、释放资源
            graphics2D.dispose();
            // 10、生成图片
            os = new FileOutputStream(targerPath);
            ImageIO.write(buffImg, "JPG", os);

            log.info("图片完成添加水印文字");

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (null != is)
                    is.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                if (null != os)
                    os.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public static void main(String [] args){
        // 源图片路径
        String srcImgPath = "D:\\tmp\\img\\1.jpg";
        // 目标图片路径
        String targerIconPath2 = "D:\\tmp\\img\\hecheng.jpg";
        // 水印文字
        String logoText = "时间：2021.07.15 14:48  地点：浙江省杭州市中河中路66号";

        //可通过此方法调试间距,颜色.不适用则默认初始化数据
        setImageMarkOptions(0.9f,250,100,null,null);

        /**给图片添加水印图片,水印图片旋转-45*/
//        markImageByIcon(iconPath, srcImgPath, targerIconPath2, 0);
        /**给图片添加水印图片,水印图片旋转-45  平行*/
//        markImageByIcon(iconPath, srcImgPath, targerIconPath2,45);
        /**给图片添加水印文字,水印图片旋转  平行*/
//        markImageByText(logoText, srcImgPath, targerIconPath2);
        /**给图片添加水印文字,水印图片旋转-45*/
        markImageByText(logoText, srcImgPath, targerIconPath2, null);
    }
}
