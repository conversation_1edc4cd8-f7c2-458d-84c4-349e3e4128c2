package com.jorchi.server;


import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.jorchi.ApplicationFast;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.server.dao.TRichTextBakDao;
import com.jorchi.server.po.TRichTextBak;
import com.jorchi.server.service.RichTextService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.CommonUtil;

import javax.annotation.Resource;
import java.util.List;


@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class RichTextTest {

    @Autowired
    RichTextService richTextService;

    @Autowired
    MaxIdServiceImpl maxIdService;

    @Resource
    TRichTextBakDao richTextBakDao;

    /**
     * 保存富文本测试
     */
    @Test
    public void insertTest() {
        // long uniqueId = maxIdService.getAndIncrement(CommonDef.UNIQUE_ID);
        long uniqueId = 3;

        /*richTextService.doSaveRichText(uniqueId, "TanShunFu", "中华人民共和国5<img src=\"http://www.baidu.com/alskjflkqwerh.jpg\"></img>");
        richTextService.doSaveRichText(uniqueId, "TanShunFu", "中华人民共和国6<img src=\"http://www.baidu.com/alskjflkqwerh.jpg\"></img>");
        richTextService.doSaveRichText(uniqueId, "TanShunFu", "中华人民共和国7<img src=\"http://www.baidu.com/alskjflkqwerh.jpg\"></img>");*/

        for (int i=0; i<10; i++) {
            richTextService.doSaveRichText(uniqueId, "TanShunFu", CommonUtil.append("中华人民共和国 T:", i));
        }
    }

    /**
     * 翻页测试
     */
    @Test
    public void selectPageTest() {
        PageHelper.startPage(1, 10, null);
        List<TRichTextBak> list = richTextBakDao.selectPage(new TRichTextBak());
        log.info(CommonUtil.append("total:", ((Page)list).getTotal())); // 总条数
        log.info(list.toString());
    }
}
