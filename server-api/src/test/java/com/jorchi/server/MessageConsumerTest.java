package com.jorchi.server;

import com.jorchi.ApplicationFast;
import com.jorchi.server.po.TMessageConsumer;
import com.jorchi.server.service.MessageConsumerService;
import com.jorchi.server.task.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;


/**
 * <AUTHOR> Sugar.Tan
 * @date : 2022-06-17 17:17
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class MessageConsumerTest {

    @Autowired
    MessageConsumerService messageConsumerService;

    @Autowired
    DayStartTask dayStartTask;

    @Autowired
    HarvestTask harvestTask;

    @Autowired
    MapUpdateTask mapUpdateTask;

    @Autowired
    DeviceStatusTask deviceStatusTask;

    @Autowired
    DeleteDataByTimeTask deleteDataByTimeTask;

    @Autowired
    DbBarkTask dbBarkTask;

    @Autowired
    CheckRestartTask checkRestartTask;
    /**
     * findByTypeValue
     */
    @Test
    public void findByTypeValue() {
        TMessageConsumer bean = messageConsumerService.findByTypeValue("LI_PEI", "315");
        log.info(bean.toString());
    }

    /**
     * 生产者
     */
    @Test
    public void doProduct() throws Exception {
        // boolean isSaveOk = messageConsumerService.tryProduct(MessageConsumerService.LOCK_TYPE.IMPORT_CENT_TASK);
//        boolean isSaveOk = messageConsumerService.tryProduct(MessageConsumerService.LOCK_TYPE.DAY_END_TASK);
//        boolean isSaveOk = messageConsumerService.tryProduct(MessageConsumerService.LOCK_TYPE.DAY_START_TASK);
//        boolean isSaveOk = messageConsumerService.tryProduct(MessageConsumerService.LOCK_TYPE.MAP_UPDATE_TASK);
//        boolean isSaveOk = messageConsumerService.tryProduct(MessageConsumerService.LOCK_TYPE.HARVEST_TASK);
//        boolean isSaveOk = messageConsumerService.tryProduct(MessageConsumerService.LOCK_TYPE.DATA_DELETE_TASK);
//        boolean isSaveOk = messageConsumerService.tryProduct(MessageConsumerService.LOCK_TYPE.DB_BARK_TASK);
//        boolean isSaveOk = messageConsumerService.tryProduct(MessageConsumerService.LOCK_TYPE.CHECK_RESTART_TASK);
        boolean isSaveOk = messageConsumerService.tryProduct(MessageConsumerService.LOCK_TYPE.PLAN_UPDATE_TASK);
        //if (isSaveOk)
        //    isSaveOk = messageConsumerService.tryProduct(MessageConsumerService.PUSH_FEI_CHI_LI_PEI, "315");
        System.out.println(isSaveOk);
        //doConsumer();
    }

    /**
     * 生产者
     */
    @Test
    public void doProduct2() throws Exception {
        boolean isSaveOk = messageConsumerService.tryProduct(MessageConsumerService.LOCK_TYPE.IMPORT_CENT_TASK, "2");
        //if (isSaveOk)
        //    isSaveOk = messageConsumerService.tryProduct(MessageConsumerService.PUSH_FEI_CHI_LI_PEI, "315");
        System.out.println(isSaveOk);
        //doConsumer();
    }

    /**
     * 消费者
     */
    @Test
    public void doConsumer1() throws Exception {
        TMessageConsumer locked = messageConsumerService.tryLock(MessageConsumerService.LOCK_TYPE.IMPORT_CENT_TASK);
        if (locked != null) // 释放锁
            messageConsumerService.unLock(locked, true);
    }

    /**
     * 消费者
     */
    @Test
    public void doConsumer2() throws Exception {
        TMessageConsumer locked = messageConsumerService.tryLock(MessageConsumerService.LOCK_TYPE.IMPORT_CENT_TASK, "2");
        if (locked != null) // 释放锁
            messageConsumerService.unLock(locked, true);
    }

    /**
     * 消费者
     */
    @Test
    public void doConsumer() throws Exception {
        String messageId = "19";
        // try to get lock by my nodeName
        TMessageConsumer locked = messageConsumerService.tryLock(messageId, "app-001");
        if (locked != null) {
            // do something ...
            messageConsumerService.unLock(locked, false);
            locked = messageConsumerService.tryLock(messageId, "app-001");
            System.out.println("locked:"+locked);

            messageConsumerService.unLock(locked, false);
            locked = messageConsumerService.tryLock(messageId, "app-001");
            System.out.println("locked:"+locked);

            messageConsumerService.unLock(locked, false);
            locked = messageConsumerService.tryLock(messageId, "app-001");
            messageConsumerService.unLock(locked, false);
            locked = messageConsumerService.tryLock(messageId, "app-001");

            locked = messageConsumerService.tryLock(messageId, "app-001");
            locked = messageConsumerService.tryLock(messageId, "app-001");
            // messageConsumerService.unLock(messageId, true);
        }
    }

    @Test
    public void dayStartTaskTest() {
        dayStartTask.doDayStart();
    }

    @Test
    public void HarvestTaskTest(){
        harvestTask.doHarvestTask();
    }

    @Test
    public void MapUpdateTask(){
        mapUpdateTask.doMapTask();
    }

    @Test
    public void DeviceStatusTask(){
        deviceStatusTask.doUpdateDeviceStatus();
    }

    @Test
    public void DeleteDataTask(){
        deleteDataByTimeTask.doDeleteDataTask();
    }

    @Test
    public void DbTask(){
        dbBarkTask.doDbBarkTask();
    }

    @Test
    public void CheckRestart(){
        checkRestartTask.doCheckRestartTask();
    }
}
