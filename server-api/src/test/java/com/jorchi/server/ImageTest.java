package com.jorchi.server;

import com.jorchi.ApplicationFast;
import com.jorchi.project.image.dao.TImageDao;
import com.jorchi.project.image.dao.TImageTmpDao;
import com.jorchi.project.image.po.TImageTmp;
import com.jorchi.project.image.service.ImageService;
import com.jorchi.server.task.DayEndTask;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.CommonUtil;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 附件模块测试
 *
 * <AUTHOR> Sugar.Tan
 * @date : 2022-08-25 10:17
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class ImageTest {

    @Resource
    ImageService imageService;

    @Resource
    TImageTmpDao imageTmpDao;

    @Resource
    TImageDao imageDao;

    @Autowired
    DayEndTask dayEndTask;

    @Test
    public void list() {
        Date overDue = new Date();

        // 删除过期临时图片
        List<TImageTmp> overDueImageTmpList = imageTmpDao.selectOverDue(overDue);
        /*if (!overDueImageTmpList.isEmpty()) {
            imageService.removeToBackup(overDueImageTmpList.get(0), true);
            log.info(CommonUtil.append("remove ok? overDueImageTmpList[0]", overDueImageTmpList.get(0).toString()));
            return;
        }*/

        // 删除过期临时图片
        overDueImageTmpList = imageDao.selectOverDue(overDue);
        if (!overDueImageTmpList.isEmpty()) {
            imageService.removeToBackup(overDueImageTmpList.get(0), false);
            log.info(CommonUtil.append("remove ok? overDueImageTmpList[0]", overDueImageTmpList.get(0).toString()));
            return;
        }
    }

    @Test
    public void dayEndTest() {
        dayEndTask.doDayEnd();
    }
}
