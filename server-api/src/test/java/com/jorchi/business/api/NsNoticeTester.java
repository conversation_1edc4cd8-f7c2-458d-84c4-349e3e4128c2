package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsNotice;
import com.jorchi.business.service.NsNoticeService;
import com.jorchi.business.form.NsNoticeForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.util.Date;

/**
 * ns_notice-模块测试<br/>
 * 对应表名：ns_notice，表备注：小程序首页公告
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON> at jorchi
 * @date: 2022-11-28 17:11:56
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsNoticeTester extends BaseApi {

    @Resource
    NsNoticeService nsNoticeService;

    @Resource
    NsNoticeApi nsNoticeApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（小程序首页公告）<br>
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-28 17:11:56
     */
    @Test
    public void findByIdNsNoticeTest() {
        Long noticeId;
        noticeId = 1L;

        NsNotice bean = nsNoticeService.findById(noticeId);
        if (bean == null)
            throw ClientException.of("NsNotice not found error!");

        log.info("findById NsNoticeTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（小程序首页公告）<br>
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-28 17:11:56
     */
//    @Test
//    public void listPageNsNoticeTest() {
//        NsNoticeForm nsNoticeVo = new NsNoticeForm();
//        nsNoticeVo.setPageNum(1);
//        nsNoticeVo.setPageSize(10);
//        log.info("listPage NsNoticeTest result:");
//        log.info(getDataTable(nsNoticeService.listPage(nsNoticeVo)).toString());
//    }

    /**
     * 删除测试（小程序首页公告）<br>
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-28 17:11:56
     */
    @Test
    public void deleteNsNoticeTest() {
        Long id = 0L;
        log.info("delete NsNoticeTest result:");
        log.info(AjaxResult.success(nsNoticeService.deleteNsNotice(id)).toString());
    }

    /**
     * 新增或保存测试（小程序首页公告）<br>
     * ID 为空即为新增，否则为更新
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-28 17:11:56
     */
    @Test
    public void saveNsNoticeTest () {
        NsNotice nsNotice = new NsNotice();
        // 主键
        // nsNotice.setNoticeId(0L);
        // 技术人员Id
        // nsNotice.setArtisanId(0L);
        // 技术人员名称
        nsNotice.setArtisanName("技术人员名称");
        // 公告内容
        nsNotice.setSpecifics("公告内容");
        // 创建者
        nsNotice.setCreateBy("创建者");
        // 创建时间
        nsNotice.setCreateTime(new Date());
        // 更新者
        nsNotice.setUpdateBy("更新者");
        // 更新时间
        nsNotice.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsNoticeApi.validatePo(nsNotice);

        // 名称唯一性检查?
        // nsNoticeService.checkNameExisted(nsNotice);

        NsNotice result = nsNoticeService.saveNsNotice(nsNotice);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（小程序首页公告）<br>
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-28 17:11:56
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("小程序首页公告");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsNotice");
        // 组件路径
        menu.setComponent("business/nsNotice/nsNotice");
        // 权限标识
        menu.setPerms("business:nsNotice:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("ChenLiFeng");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("小程序首页公告", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "小程序首页公告-查询", 1, "business:nsNotice:list");
            addOperationMenu(menu.getMenuId(), "小程序首页公告-新增", 2, "business:nsNotice:add");
            addOperationMenu(menu.getMenuId(), "小程序首页公告-修改", 3, "business:nsNotice:edit");
            addOperationMenu(menu.getMenuId(), "小程序首页公告-删除", 4, "business:nsNotice:remove");
            addOperationMenu(menu.getMenuId(), "小程序首页公告-导出", 5, "business:nsNotice:export");
            addOperationMenu(menu.getMenuId(), "小程序首页公告-导入", 6, "business:nsNotice:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsNotice ok");
        }
    }

    /**
     * 创建操作按钮权限（小程序首页公告）<br>
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-28 17:11:56
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（小程序首页公告）<br>
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-28 17:11:56
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("小程序首页公告", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsNotice menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
