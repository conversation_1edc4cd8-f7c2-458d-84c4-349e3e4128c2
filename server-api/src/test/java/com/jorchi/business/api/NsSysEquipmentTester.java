package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsSysEquipment;
import com.jorchi.business.service.NsSysEquipmentService;
import com.jorchi.business.form.NsSysEquipmentForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.util.Date;

/**
 * ns_sys_equipment-模块测试<br/>
 * 对应表名：ns_sys_equipment，表备注：设备表
 * @author: xubinbin at jorchi
 * @date: 2023-02-15 15:19:43
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsSysEquipmentTester extends BaseApi {

    @Resource
    NsSysEquipmentService nsSysEquipmentService;

    @Resource
    NsSysEquipmentApi nsSysEquipmentApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（设备表）<br>
     * @author: xubinbin at jorchi
     * @date: 2023-02-15 15:19:43
     */
    @Test
    public void findByIdNsSysEquipmentTest() {
        Long id;
        id = 1L;

        NsSysEquipment bean = nsSysEquipmentService.findById(id);
        if (bean == null)
            throw ClientException.of("NsSysEquipment not found error!");

        log.info("findById NsSysEquipmentTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（设备表）<br>
     * @author: xubinbin at jorchi
     * @date: 2023-02-15 15:19:43
     */
    @Test
    public void listPageNsSysEquipmentTest() {
        NsSysEquipmentForm nsSysEquipmentVo = new NsSysEquipmentForm();
        nsSysEquipmentVo.setPageNum(1);
        nsSysEquipmentVo.setPageSize(10);
        log.info("listPage NsSysEquipmentTest result:");
        log.info(getDataTable(nsSysEquipmentService.listPage(nsSysEquipmentVo)).toString());
    }

    /**
     * 删除测试（设备表）<br>
     * @author: xubinbin at jorchi
     * @date: 2023-02-15 15:19:43
     */
    @Test
    public void deleteNsSysEquipmentTest() {
        Long id = 0L;
        log.info("delete NsSysEquipmentTest result:");
        log.info(AjaxResult.success(nsSysEquipmentService.deleteNsSysEquipment(id)).toString());
    }

    /**
     * 新增或保存测试（设备表）<br>
     * ID 为空即为新增，否则为更新
     * @author: xubinbin at jorchi
     * @date: 2023-02-15 15:19:43
     */
/*    @Test
    public void saveNsSysEquipmentTest () {
        NsSysEquipment nsSysEquipment = new NsSysEquipment();
        // 设备表主键
        // nsSysEquipment.setId(0L);
        // 设备名称
        nsSysEquipment.setDeviceName("设备名称");
        // 设备编号
        nsSysEquipment.setDeviceNodeId("设备编号");
        // 设备类型
        nsSysEquipment.setDeviceType(0L);
        // 最近采集时间
        nsSysEquipment.setCollectDate(new Date());
        // 上线状态：0:离线，1：在线
        nsSysEquipment.setOnlineStatus(0);
        // 所属大棚
        nsSysEquipment.setHouseId(0L);
        // 删除标志（0代表存在2代表删除）
        nsSysEquipment.setDeleted(0);
        // 创建人
        nsSysEquipment.setCreateBy(0L);
        // 创建时间
        nsSysEquipment.setCreateTime(new Date());
        // 更新人
        nsSysEquipment.setUpdateBy(0L);
        // 更新时间
        nsSysEquipment.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsSysEquipmentApi.validatePo(nsSysEquipment);

        // 名称唯一性检查?
        // nsSysEquipmentService.checkNameExisted(nsSysEquipment);

        NsSysEquipment result = nsSysEquipmentService.saveNsSysEquipment(nsSysEquipment);
        log.info("save result:");
        log.info(result.toString());
    }*/


    /**
     * 生成菜单（设备表）<br>
     * @author: xubinbin at jorchi
     * @date: 2023-02-15 15:19:43
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("设备表");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsSysEquipment");
        // 组件路径
        menu.setComponent("business/nsSysEquipment/nsSysEquipment");
        // 权限标识
        menu.setPerms("business:nsSysEquipment:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("xubinbin");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("设备表", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "设备表-查询", 1, "business:nsSysEquipment:list");
            addOperationMenu(menu.getMenuId(), "设备表-新增", 2, "business:nsSysEquipment:add");
            addOperationMenu(menu.getMenuId(), "设备表-修改", 3, "business:nsSysEquipment:edit");
            addOperationMenu(menu.getMenuId(), "设备表-删除", 4, "business:nsSysEquipment:remove");
            addOperationMenu(menu.getMenuId(), "设备表-导出", 5, "business:nsSysEquipment:export");
            addOperationMenu(menu.getMenuId(), "设备表-导入", 6, "business:nsSysEquipment:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsSysEquipment ok");
        }
    }

    /**
     * 创建操作按钮权限（设备表）<br>
     * @author: xubinbin at jorchi
     * @date: 2023-02-15 15:19:43
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（设备表）<br>
     * @author: xubinbin at jorchi
     * @date: 2023-02-15 15:19:43
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("设备表", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsSysEquipment menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
