package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsPurchaseApplicationItem;
import com.jorchi.business.service.NsPurchaseApplicationItemService;
import com.jorchi.business.form.NsPurchaseApplicationItemForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartHttpServletRequest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ns_purchase_application_item-模块测试<br/>
 * 对应表名：ns_purchase_application_item，表备注：采购申请子项表
 * @author: 周建宇 at jorchi
 * @date: 2024-12-25 09:16:59
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsPurchaseApplicationItemTester extends BaseApi {

    @Resource
    NsPurchaseApplicationItemService nsPurchaseApplicationItemService;

    @Resource
    NsPurchaseApplicationItemApi nsPurchaseApplicationItemApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（采购申请子项表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 09:16:59
     */
    @Test
    public void findByIdNsPurchaseApplicationItemTest() {
        Long itemId;
        itemId = 1L;

        NsPurchaseApplicationItem bean = nsPurchaseApplicationItemService.findById(itemId);
        if (bean == null)
            throw ClientException.of("NsPurchaseApplicationItem not found error!");

        log.info("findById NsPurchaseApplicationItemTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（采购申请子项表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 09:16:59
     */
    @Test
    public void listPageNsPurchaseApplicationItemTest() {
        NsPurchaseApplicationItemForm nsPurchaseApplicationItemVo = new NsPurchaseApplicationItemForm();
        nsPurchaseApplicationItemVo.setPageNum(1);
        nsPurchaseApplicationItemVo.setPageSize(10);
        log.info("listPage NsPurchaseApplicationItemTest result:");
        log.info(getDataTable(nsPurchaseApplicationItemService.listPage(nsPurchaseApplicationItemVo)).toString());
    }

    /**
     * 删除测试（采购申请子项表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 09:16:59
     */
    @Test
    public void deleteNsPurchaseApplicationItemTest() {
        Long id = 0L;
        log.info("delete NsPurchaseApplicationItemTest result:");
        log.info(AjaxResult.success(nsPurchaseApplicationItemService.deleteNsPurchaseApplicationItem(id)).toString());
    }

    /**
     * 新增或保存测试（采购申请子项表）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 09:16:59
     */
    @Test
    public void saveNsPurchaseApplicationItemTest () {
        NsPurchaseApplicationItem nsPurchaseApplicationItem = new NsPurchaseApplicationItem();
        // 子项ID
        // nsPurchaseApplicationItem.setItemId(0L);
        // 采购单号
        // nsPurchaseApplicationItem.setPurchaseId(0L);
        // 供应商ID
        nsPurchaseApplicationItem.setSupplierId(0L);
        // 供应商名称
        nsPurchaseApplicationItem.setSupplierName("供应商名称");
        // 投入品大类
        nsPurchaseApplicationItem.setInputType(0);
        // 投入品子类
        nsPurchaseApplicationItem.setInputCategory("投入品子类");
        // 商品ID
        nsPurchaseApplicationItem.setBusinessId(0L);
        // 采购名称
        nsPurchaseApplicationItem.setInputName("采购名称");
        // 生产日期
        nsPurchaseApplicationItem.setProductionDate(new Date());
        // 采购单价
        nsPurchaseApplicationItem.setUnitPrice(new BigDecimal(0));
        // 采购单位
        nsPurchaseApplicationItem.setUnit("采购单位");
        // 数量
        nsPurchaseApplicationItem.setQuantity(new BigDecimal("0"));
        // 小计（unit_price*quantity）
        nsPurchaseApplicationItem.setSubtotal(new BigDecimal(0));
        // 删除标志（0代表存在，2代表删除）
        nsPurchaseApplicationItem.setDeleted(0);
        // 创建人
        nsPurchaseApplicationItem.setCreateBy(0L);
        // 创建时间
        nsPurchaseApplicationItem.setCreateTime(new Date());
        // 更新人
        nsPurchaseApplicationItem.setUpdateBy(0L);
        // 更新时间
        nsPurchaseApplicationItem.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsPurchaseApplicationItemApi.validatePo(nsPurchaseApplicationItem);

        // 名称唯一性检查?
        // nsPurchaseApplicationItemService.checkNameExisted(nsPurchaseApplicationItem);

        NsPurchaseApplicationItem result = nsPurchaseApplicationItemService.saveNsPurchaseApplicationItem(nsPurchaseApplicationItem);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（采购申请子项表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 09:16:59
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("采购申请子项表");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsPurchaseApplicationItem");
        // 组件路径
        menu.setComponent("business/nsPurchaseApplicationItem/nsPurchaseApplicationItem");
        // 权限标识
        menu.setPerms("business:nsPurchaseApplicationItem:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("周建宇");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("采购申请子项表", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "采购申请子项表-查询", 1, "business:nsPurchaseApplicationItem:list");
            addOperationMenu(menu.getMenuId(), "采购申请子项表-新增", 2, "business:nsPurchaseApplicationItem:add");
            addOperationMenu(menu.getMenuId(), "采购申请子项表-修改", 3, "business:nsPurchaseApplicationItem:edit");
            addOperationMenu(menu.getMenuId(), "采购申请子项表-删除", 4, "business:nsPurchaseApplicationItem:remove");
            addOperationMenu(menu.getMenuId(), "采购申请子项表-导出", 5, "business:nsPurchaseApplicationItem:export");
            addOperationMenu(menu.getMenuId(), "采购申请子项表-导入", 6, "business:nsPurchaseApplicationItem:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsPurchaseApplicationItem ok");
        }
    }

    /**
     * 创建操作按钮权限（采购申请子项表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 09:16:59
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（采购申请子项表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 09:16:59
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("采购申请子项表", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsPurchaseApplicationItem menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
