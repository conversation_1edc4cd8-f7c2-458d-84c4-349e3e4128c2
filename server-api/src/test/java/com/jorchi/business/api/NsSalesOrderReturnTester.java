package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsSalesOrderReturn;
import com.jorchi.business.service.NsSalesOrderReturnService;
import com.jorchi.business.form.NsSalesOrderReturnForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartHttpServletRequest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ns_sales_order_return-模块测试<br/>
 * 对应表名：ns_sales_order_return，表备注：销售退库表
 * @author: 周建宇 at jorchi
 * @date: 2025-01-14 14:51:34
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsSalesOrderReturnTester extends BaseApi {

    @Resource
    NsSalesOrderReturnService nsSalesOrderReturnService;

    @Resource
    NsSalesOrderReturnApi nsSalesOrderReturnApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（销售退库表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-14 14:51:34
     */
    @Test
    public void findByIdNsSalesOrderReturnTest() {
        Long returnId;
        returnId = 1L;

        NsSalesOrderReturn bean = nsSalesOrderReturnService.findById(returnId);
        if (bean == null)
            throw ClientException.of("NsSalesOrderReturn not found error!");

        log.info("findById NsSalesOrderReturnTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（销售退库表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-14 14:51:34
     */
    @Test
    public void listPageNsSalesOrderReturnTest() {
        NsSalesOrderReturnForm nsSalesOrderReturnVo = new NsSalesOrderReturnForm();
        nsSalesOrderReturnVo.setPageNum(1);
        nsSalesOrderReturnVo.setPageSize(10);
        log.info("listPage NsSalesOrderReturnTest result:");
        log.info(getDataTable(nsSalesOrderReturnService.listPage(nsSalesOrderReturnVo)).toString());
    }

    /**
     * 删除测试（销售退库表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-14 14:51:34
     */
    @Test
    public void deleteNsSalesOrderReturnTest() {
        Long id = 0L;
        log.info("delete NsSalesOrderReturnTest result:");
        log.info(AjaxResult.success(nsSalesOrderReturnService.deleteNsSalesOrderReturn(id)).toString());
    }

    /**
     * 新增或保存测试（销售退库表）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2025-01-14 14:51:34
     */
    @Test
    public void saveNsSalesOrderReturnTest () {
        NsSalesOrderReturn nsSalesOrderReturn = new NsSalesOrderReturn();
        // 退货id
        // nsSalesOrderReturn.setReturnId(0L);
        // 申请人
        nsSalesOrderReturn.setApplicant("申请人");
        // 退货日期
        nsSalesOrderReturn.setReturnDate(new Date());
        // 申请人手机号
        nsSalesOrderReturn.setApplicantMobile("申请人手机号");
        // 申请人ID
        nsSalesOrderReturn.setApplicantId(0L);
        // 退货事由
        nsSalesOrderReturn.setReturnReason("退货事由");
        // 退货状态:0-未发起，1-待审批，2-已驳回，3-已完结
        nsSalesOrderReturn.setReturnStatus(0);
        // 审批人
        nsSalesOrderReturn.setApprovedBy(0L);
        // 审批时间
        nsSalesOrderReturn.setApprovedTime(new Date());
        // 删除标志（0代表存在，2代表删除）
        nsSalesOrderReturn.setDeleted(0);
        // 创建人
        nsSalesOrderReturn.setCreateBy(0L);
        // 创建时间
        nsSalesOrderReturn.setCreateTime(new Date());
        // 更新人
        nsSalesOrderReturn.setUpdateBy(0L);
        // 更新时间
        nsSalesOrderReturn.setUpdateTime(new Date());
        // 销售订单id
        nsSalesOrderReturn.setOrderId(0L);
        // 销售单号
        nsSalesOrderReturn.setSalesOrderCode("销售单号");

        // 检查待保存数据的合法性
        nsSalesOrderReturnApi.validatePo(nsSalesOrderReturn);

        // 名称唯一性检查?
        // nsSalesOrderReturnService.checkNameExisted(nsSalesOrderReturn);

        NsSalesOrderReturn result = nsSalesOrderReturnService.saveNsSalesOrderReturn(nsSalesOrderReturn);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（销售退库表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-14 14:51:34
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("销售退库表");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsSalesOrderReturn");
        // 组件路径
        menu.setComponent("business/nsSalesOrderReturn/nsSalesOrderReturn");
        // 权限标识
        menu.setPerms("business:nsSalesOrderReturn:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("周建宇");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("销售退库表", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "销售退库表-查询", 1, "business:nsSalesOrderReturn:list");
            addOperationMenu(menu.getMenuId(), "销售退库表-新增", 2, "business:nsSalesOrderReturn:add");
            addOperationMenu(menu.getMenuId(), "销售退库表-修改", 3, "business:nsSalesOrderReturn:edit");
            addOperationMenu(menu.getMenuId(), "销售退库表-删除", 4, "business:nsSalesOrderReturn:remove");
            addOperationMenu(menu.getMenuId(), "销售退库表-导出", 5, "business:nsSalesOrderReturn:export");
            addOperationMenu(menu.getMenuId(), "销售退库表-导入", 6, "business:nsSalesOrderReturn:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsSalesOrderReturn ok");
        }
    }

    /**
     * 创建操作按钮权限（销售退库表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-14 14:51:34
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（销售退库表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-14 14:51:34
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("销售退库表", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsSalesOrderReturn menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
