package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsMyKnowledgeBase;
import com.jorchi.business.service.NsMyKnowledgeBaseService;
import com.jorchi.business.form.NsMyKnowledgeBaseForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.util.Date;

/**
 * ns_my_knowledge_base-模块测试<br/>
 * 对应表名：ns_my_knowledge_base，表备注：我的知识库
 * @author: ysj at jorchi
 * @date: 2022-11-17 16:32:13
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsMyKnowledgeBaseTester extends BaseApi {

    @Resource
    NsMyKnowledgeBaseService nsMyKnowledgeBaseService;

    @Resource
    NsMyKnowledgeBaseApi nsMyKnowledgeBaseApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（我的知识库）<br>
     * @author: ysj at jorchi
     * @date: 2022-11-17 16:32:13
     */
    @Test
    public void findByIdNsMyKnowledgeBaseTest() {
        Long knowledgeId;
        knowledgeId = 1L;

        NsMyKnowledgeBase bean = nsMyKnowledgeBaseService.findById(knowledgeId);
        if (bean == null)
            throw ClientException.of("NsMyKnowledgeBase not found error!");

        log.info("findById NsMyKnowledgeBaseTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（我的知识库）<br>
     * @author: ysj at jorchi
     * @date: 2022-11-17 16:32:13
     */
    @Test
    public void listPageNsMyKnowledgeBaseTest() {
        NsMyKnowledgeBaseForm nsMyKnowledgeBaseVo = new NsMyKnowledgeBaseForm();
        nsMyKnowledgeBaseVo.setPageNum(1);
        nsMyKnowledgeBaseVo.setPageSize(10);
        log.info("listPage NsMyKnowledgeBaseTest result:");
        log.info(getDataTable(nsMyKnowledgeBaseService.listPage(nsMyKnowledgeBaseVo)).toString());
    }

    /**
     * 删除测试（我的知识库）<br>
     * @author: ysj at jorchi
     * @date: 2022-11-17 16:32:13
     */
    @Test
    public void deleteNsMyKnowledgeBaseTest() {
        Long id = 0L;
        log.info("delete NsMyKnowledgeBaseTest result:");
        log.info(AjaxResult.success(nsMyKnowledgeBaseService.deleteNsMyKnowledgeBase(id,1L)).toString());
    }

    /**
     * 新增或保存测试（我的知识库）<br>
     * ID 为空即为新增，否则为更新
     * @author: ysj at jorchi
     * @date: 2022-11-17 16:32:13
     */
    @Test
    public void saveNsMyKnowledgeBaseTest () {
        NsMyKnowledgeBase nsMyKnowledgeBase = new NsMyKnowledgeBase();
        // 我的知识库主键
        // nsMyKnowledgeBase.setKnowledgeId(0L);
        // 作物
        nsMyKnowledgeBase.setCropName("作物");
        // 品种
        nsMyKnowledgeBase.setBreeds("品种");
        // 知识编码
        nsMyKnowledgeBase.setKnowledgeCoding("知识编码");
        // 发芽起始温度
        nsMyKnowledgeBase.setSproutingStratTemperature(0L);
        // 发芽终止温度
        nsMyKnowledgeBase.setSproutingEndTemperature(0L);
        // 生长起始温度
        nsMyKnowledgeBase.setGrowStratTemperature(0L);
        // 生长终止温度
        nsMyKnowledgeBase.setGrowEndTemperature(0L);
        // 有效起始积温
        nsMyKnowledgeBase.setValidStratTemperature(0L);
        // 有效终止积温
        nsMyKnowledgeBase.setValidEndTemperature(0L);
        // 备注
        nsMyKnowledgeBase.setRemarks("备注");
        // 删除标志（0代表存在2代表删除）
        nsMyKnowledgeBase.setDeleted(0);
        // 创建人
        nsMyKnowledgeBase.setCreateBy(0L);
        // 创建时间
        nsMyKnowledgeBase.setCreateTime(new Date());
        // 更新人
        nsMyKnowledgeBase.setUpdateBy(0L);
        // 更新时间
        nsMyKnowledgeBase.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsMyKnowledgeBaseApi.validatePo(nsMyKnowledgeBase);

        // 名称唯一性检查?
        // nsMyKnowledgeBaseService.checkNameExisted(nsMyKnowledgeBase);

        NsMyKnowledgeBase result = nsMyKnowledgeBaseService.saveNsMyKnowledgeBase(nsMyKnowledgeBase,1L);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（我的知识库）<br>
     * @author: ysj at jorchi
     * @date: 2022-11-17 16:32:13
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("我的知识库");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsMyKnowledgeBase");
        // 组件路径
        menu.setComponent("business/nsMyKnowledgeBase/nsMyKnowledgeBase");
        // 权限标识
        menu.setPerms("business:nsMyKnowledgeBase:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("ysj");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("我的知识库", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "我的知识库-查询", 1, "business:nsMyKnowledgeBase:list");
            addOperationMenu(menu.getMenuId(), "我的知识库-新增", 2, "business:nsMyKnowledgeBase:add");
            addOperationMenu(menu.getMenuId(), "我的知识库-修改", 3, "business:nsMyKnowledgeBase:edit");
            addOperationMenu(menu.getMenuId(), "我的知识库-删除", 4, "business:nsMyKnowledgeBase:remove");
            //addOperationMenu(menu.getMenuId(), "我的知识库-导出", 5, "business:nsMyKnowledgeBase:export");
            //addOperationMenu(menu.getMenuId(), "我的知识库-导入", 6, "business:nsMyKnowledgeBase:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsMyKnowledgeBase ok");
        }
    }

    /**
     * 创建操作按钮权限（我的知识库）<br>
     * @author: ysj at jorchi
     * @date: 2022-11-17 16:32:13
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（我的知识库）<br>
     * @author: ysj at jorchi
     * @date: 2022-11-17 16:32:13
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("我的知识库", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsMyKnowledgeBase menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
