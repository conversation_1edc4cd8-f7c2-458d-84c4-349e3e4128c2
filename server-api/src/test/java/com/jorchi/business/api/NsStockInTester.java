package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsStockIn;
import com.jorchi.business.service.NsStockInService;
import com.jorchi.business.form.NsStockInForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartHttpServletRequest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ns_stock_in-模块测试<br/>
 * 对应表名：ns_stock_in，表备注：入库记录表
 * @author: 周建宇 at jorchi
 * @date: 2025-01-02 09:19:01
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsStockInTester extends BaseApi {

    @Resource
    NsStockInService nsStockInService;

    @Resource
    NsStockInApi nsStockInApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（入库记录表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-02 09:19:01
     */
    @Test
    public void findByIdNsStockInTest() {
        Long stockInId;
        stockInId = 1L;

        NsStockIn bean = nsStockInService.findById(stockInId);
        if (bean == null)
            throw ClientException.of("NsStockIn not found error!");

        log.info("findById NsStockInTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（入库记录表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-02 09:19:01
     */
    @Test
    public void listPageNsStockInTest() {
        NsStockInForm nsStockInVo = new NsStockInForm();
        nsStockInVo.setPageNum(1);
        nsStockInVo.setPageSize(10);
        log.info("listPage NsStockInTest result:");
        log.info(getDataTable(nsStockInService.listPage(nsStockInVo)).toString());
    }

    /**
     * 删除测试（入库记录表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-02 09:19:01
     */
    @Test
    public void deleteNsStockInTest() {
        Long id = 0L;
        log.info("delete NsStockInTest result:");
        log.info(AjaxResult.success(nsStockInService.deleteNsStockIn(id)).toString());
    }

    /**
     * 新增或保存测试（入库记录表）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2025-01-02 09:19:01
     */
    @Test
    public void saveNsStockInTest () {
        NsStockIn nsStockIn = new NsStockIn();
        // 入库记录ID
        // nsStockIn.setStockInId(0L);
        // 业务类型:1-采购入库
        // nsStockIn.setBusinessType(0);
        // 采购申请
        nsStockIn.setPurchaseId(0L);
        // 关联采购单号
        nsStockIn.setPurchaseCode("关联采购单号");
        // 入库仓库
        nsStockIn.setWarehouseId(0L);
        // 入库仓库编码
        nsStockIn.setWarehouseCode("入库仓库编码");
        // 入库日期
        nsStockIn.setStockInDate(new Date());
        // 经办人
        nsStockIn.setOperator("经办人");
        // 经办人ID
        nsStockIn.setOperatorId(0L);
        // 供应商
        nsStockIn.setSupplier("供应商");
        // 供应商ID
        nsStockIn.setSupplierId(0L);
        // 投入品类型：2=肥料，3=植保剂，4=种子，5=农膜，6=其他
        nsStockIn.setInputType(0);
        // 投入品ID
        nsStockIn.setBusinessId(0L);
        // 投入品类别
        nsStockIn.setInputCategory("投入品类别");
        // 投入品名称
        nsStockIn.setInputName("投入品名称");
        // 有效日期
        nsStockIn.setEffectiveDate(new Date());
        // 生产日期
        nsStockIn.setProductionDate(new Date());
        // 采购单价
        nsStockIn.setUnitPrice(new BigDecimal(0));
        // 采购数量
        nsStockIn.setQuantity(new BigDecimal(0));
        // 入库数量
        nsStockIn.setStockQuantity(new BigDecimal(0));
        // 删除标志（0代表存在2代表删除）
        nsStockIn.setDeleted(0);
        // 创建人
        nsStockIn.setCreateBy(0L);
        // 创建时间
        nsStockIn.setCreateTime(new Date());
        // 更新人
        nsStockIn.setUpdateBy(0L);
        // 更新时间
        nsStockIn.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsStockInApi.validatePo(nsStockIn);

        // 名称唯一性检查?
        // nsStockInService.checkNameExisted(nsStockIn);

        nsStockInService.saveNsStockIn(nsStockIn);
        log.info("save result:");

    }


    /**
     * 生成菜单（入库记录表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-02 09:19:01
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("入库记录表");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsStockIn");
        // 组件路径
        menu.setComponent("business/nsStockIn/nsStockIn");
        // 权限标识
        menu.setPerms("business:nsStockIn:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("周建宇");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("入库记录表", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "入库记录表-查询", 1, "business:nsStockIn:list");
            addOperationMenu(menu.getMenuId(), "入库记录表-新增", 2, "business:nsStockIn:add");
            addOperationMenu(menu.getMenuId(), "入库记录表-修改", 3, "business:nsStockIn:edit");
            addOperationMenu(menu.getMenuId(), "入库记录表-删除", 4, "business:nsStockIn:remove");
            addOperationMenu(menu.getMenuId(), "入库记录表-导出", 5, "business:nsStockIn:export");
            addOperationMenu(menu.getMenuId(), "入库记录表-导入", 6, "business:nsStockIn:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsStockIn ok");
        }
    }

    /**
     * 创建操作按钮权限（入库记录表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-02 09:19:01
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（入库记录表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-02 09:19:01
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("入库记录表", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsStockIn menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
