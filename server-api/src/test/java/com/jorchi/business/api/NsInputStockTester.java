package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsInputStock;
import com.jorchi.business.service.NsInputStockService;
import com.jorchi.business.form.NsInputStockForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartHttpServletRequest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ns_input_stock-模块测试<br/>
 * 对应表名：ns_input_stock，表备注：投入品库存表
 * @author: 周建宇 at jorchi
 * @date: 2024-12-13 11:01:03
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsInputStockTester extends BaseApi {

    @Resource
    NsInputStockService nsInputStockService;

    @Resource
    NsInputStockApi nsInputStockApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（投入品库存表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-13 11:01:03
     */
    @Test
    public void findByIdNsInputStockTest() {
        Long inputId;
        inputId = 1L;

        NsInputStock bean = nsInputStockService.findById(inputId);
        if (bean == null)
            throw ClientException.of("NsInputStock not found error!");

        log.info("findById NsInputStockTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（投入品库存表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-13 11:01:03
     */
    @Test
    public void listPageNsInputStockTest() {
        NsInputStockForm nsInputStockVo = new NsInputStockForm();
        nsInputStockVo.setPageNum(1);
        nsInputStockVo.setPageSize(10);
        log.info("listPage NsInputStockTest result:");
        log.info(getDataTable(nsInputStockService.listPage(nsInputStockVo)).toString());
    }

    /**
     * 删除测试（投入品库存表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-13 11:01:03
     */
    @Test
    public void deleteNsInputStockTest() {
        Long id = 0L;
        log.info("delete NsInputStockTest result:");
        log.info(AjaxResult.success(nsInputStockService.deleteNsInputStock(id)).toString());
    }

    /**
     * 新增或保存测试（投入品库存表）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2024-12-13 11:01:03
     */
    @Test
    public void saveNsInputStockTest () {
        NsInputStock nsInputStock = new NsInputStock();
        // 投入品库存id
        // nsInputStock.setInputId(0L);
        // 投入品类别
        nsInputStock.setInputCategory("投入品类别");
        // 投入品名称
        nsInputStock.setInputName("投入品名称");
        // 库存预警线
        nsInputStock.setStockWarningLine(new BigDecimal(0));
        // 库存量
        nsInputStock.setStockQuantity(new BigDecimal(0));
        // 预警状态
        nsInputStock.setWarningStatus("预警状态");
        // 删除标记
        nsInputStock.setDeleted(0);
        // 创建人
        nsInputStock.setCreateBy(0L);
        // 创建时间
        nsInputStock.setCreateTime(new Date());
        // 更新人
        nsInputStock.setUpdateBy(0L);
        // 更新时间
        nsInputStock.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsInputStockApi.validatePo(nsInputStock);

        // 名称唯一性检查?
        // nsInputStockService.checkNameExisted(nsInputStock);

        NsInputStock result = nsInputStockService.saveNsInputStock(nsInputStock);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（投入品库存表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-13 11:01:03
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("投入品库存表");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsInputStock");
        // 组件路径
        menu.setComponent("business/nsInputStock/nsInputStock");
        // 权限标识
        menu.setPerms("business:nsInputStock:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("周建宇");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("投入品库存表", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "投入品库存表-查询", 1, "business:nsInputStock:list");
            addOperationMenu(menu.getMenuId(), "投入品库存表-新增", 2, "business:nsInputStock:add");
            addOperationMenu(menu.getMenuId(), "投入品库存表-修改", 3, "business:nsInputStock:edit");
            addOperationMenu(menu.getMenuId(), "投入品库存表-删除", 4, "business:nsInputStock:remove");
            addOperationMenu(menu.getMenuId(), "投入品库存表-导出", 5, "business:nsInputStock:export");
            addOperationMenu(menu.getMenuId(), "投入品库存表-导入", 6, "business:nsInputStock:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsInputStock ok");
        }
    }

    /**
     * 创建操作按钮权限（投入品库存表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-13 11:01:03
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（投入品库存表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-13 11:01:03
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("投入品库存表", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsInputStock menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
