package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsProductStock;
import com.jorchi.business.service.NsProductStockService;
import com.jorchi.business.form.NsProductStockForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartHttpServletRequest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ns_product_stock-模块测试<br/>
 * 对应表名：ns_product_stock，表备注：农产品库存表
 * @author: 周建宇 at jorchi
 * @date: 2024-12-17 15:49:39
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsProductStockTester extends BaseApi {

    @Resource
    NsProductStockService nsProductStockService;

    @Resource
    NsProductStockApi nsProductStockApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（农产品库存表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-17 15:49:39
     */
    @Test
    public void findByIdNsProductStockTest() {
        Long productStockId;
        productStockId = 1L;

        NsProductStock bean = nsProductStockService.findById(productStockId);
        if (bean == null)
            throw ClientException.of("NsProductStock not found error!");

        log.info("findById NsProductStockTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（农产品库存表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-17 15:49:39
     */
    @Test
    public void listPageNsProductStockTest() {
        NsProductStockForm nsProductStockVo = new NsProductStockForm();
        nsProductStockVo.setPageNum(1);
        nsProductStockVo.setPageSize(10);
        log.info("listPage NsProductStockTest result:");
        log.info(getDataTable(nsProductStockService.listPage(nsProductStockVo)).toString());
    }

    /**
     * 删除测试（农产品库存表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-17 15:49:39
     */
    @Test
    public void deleteNsProductStockTest() {
        Long id = 0L;
        log.info("delete NsProductStockTest result:");
        log.info(AjaxResult.success(nsProductStockService.deleteNsProductStock(id)).toString());
    }

    /**
     * 新增或保存测试（农产品库存表）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2024-12-17 15:49:39
     */
    @Test
    public void saveNsProductStockTest () {
        NsProductStock nsProductStock = new NsProductStock();
        // 农产品库存id
        // nsProductStock.setProductStockId(0L);
        // 农产品类别
        nsProductStock.setCropCategory("农产品类别");
        // 农产品名称
        nsProductStock.setCropName("农产品名称");
        // 库存预警线
        nsProductStock.setStockWarningLine(new BigDecimal(0));
        // 库存量
        nsProductStock.setStockQuantity(new BigDecimal(0));
        // 预警状态
        nsProductStock.setWarningStatus("预警状态");
        // 删除标记
        nsProductStock.setDeleted(0);
        // 创建人
        nsProductStock.setCreateBy(0L);
        // 创建时间
        nsProductStock.setCreateTime(new Date());
        // 更新人
        nsProductStock.setUpdateBy(0L);
        // 更新时间
        nsProductStock.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsProductStockApi.validatePo(nsProductStock);

        // 名称唯一性检查?
        // nsProductStockService.checkNameExisted(nsProductStock);

        NsProductStock result = nsProductStockService.saveNsProductStock(nsProductStock);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（农产品库存表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-17 15:49:39
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("农产品库存表");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsProductStock");
        // 组件路径
        menu.setComponent("business/nsProductStock/nsProductStock");
        // 权限标识
        menu.setPerms("business:nsProductStock:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("周建宇");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("农产品库存表", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "农产品库存表-查询", 1, "business:nsProductStock:list");
            addOperationMenu(menu.getMenuId(), "农产品库存表-新增", 2, "business:nsProductStock:add");
            addOperationMenu(menu.getMenuId(), "农产品库存表-修改", 3, "business:nsProductStock:edit");
            addOperationMenu(menu.getMenuId(), "农产品库存表-删除", 4, "business:nsProductStock:remove");
            addOperationMenu(menu.getMenuId(), "农产品库存表-导出", 5, "business:nsProductStock:export");
            addOperationMenu(menu.getMenuId(), "农产品库存表-导入", 6, "business:nsProductStock:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsProductStock ok");
        }
    }

    /**
     * 创建操作按钮权限（农产品库存表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-17 15:49:39
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（农产品库存表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-17 15:49:39
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("农产品库存表", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsProductStock menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
