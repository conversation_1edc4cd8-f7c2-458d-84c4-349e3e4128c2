package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsCustomer;
import com.jorchi.business.service.NsCustomerService;
import com.jorchi.business.form.NsCustomerForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartHttpServletRequest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ns_customer-模块测试<br/>
 * 对应表名：ns_customer，表备注：客户表
 * @author: 周建宇 at jorchi
 * @date: 2025-01-11 12:32:18
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsCustomerTester extends BaseApi {

    @Resource
    NsCustomerService nsCustomerService;

    @Resource
    NsCustomerApi nsCustomerApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（客户表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-11 12:32:18
     */
    @Test
    public void findByIdNsCustomerTest() {
        Long customerId;
        customerId = 1L;

        NsCustomer bean = nsCustomerService.findById(customerId);
        if (bean == null)
            throw ClientException.of("NsCustomer not found error!");

        log.info("findById NsCustomerTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（客户表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-11 12:32:18
     */
    @Test
    public void listPageNsCustomerTest() {
        NsCustomerForm nsCustomerVo = new NsCustomerForm();
        nsCustomerVo.setPageNum(1);
        nsCustomerVo.setPageSize(10);
        log.info("listPage NsCustomerTest result:");
        log.info(getDataTable(nsCustomerService.listPage(nsCustomerVo)).toString());
    }

    /**
     * 删除测试（客户表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-11 12:32:18
     */
    @Test
    public void deleteNsCustomerTest() {
        Long id = 0L;
        log.info("delete NsCustomerTest result:");
        log.info(AjaxResult.success(nsCustomerService.deleteNsCustomer(id)).toString());
    }

    /**
     * 新增或保存测试（客户表）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2025-01-11 12:32:18
     */
    @Test
    public void saveNsCustomerTest () {
        NsCustomer nsCustomer = new NsCustomer();
        // 客户ID
        // nsCustomer.setCustomerId(0L);
        // 客户名称
        nsCustomer.setCustomerName("客户名称");
        // 税号
        nsCustomer.setTaxNumber("税号");
        // 地址
        nsCustomer.setAddress("地址");
        // 开户银行
        nsCustomer.setBankName("开户银行");
        // 银行账号
        nsCustomer.setBankAccount("银行账号");
        // 联系人
        nsCustomer.setContactPerson("联系人");
        // 联系人电话
        nsCustomer.setContactPhone("联系人电话");
        // 删除标志（0代表存在，2代表删除）
        nsCustomer.setDeleted(0);
        // 创建人
        nsCustomer.setCreateBy(0L);
        // 创建时间
        nsCustomer.setCreateTime(new Date());
        // 更新人
        nsCustomer.setUpdateBy(0L);
        // 更新时间
        nsCustomer.setUpdateTime(new Date());
        // 备注
        nsCustomer.setNote("备注");

        // 检查待保存数据的合法性
        nsCustomerApi.validatePo(nsCustomer);

        // 名称唯一性检查?
        // nsCustomerService.checkNameExisted(nsCustomer);

        NsCustomer result = nsCustomerService.saveNsCustomer(nsCustomer);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（客户表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-11 12:32:18
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("客户表");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsCustomer");
        // 组件路径
        menu.setComponent("business/nsCustomer/nsCustomer");
        // 权限标识
        menu.setPerms("business:nsCustomer:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("周建宇");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("客户表", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "客户表-查询", 1, "business:nsCustomer:list");
            addOperationMenu(menu.getMenuId(), "客户表-新增", 2, "business:nsCustomer:add");
            addOperationMenu(menu.getMenuId(), "客户表-修改", 3, "business:nsCustomer:edit");
            addOperationMenu(menu.getMenuId(), "客户表-删除", 4, "business:nsCustomer:remove");
            addOperationMenu(menu.getMenuId(), "客户表-导出", 5, "business:nsCustomer:export");
            addOperationMenu(menu.getMenuId(), "客户表-导入", 6, "business:nsCustomer:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsCustomer ok");
        }
    }

    /**
     * 创建操作按钮权限（客户表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-11 12:32:18
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（客户表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-11 12:32:18
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("客户表", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsCustomer menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
