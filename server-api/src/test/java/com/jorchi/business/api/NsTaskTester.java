package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsTask;
import com.jorchi.business.service.NsTaskService;
import com.jorchi.business.form.NsTaskForm;
import com.jorchi.common.constant.CommonDef;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.util.Date;

/**
 * ns_task-模块测试<br/>
 * 对应表名：ns_task，表备注：任务表
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON> at jorchi
 * @date: 2022-11-11 17:39:46
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsTaskTester extends BaseApi {

    @Resource
    NsTaskService nsTaskService;

    @Resource
    NsTaskApi nsTaskApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（任务表）<br>
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-11 17:39:46
     */
    @Test
    public void findByIdNsTaskTest() {
        Long taskId;
        taskId = 1L;

        NsTask bean = nsTaskService.findById(taskId);
        if (bean == null)
            throw ClientException.of("NsTask not found error!");

        log.info("findById NsTaskTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（任务表）<br>
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-11 17:39:46
     */
    @Test
    public void listPageNsTaskTest() {
        NsTaskForm nsTaskVo = new NsTaskForm();
        nsTaskVo.setPageNum(1);
        nsTaskVo.setPageSize(10);
        log.info("listPage NsTaskTest result:");
        log.info(getDataTable(nsTaskService.listPage(nsTaskVo)).toString());
    }

    /**
     * 删除测试（任务表）<br>
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-11 17:39:46
     */
    @Test
    public void deleteNsTaskTest() {
        Long id = 0L;
        log.info("delete NsTaskTest result:");
        log.info(AjaxResult.success(nsTaskService.deleteNsTask(id)).toString());
    }

    /**
     * 新增或保存测试（任务表）<br>
     * ID 为空即为新增，否则为更新
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-11 17:39:46
     */
    @Test
    public void saveNsTaskTest () {
        NsTask nsTask = new NsTask();
        // 任务主键
        // nsTask.setTaskId(0L);
        // 生产计划id
        // nsTask.setNsProductionPlanId(0L);
        // 农事流程id
        nsTask.setFarmingProcessId(0L);
        // 农事流程名称
        nsTask.setProcessName("农事流程名称");
        // 流程开始时间
        nsTask.setStartTime(new Date());
        // 流程结束时间
        nsTask.setEndTime(new Date());
        // 完成状态
        nsTask.setStatus(0);
        // 是否延期
        nsTask.setDelay(1);
        // 产量
        nsTask.setOutput("产量");
        // 采收面积
        nsTask.setRecoveredArea("采收面积");
        // 完成时间
        nsTask.setCompletionTime(new Date());
        // 周期开始
        nsTask.setCycleStart(0);
        // 周期结束
        nsTask.setCycleEnd(0);
        // 备注
        nsTask.setRemarks("备注");
        // 图片
        nsTask.setPictureUrl("图片");
        // 技术人员id
        nsTask.setArtisanId(0L);
        // 技术人员名字
        nsTask.setArtisanName("技术人员名字");
        // 代理人ID
        nsTask.setTechnicianAgentId(0L);
        // 代理人名称
        nsTask.setTechnicianAgentName("代理人名称");
        // 删除标志（0代表存在2代表删除）
        nsTask.setDeleted(true);
        // 创建人
        nsTask.setCreateBy(0L);
        // 创建时间
        nsTask.setCreateTime(new Date());
        // 更新人
        nsTask.setUpdateBy(0L);
        // 更新时间
        nsTask.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsTaskApi.validatePo(nsTask);

        // 名称唯一性检查?
        // nsTaskService.checkNameExisted(nsTask);

        NsTask result = nsTaskService.saveNsTask(nsTask);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（任务表）<br>
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-11 17:39:46
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("任务表");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsTask");
        // 组件路径
        menu.setComponent("business/nsTask/nsTask");
        // 权限标识
        menu.setPerms("business:nsTask:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("ChenLiFeng");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("任务表", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "任务表-查询", 1, "business:nsTask:list");
            addOperationMenu(menu.getMenuId(), "任务表-新增", 2, "business:nsTask:add");
            addOperationMenu(menu.getMenuId(), "任务表-修改", 3, "business:nsTask:edit");
            addOperationMenu(menu.getMenuId(), "任务表-删除", 4, "business:nsTask:remove");
            addOperationMenu(menu.getMenuId(), "任务表-导出", 5, "business:nsTask:export");
            addOperationMenu(menu.getMenuId(), "任务表-导入", 6, "business:nsTask:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsTask ok");
        }
    }

    /**
     * 创建操作按钮权限（任务表）<br>
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-11 17:39:46
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（任务表）<br>
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-11 17:39:46
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("任务表", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsTask menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
