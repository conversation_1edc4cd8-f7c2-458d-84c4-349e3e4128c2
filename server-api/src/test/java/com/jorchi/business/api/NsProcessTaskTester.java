package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsProcessTask;
import com.jorchi.business.service.NsProcessTaskService;
import com.jorchi.business.form.NsProcessTaskForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartHttpServletRequest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ns_process_task-模块测试<br/>
 * 对应表名：ns_process_task，表备注：流程任务表
 * @author: 周建宇 at jorchi
 * @date: 2024-12-25 15:17:09
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsProcessTaskTester extends BaseApi {

    @Resource
    NsProcessTaskService nsProcessTaskService;

    @Resource
    NsProcessTaskApi nsProcessTaskApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（流程任务表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 15:17:09
     */
    @Test
    public void findByIdNsProcessTaskTest() {
        Long taskId;
        taskId = 1L;

        NsProcessTask bean = nsProcessTaskService.findById(taskId);
        if (bean == null)
            throw ClientException.of("NsProcessTask not found error!");

        log.info("findById NsProcessTaskTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（流程任务表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 15:17:09
     */
    @Test
    public void listPageNsProcessTaskTest() {
        NsProcessTaskForm nsProcessTaskVo = new NsProcessTaskForm();
        nsProcessTaskVo.setPageNum(1);
        nsProcessTaskVo.setPageSize(10);
        log.info("listPage NsProcessTaskTest result:");
        log.info(getDataTable(nsProcessTaskService.listPage(nsProcessTaskVo)).toString());
    }

    /**
     * 删除测试（流程任务表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 15:17:09
     */
    @Test
    public void deleteNsProcessTaskTest() {
        Long id = 0L;
        log.info("delete NsProcessTaskTest result:");
        log.info(AjaxResult.success(nsProcessTaskService.deleteNsProcessTask(id)).toString());
    }

    /**
     * 新增或保存测试（流程任务表）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 15:17:09
     */
    @Test
    public void saveNsProcessTaskTest () {
        NsProcessTask nsProcessTask = new NsProcessTask();
        // 流程任务ID
        // nsProcessTask.setTaskId(0L);
        // 流程ID
        // nsProcessTask.setProcessId(0L);
        // 审批人
        nsProcessTask.setApprover("审批人");
        // 审批人ID
        nsProcessTask.setApprovedBy(0L);
        // 审批状态:0-待审批，1-已驳回，2-已完结
        nsProcessTask.setApprovalStatus(0);
        // 审批备注
        nsProcessTask.setApprovalRemark("审批备注");
        // 任务顺序
        nsProcessTask.setTaskSequence(0);
        // 实际完成时间
        nsProcessTask.setActualCompletion(new Date());
        // 是否紧急（0：否，1：是）
        nsProcessTask.setIsUrgent(0);
        // 删除标志（0代表存在，1代表删除）
        nsProcessTask.setDeleted(0);
        // 创建人
        nsProcessTask.setCreateBy(0L);
        // 创建时间
        nsProcessTask.setCreateTime(new Date());
        // 更新人
        nsProcessTask.setUpdateBy(0L);
        // 更新时间
        nsProcessTask.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsProcessTaskApi.validatePo(nsProcessTask);

        // 名称唯一性检查?
        // nsProcessTaskService.checkNameExisted(nsProcessTask);

        NsProcessTask result = nsProcessTaskService.saveNsProcessTask(nsProcessTask);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（流程任务表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 15:17:09
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("流程任务表");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsProcessTask");
        // 组件路径
        menu.setComponent("business/nsProcessTask/nsProcessTask");
        // 权限标识
        menu.setPerms("business:nsProcessTask:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("周建宇");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("流程任务表", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "流程任务表-查询", 1, "business:nsProcessTask:list");
            addOperationMenu(menu.getMenuId(), "流程任务表-新增", 2, "business:nsProcessTask:add");
            addOperationMenu(menu.getMenuId(), "流程任务表-修改", 3, "business:nsProcessTask:edit");
            addOperationMenu(menu.getMenuId(), "流程任务表-删除", 4, "business:nsProcessTask:remove");
            addOperationMenu(menu.getMenuId(), "流程任务表-导出", 5, "business:nsProcessTask:export");
            addOperationMenu(menu.getMenuId(), "流程任务表-导入", 6, "business:nsProcessTask:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsProcessTask ok");
        }
    }

    /**
     * 创建操作按钮权限（流程任务表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 15:17:09
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（流程任务表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 15:17:09
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("流程任务表", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsProcessTask menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
