package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.dto.AddPesticideResidueTesDto;
import com.jorchi.business.po.NsPesticideResidueTest;
import com.jorchi.business.service.NsPesticideResidueTestService;
import com.jorchi.business.form.NsPesticideResidueTestForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartHttpServletRequest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ns_pesticide_residue_test-模块测试<br/>
 * 对应表名：ns_pesticide_residue_test，表备注：农残检测表
 * @author: 周建宇 at jorchi
 * @date: 2025-01-21 16:32:59
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsPesticideResidueTestTester extends BaseApi {

    @Resource
    NsPesticideResidueTestService nsPesticideResidueTestService;

    @Resource
    NsPesticideResidueTestApi nsPesticideResidueTestApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（农残检测表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-21 16:32:59
     */
    @Test
    public void findByIdNsPesticideResidueTestTest() {
        Long testId;
        testId = 1L;

        NsPesticideResidueTest bean = nsPesticideResidueTestService.findById(testId);
        if (bean == null)
            throw ClientException.of("NsPesticideResidueTest not found error!");

        log.info("findById NsPesticideResidueTestTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（农残检测表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-21 16:32:59
     */
    @Test
    public void listPageNsPesticideResidueTestTest() {
        NsPesticideResidueTestForm nsPesticideResidueTestVo = new NsPesticideResidueTestForm();
        nsPesticideResidueTestVo.setPageNum(1);
        nsPesticideResidueTestVo.setPageSize(10);
        log.info("listPage NsPesticideResidueTestTest result:");
        log.info(getDataTable(nsPesticideResidueTestService.listPage(nsPesticideResidueTestVo)).toString());
    }

    /**
     * 删除测试（农残检测表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-21 16:32:59
     */
    @Test
    public void deleteNsPesticideResidueTestTest() {
        Long id = 0L;
        log.info("delete NsPesticideResidueTestTest result:");
        log.info(AjaxResult.success(nsPesticideResidueTestService.deleteNsPesticideResidueTest(id)).toString());
    }

    /**
     * 新增或保存测试（农残检测表）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2025-01-21 16:32:59
     */
    @Test
    public void saveNsPesticideResidueTestTest () {
        AddPesticideResidueTesDto nsPesticideResidueTest = new AddPesticideResidueTesDto();
        // 检测id
        // nsPesticideResidueTest.setTestId(0L);
        // 检查人
        nsPesticideResidueTest.setInspector("检查人");
        // 检查人id
        nsPesticideResidueTest.setInspectorId(0L);
        // 检测时间
        nsPesticideResidueTest.setTestTime(new Date());
        // 销售订单id，关联销售订单表的订单id
        nsPesticideResidueTest.setSalesOrderId(0L);
        // 销售订单号，关联销售订单表的订单号
        nsPesticideResidueTest.setSalesOrderCode("销售订单号，关联销售订单表的订单号");
        // 删除标志（0代表存在，2代表删除）
        nsPesticideResidueTest.setDeleted(0);
        // 创建人
        nsPesticideResidueTest.setCreateBy(0L);
        // 创建时间
        nsPesticideResidueTest.setCreateTime(new Date());
        // 更新人
        nsPesticideResidueTest.setUpdateBy(0L);
        // 更新时间
        nsPesticideResidueTest.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsPesticideResidueTestApi.validatePo(nsPesticideResidueTest);

        // 名称唯一性检查?
        // nsPesticideResidueTestService.checkNameExisted(nsPesticideResidueTest);

        NsPesticideResidueTest result = nsPesticideResidueTestService.saveNsPesticideResidueTest(nsPesticideResidueTest);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（农残检测表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-21 16:32:59
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("农残检测表");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsPesticideResidueTest");
        // 组件路径
        menu.setComponent("business/nsPesticideResidueTest/nsPesticideResidueTest");
        // 权限标识
        menu.setPerms("business:nsPesticideResidueTest:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("周建宇");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("农残检测表", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "农残检测表-查询", 1, "business:nsPesticideResidueTest:list");
            addOperationMenu(menu.getMenuId(), "农残检测表-新增", 2, "business:nsPesticideResidueTest:add");
            addOperationMenu(menu.getMenuId(), "农残检测表-修改", 3, "business:nsPesticideResidueTest:edit");
            addOperationMenu(menu.getMenuId(), "农残检测表-删除", 4, "business:nsPesticideResidueTest:remove");
            addOperationMenu(menu.getMenuId(), "农残检测表-导出", 5, "business:nsPesticideResidueTest:export");
            addOperationMenu(menu.getMenuId(), "农残检测表-导入", 6, "business:nsPesticideResidueTest:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsPesticideResidueTest ok");
        }
    }

    /**
     * 创建操作按钮权限（农残检测表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-21 16:32:59
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（农残检测表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-21 16:32:59
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("农残检测表", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsPesticideResidueTest menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
