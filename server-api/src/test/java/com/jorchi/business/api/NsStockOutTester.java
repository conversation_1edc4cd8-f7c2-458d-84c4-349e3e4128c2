package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsStockOut;
import com.jorchi.business.service.NsStockOutService;
import com.jorchi.business.form.NsStockOutForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartHttpServletRequest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ns_stock_out-模块测试<br/>
 * 对应表名：ns_stock_out，表备注：出库记录表
 * @author: 周建宇 at jorchi
 * @date: 2025-01-04 16:32:09
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsStockOutTester extends BaseApi {

    @Resource
    NsStockOutService nsStockOutService;

    @Resource
    NsStockOutApi nsStockOutApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（出库记录表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-04 16:32:09
     */
    @Test
    public void findByIdNsStockOutTest() {
        Long stockOutId;
        stockOutId = 1L;

        NsStockOut bean = nsStockOutService.findById(stockOutId);
        if (bean == null)
            throw ClientException.of("NsStockOut not found error!");

        log.info("findById NsStockOutTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（出库记录表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-04 16:32:09
     */
    @Test
    public void listPageNsStockOutTest() {
        NsStockOutForm nsStockOutVo = new NsStockOutForm();
        nsStockOutVo.setPageNum(1);
        nsStockOutVo.setPageSize(10);
        log.info("listPage NsStockOutTest result:");
        log.info(getDataTable(nsStockOutService.listPage(nsStockOutVo)).toString());
    }

    /**
     * 删除测试（出库记录表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-04 16:32:09
     */
    @Test
    public void deleteNsStockOutTest() {
        Long id = 0L;
        log.info("delete NsStockOutTest result:");
        log.info(AjaxResult.success(nsStockOutService.deleteNsStockOut(id)).toString());
    }

    /**
     * 新增或保存测试（出库记录表）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2025-01-04 16:32:09
     */
    @Test
    public void saveNsStockOutTest () {
        NsStockOut nsStockOut = new NsStockOut();
        // 出库记录ID
        // nsStockOut.setStockOutId(0L);
        // 业务类型:1-领用出库
        // nsStockOut.setBusinessType(0);
        // 采购申请
        nsStockOut.setUseId(0L);
        // 关联采购单号
        nsStockOut.setUseCode("关联采购单号");
        // 入库仓库
        nsStockOut.setWarehouseId(0L);
        // 入库仓库编码
        nsStockOut.setWarehouseCode("入库仓库编码");
        // 入库日期
        nsStockOut.setStockOutDate(new Date());
        // 经办人
        nsStockOut.setOperator("经办人");
        // 经办人ID
        nsStockOut.setOperatorId(0L);
        // 投入品类型：2=肥料，3=植保剂，4=种子，5=农膜，6=其他
        nsStockOut.setInputType(0);
        // 投入品ID
        nsStockOut.setBusinessId(0L);
        // 投入品类别
        nsStockOut.setInputCategory("投入品类别");
        // 投入品名称
        nsStockOut.setInputName("投入品名称");
        // 投入品单位
        nsStockOut.setInputUnit("投入品单位");
        // 出库数量
        nsStockOut.setStockQuantity(new BigDecimal(0));
        // 删除标志（0代表存在2代表删除）
        nsStockOut.setDeleted(0);
        // 创建人
        nsStockOut.setCreateBy(0L);
        // 创建时间
        nsStockOut.setCreateTime(new Date());
        // 更新人
        nsStockOut.setUpdateBy(0L);
        // 更新时间
        nsStockOut.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsStockOutApi.validatePo(nsStockOut);

        // 名称唯一性检查?
        // nsStockOutService.checkNameExisted(nsStockOut);

        nsStockOutService.saveNsStockOut(nsStockOut);
        log.info("save result:");
    }


    /**
     * 生成菜单（出库记录表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-04 16:32:09
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("出库记录表");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsStockOut");
        // 组件路径
        menu.setComponent("business/nsStockOut/nsStockOut");
        // 权限标识
        menu.setPerms("business:nsStockOut:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("周建宇");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("出库记录表", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "出库记录表-查询", 1, "business:nsStockOut:list");
            addOperationMenu(menu.getMenuId(), "出库记录表-新增", 2, "business:nsStockOut:add");
            addOperationMenu(menu.getMenuId(), "出库记录表-修改", 3, "business:nsStockOut:edit");
            addOperationMenu(menu.getMenuId(), "出库记录表-删除", 4, "business:nsStockOut:remove");
            addOperationMenu(menu.getMenuId(), "出库记录表-导出", 5, "business:nsStockOut:export");
            addOperationMenu(menu.getMenuId(), "出库记录表-导入", 6, "business:nsStockOut:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsStockOut ok");
        }
    }

    /**
     * 创建操作按钮权限（出库记录表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-04 16:32:09
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（出库记录表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-04 16:32:09
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("出库记录表", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsStockOut menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
