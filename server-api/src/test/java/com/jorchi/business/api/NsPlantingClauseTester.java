package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsPlantingClause;
import com.jorchi.business.service.NsPlantingClauseService;
import com.jorchi.business.form.NsPlantingClauseForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.util.Date;

/**
 * ns_planting_clause-模块测试<br/>
 * 对应表名：ns_planting_clause，表备注：种植项
 * @author: GeSenQi at jorchi
 * @date: 2022-11-10 15:30:33
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsPlantingClauseTester extends BaseApi {

    @Resource
    NsPlantingClauseService nsPlantingClauseService;

    @Resource
    NsPlantingClauseApi nsPlantingClauseApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（种植项）<br>
     * @author: GeSenQi at jorchi
     * @date: 2022-11-10 15:30:33
     */
    @Test
    public void findByIdNsPlantingClauseTest() {
        Long id;
        id = 1L;

        NsPlantingClause bean = nsPlantingClauseService.findById(id);
        if (bean == null)
            throw ClientException.of("NsPlantingClause not found error!");

        log.info("findById NsPlantingClauseTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（种植项）<br>
     * @author: GeSenQi at jorchi
     * @date: 2022-11-10 15:30:33
     */
    @Test
    public void listPageNsPlantingClauseTest() {
        NsPlantingClauseForm nsPlantingClauseVo = new NsPlantingClauseForm();
        nsPlantingClauseVo.setPageNum(1);
        nsPlantingClauseVo.setPageSize(10);
        log.info("listPage NsPlantingClauseTest result:");
        log.info(getDataTable(nsPlantingClauseService.listPage(nsPlantingClauseVo)).toString());
    }

//    /**
//     * 删除测试（种植项）<br>
//     * @author: GeSenQi at jorchi
//     * @date: 2022-11-10 15:30:33
//     */
//    @Test
//    public void deleteNsPlantingClauseTest() {
//        Long id = 0L;
//        log.info("delete NsPlantingClauseTest result:");
//        log.info(AjaxResult.success(nsPlantingClauseService.deleteNsPlantingClause(id)).toString());
//    }

//    /**
//     * 新增或保存测试（种植项）<br>
//     * ID 为空即为新增，否则为更新
//     * @author: GeSenQi at jorchi
//     * @date: 2022-11-10 15:30:33
//     */
//    @Test
//    public void saveNsPlantingClauseTest () {
//        NsPlantingClause nsPlantingClause = new NsPlantingClause();
//        // 种植项ID
//        // nsPlantingClause.setPlantingClauseId(0L);
//        // 种植标准ID
//        // nsPlantingClause.setPlantingId(0L);
//        // 农事流程ID
//        nsPlantingClause.setFarmingProcessId(0L);
//        // 农事流程名称
//        nsPlantingClause.setFarmingProcessName("农事流程名称");
//        // 作业时间
//        nsPlantingClause.setInputTime(0);
//        // 是否重复执行
//        nsPlantingClause.setIsRepeat(0);
//        // 周期开始
//        nsPlantingClause.setCycleStart(0);
//        // 周期结束
//        nsPlantingClause.setCycleEnd(0);
//        // 是否选择物料
//        nsPlantingClause.setIsSelect(0);
//        // 内容详情
//        nsPlantingClause.setContext("内容详情");
//        // 删除标志
//        nsPlantingClause.setDeleted(0);
//        // 创建人
//        nsPlantingClause.setCreateBy(0L);
//        // 创建时间
//        nsPlantingClause.setCreateTime(new Date());
//        // 更新人
//        nsPlantingClause.setUpdateBy(0L);
//        // 更新时间
//        nsPlantingClause.setUpdateTime(new Date());
//
//        // 检查待保存数据的合法性
//        nsPlantingClauseApi.validatePo(nsPlantingClause);
//
//        // 名称唯一性检查?
//        // nsPlantingClauseService.checkNameExisted(nsPlantingClause);
//
//        NsPlantingClause result = nsPlantingClauseService.saveNsPlantingClause(nsPlantingClause);
//        log.info("save result:");
//        log.info(result.toString());
//    }


    /**
     * 生成菜单（种植项）<br>
     * @author: GeSenQi at jorchi
     * @date: 2022-11-10 15:30:33
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("种植项");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsPlantingClause");
        // 组件路径
        menu.setComponent("business/nsPlantingClause/nsPlantingClause");
        // 权限标识
        menu.setPerms("business:nsPlantingClause:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("GeSenQi");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("种植项", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "种植项-查询", 1, "business:nsPlantingClause:list");
            addOperationMenu(menu.getMenuId(), "种植项-新增", 2, "business:nsPlantingClause:add");
            addOperationMenu(menu.getMenuId(), "种植项-修改", 3, "business:nsPlantingClause:edit");
            addOperationMenu(menu.getMenuId(), "种植项-删除", 4, "business:nsPlantingClause:remove");
            addOperationMenu(menu.getMenuId(), "种植项-导出", 5, "business:nsPlantingClause:export");
            addOperationMenu(menu.getMenuId(), "种植项-导入", 6, "business:nsPlantingClause:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsPlantingClause ok");
        }
    }

    /**
     * 创建操作按钮权限（种植项）<br>
     * @author: GeSenQi at jorchi
     * @date: 2022-11-10 15:30:33
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（种植项）<br>
     * @author: GeSenQi at jorchi
     * @date: 2022-11-10 15:30:33
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("种植项", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsPlantingClause menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
