package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsApprovalProcess;
import com.jorchi.business.service.NsApprovalProcessService;
import com.jorchi.business.form.NsApprovalProcessForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartHttpServletRequest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ns_approval_process-模块测试<br/>
 * 对应表名：ns_approval_process，表备注：审批流程表
 * @author: 周建宇 at jorchi
 * @date: 2024-12-25 15:16:03
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsApprovalProcessTester extends BaseApi {

    @Resource
    NsApprovalProcessService nsApprovalProcessService;

    @Resource
    NsApprovalProcessApi nsApprovalProcessApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（审批流程表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 15:16:03
     */
    @Test
    public void findByIdNsApprovalProcessTest() {
        Long approvalId;
        approvalId = 1L;

        NsApprovalProcess bean = nsApprovalProcessService.findById(approvalId);
        if (bean == null)
            throw ClientException.of("NsApprovalProcess not found error!");

        log.info("findById NsApprovalProcessTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（审批流程表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 15:16:03
     */
    @Test
    public void listPageNsApprovalProcessTest() {
        NsApprovalProcessForm nsApprovalProcessVo = new NsApprovalProcessForm();
        nsApprovalProcessVo.setPageNum(1);
        nsApprovalProcessVo.setPageSize(10);
        log.info("listPage NsApprovalProcessTest result:");
        log.info(getDataTable(nsApprovalProcessService.listPage(nsApprovalProcessVo)).toString());
    }

    /**
     * 删除测试（审批流程表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 15:16:03
     */
    @Test
    public void deleteNsApprovalProcessTest() {
        Long id = 0L;
        log.info("delete NsApprovalProcessTest result:");
        log.info(AjaxResult.success(nsApprovalProcessService.deleteNsApprovalProcess(id)).toString());
    }

    /**
     * 新增或保存测试（审批流程表）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 15:16:03
     */
    @Test
    public void saveNsApprovalProcessTest () {
        NsApprovalProcess nsApprovalProcess = new NsApprovalProcess();
        // 审批流程ID
        // nsApprovalProcess.setApprovalId(0L);
        // 发起人
        nsApprovalProcess.setInitiator("发起人");
        // 发起人ID
        nsApprovalProcess.setInitiatorId(0L);
        // 发起时间
        nsApprovalProcess.setInitiationTime(new Date());
        // 业务类型
        nsApprovalProcess.setBusinessType("业务类型");
        // 业务ID
        nsApprovalProcess.setBusinessId(0L);
        // 审批内容
        nsApprovalProcess.setApprovalContent("审批内容");
        // 审批状态:0-待审批，1-已驳回，2-已完结
        nsApprovalProcess.setApprovalStatus(0);
        // 审批人
        nsApprovalProcess.setApprover("审批人");
        // 审批人ID
        nsApprovalProcess.setApprovedBy(0L);
        // 审批时间
        nsApprovalProcess.setApprovedTime(new Date());
        // 删除标志（0代表存在，1代表删除）
        nsApprovalProcess.setDeleted(0);
        // 创建人
        nsApprovalProcess.setCreateBy(0L);
        // 创建时间
        nsApprovalProcess.setCreateTime(new Date());
        // 更新人
        nsApprovalProcess.setUpdateBy(0L);
        // 更新时间
        nsApprovalProcess.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsApprovalProcessApi.validatePo(nsApprovalProcess);

        // 名称唯一性检查?
        // nsApprovalProcessService.checkNameExisted(nsApprovalProcess);

        NsApprovalProcess result = nsApprovalProcessService.saveNsApprovalProcess(nsApprovalProcess);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（审批流程表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 15:16:03
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("审批流程表");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsApprovalProcess");
        // 组件路径
        menu.setComponent("business/nsApprovalProcess/nsApprovalProcess");
        // 权限标识
        menu.setPerms("business:nsApprovalProcess:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("周建宇");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("审批流程表", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "审批流程表-查询", 1, "business:nsApprovalProcess:list");
            addOperationMenu(menu.getMenuId(), "审批流程表-新增", 2, "business:nsApprovalProcess:add");
            addOperationMenu(menu.getMenuId(), "审批流程表-修改", 3, "business:nsApprovalProcess:edit");
            addOperationMenu(menu.getMenuId(), "审批流程表-删除", 4, "business:nsApprovalProcess:remove");
            addOperationMenu(menu.getMenuId(), "审批流程表-导出", 5, "business:nsApprovalProcess:export");
            addOperationMenu(menu.getMenuId(), "审批流程表-导入", 6, "business:nsApprovalProcess:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsApprovalProcess ok");
        }
    }

    /**
     * 创建操作按钮权限（审批流程表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 15:16:03
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（审批流程表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 15:16:03
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("审批流程表", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsApprovalProcess menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
