package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsSpec;
import com.jorchi.business.service.NsSpecService;
import com.jorchi.business.form.NsSpecForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartHttpServletRequest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ns_spec-模块测试<br/>
 * 对应表名：ns_spec，表备注：规格表
 * @author: 周建宇 at jorchi
 * @date: 2025-06-14 10:50:47
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsSpecTester extends BaseApi {

    @Resource
    NsSpecService nsSpecService;

    @Resource
    NsSpecApi nsSpecApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（规格表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-06-14 10:50:47
     */
    @Test
    public void findByIdNsSpecTest() {
        Long specId;
        specId = 1L;

        NsSpec bean = nsSpecService.findById(specId);
        if (bean == null)
            throw ClientException.of("NsSpec not found error!");

        log.info("findById NsSpecTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（规格表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-06-14 10:50:47
     */
    @Test
    public void listPageNsSpecTest() {
        NsSpecForm nsSpecVo = new NsSpecForm();
        nsSpecVo.setPageNum(1);
        nsSpecVo.setPageSize(10);
        log.info("listPage NsSpecTest result:");
        log.info(getDataTable(nsSpecService.listPage(nsSpecVo)).toString());
    }

    /**
     * 删除测试（规格表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-06-14 10:50:47
     */
    @Test
    public void deleteNsSpecTest() {
        Long id = 0L;
        log.info("delete NsSpecTest result:");
        log.info(AjaxResult.success(nsSpecService.deleteNsSpec(id)).toString());
    }

    /**
     * 新增或保存测试（规格表）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2025-06-14 10:50:47
     */
    @Test
    public void saveNsSpecTest () {
        NsSpec nsSpec = new NsSpec();
        // 规格ID
        // nsSpec.setSpecId(0L);
        // 规格类型:1-投入品,2-农产品
        // nsSpec.setSpecType(0);
        // 投入品类型：2=肥料，3=植保剂，4=种子，5=农膜，6=其他
        nsSpec.setInputType(0);
        // 农产品ID
        nsSpec.setProductStockId(0L);
        // 农产品或者投入品名称
        nsSpec.setName("农产品或者投入品名称");
        // 单位，例如kg
        nsSpec.setUnit("单位，例如kg");
        // 打包单位,例如袋
        nsSpec.setPackageUnit("打包单位,例如袋");
        // 规格说明
        nsSpec.setMark("规格说明");
        // 删除标志
        nsSpec.setDeleted(0);
        // 创建人
        nsSpec.setCreateBy(0L);
        // 创建时间
        nsSpec.setCreateTime(new Date());
        // 更新人
        nsSpec.setUpdateBy(0L);
        // 更新时间
        nsSpec.setUpdateTime(new Date());
        // 农场id
        nsSpec.setRegionDeptId(0L);

        // 检查待保存数据的合法性
        nsSpecApi.validatePo(nsSpec);

        // 名称唯一性检查?
        // nsSpecService.checkNameExisted(nsSpec);

        NsSpec result = nsSpecService.saveNsSpec(nsSpec);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（规格表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-06-14 10:50:47
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("规格表");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsSpec");
        // 组件路径
        menu.setComponent("business/nsSpec/nsSpec");
        // 权限标识
        menu.setPerms("business:nsSpec:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("周建宇");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("规格表", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "规格表-查询", 1, "business:nsSpec:list");
            addOperationMenu(menu.getMenuId(), "规格表-新增", 2, "business:nsSpec:add");
            addOperationMenu(menu.getMenuId(), "规格表-修改", 3, "business:nsSpec:edit");
            addOperationMenu(menu.getMenuId(), "规格表-删除", 4, "business:nsSpec:remove");
            addOperationMenu(menu.getMenuId(), "规格表-导出", 5, "business:nsSpec:export");
            addOperationMenu(menu.getMenuId(), "规格表-导入", 6, "business:nsSpec:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsSpec ok");
        }
    }

    /**
     * 创建操作按钮权限（规格表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-06-14 10:50:47
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（规格表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-06-14 10:50:47
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("规格表", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsSpec menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
