package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsFertilizerSelect;
import com.jorchi.business.service.NsFertilizerSelectService;
import com.jorchi.business.form.NsFertilizerSelectForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.util.Date;

/**
 * ns_fertilizer_select-模块测试<br/>
 * 对应表名：ns_fertilizer_select，表备注：种植项配置（选择肥料）
 * @author: xubinbin at jorchi
 * @date: 2023-01-16 15:46:22
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsFertilizerSelectTester extends BaseApi {

    @Resource
    NsFertilizerSelectService nsFertilizerSelectService;

    @Resource
    NsFertilizerSelectApi nsFertilizerSelectApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（种植项配置（选择肥料））<br>
     * @author: xubinbin at jorchi
     * @date: 2023-01-16 15:46:22
     */
    @Test
    public void findByIdNsFertilizerSelectTest() {
        Long fertilizerId;
        fertilizerId = 1L;

        NsFertilizerSelect bean = nsFertilizerSelectService.findById(fertilizerId);
        if (bean == null)
            throw ClientException.of("NsFertilizerSelect not found error!");

        log.info("findById NsFertilizerSelectTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（种植项配置（选择肥料））<br>
     * @author: xubinbin at jorchi
     * @date: 2023-01-16 15:46:22
     */
    @Test
    public void listPageNsFertilizerSelectTest() {
        NsFertilizerSelectForm nsFertilizerSelectVo = new NsFertilizerSelectForm();
        nsFertilizerSelectVo.setPageNum(1);
        nsFertilizerSelectVo.setPageSize(10);
        log.info("listPage NsFertilizerSelectTest result:");
        log.info(getDataTable(nsFertilizerSelectService.listPage(nsFertilizerSelectVo)).toString());
    }

    /**
     * 删除测试（种植项配置（选择肥料））<br>
     * @author: xubinbin at jorchi
     * @date: 2023-01-16 15:46:22
     */
    @Test
    public void deleteNsFertilizerSelectTest() {
        Long id = 0L;
        log.info("delete NsFertilizerSelectTest result:");
        log.info(AjaxResult.success(nsFertilizerSelectService.deleteNsFertilizerSelect(id)).toString());
    }

    /**
     * 新增或保存测试（种植项配置（选择肥料））<br>
     * ID 为空即为新增，否则为更新
     * @author: xubinbin at jorchi
     * @date: 2023-01-16 15:46:22
     */
    @Test
    public void saveNsFertilizerSelectTest () {
        NsFertilizerSelect nsFertilizerSelect = new NsFertilizerSelect();
        // 选择肥料表id
        // nsFertilizerSelect.setFertilizerId(0L);
        // 种植项id
        // nsFertilizerSelect.setPlantingClauseId(0L);
        // 产品名称
        nsFertilizerSelect.setFertilizerName("产品名称");
        // 使用方法
        nsFertilizerSelect.setUseMethod("使用方法");
        // 使用量（公斤/棚）
        nsFertilizerSelect.setUseDosage("使用量（公斤/棚）");
        // 每平米用量
        nsFertilizerSelect.setAvgDosage("每平米用量");
        // 删除标志（0代表存在2代表删除）
        nsFertilizerSelect.setDeleted(0);
        // 创建人
        nsFertilizerSelect.setCreateBy(0L);
        // 创建时间
        nsFertilizerSelect.setCreateTime(new Date());
        // 更新人
        nsFertilizerSelect.setUpdateBy(0L);
        // 更新时间
        nsFertilizerSelect.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsFertilizerSelectApi.validatePo(nsFertilizerSelect);

        // 名称唯一性检查?
        // nsFertilizerSelectService.checkNameExisted(nsFertilizerSelect);

        NsFertilizerSelect result = nsFertilizerSelectService.saveNsFertilizerSelect(nsFertilizerSelect);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（种植项配置（选择肥料））<br>
     * @author: xubinbin at jorchi
     * @date: 2023-01-16 15:46:22
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("种植项配置（选择肥料）");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsFertilizerSelect");
        // 组件路径
        menu.setComponent("business/nsFertilizerSelect/nsFertilizerSelect");
        // 权限标识
        menu.setPerms("business:nsFertilizerSelect:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("xubinbin");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("种植项配置（选择肥料）", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "种植项配置（选择肥料）-查询", 1, "business:nsFertilizerSelect:list");
            addOperationMenu(menu.getMenuId(), "种植项配置（选择肥料）-新增", 2, "business:nsFertilizerSelect:add");
            addOperationMenu(menu.getMenuId(), "种植项配置（选择肥料）-修改", 3, "business:nsFertilizerSelect:edit");
            addOperationMenu(menu.getMenuId(), "种植项配置（选择肥料）-删除", 4, "business:nsFertilizerSelect:remove");
            addOperationMenu(menu.getMenuId(), "种植项配置（选择肥料）-导出", 5, "business:nsFertilizerSelect:export");
            addOperationMenu(menu.getMenuId(), "种植项配置（选择肥料）-导入", 6, "business:nsFertilizerSelect:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsFertilizerSelect ok");
        }
    }

    /**
     * 创建操作按钮权限（种植项配置（选择肥料））<br>
     * @author: xubinbin at jorchi
     * @date: 2023-01-16 15:46:22
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（种植项配置（选择肥料））<br>
     * @author: xubinbin at jorchi
     * @date: 2023-01-16 15:46:22
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("种植项配置（选择肥料）", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsFertilizerSelect menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
