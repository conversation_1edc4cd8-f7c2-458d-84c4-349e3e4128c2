package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsHouse;
import com.jorchi.business.service.NsHouseService;
import com.jorchi.business.form.NsHouseForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.util.Date;

/**
 * ns_house-模块测试<br/>
 * 对应表名：ns_house，表备注：大棚
 * @author: ChenLiFeng at jorchi
 * @date: 2022-11-10 14:55:56
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsHouseTester extends BaseApi {

    @Resource
    NsHouseService nsHouseService;

    @Resource
    NsHouseApi nsHouseApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（大棚）<br>
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-10 14:55:56
     */
    @Test
    public void findByIdNsHouseTest() {
        Long houseId;
        houseId = 1L;

        NsHouse bean = nsHouseService.findById(houseId);
        if (bean == null)
            throw ClientException.of("NsHouse not found error!");

        log.info("findById NsHouseTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（大棚）<br>
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-10 14:55:56
     */
    @Test
    public void listPageNsHouseTest() {
        NsHouseForm nsHouseVo = new NsHouseForm();
        nsHouseVo.setPageNum(1);
        nsHouseVo.setPageSize(10);
        log.info("listPage NsHouseTest result:");
        log.info(getDataTable(nsHouseService.listPage(nsHouseVo)).toString());
    }

    /**
     * 删除测试（大棚）<br>
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-10 14:55:56
     */
    @Test
    public void deleteNsHouseTest() {
        Long id = 0L;
        log.info("delete NsHouseTest result:");
        log.info(AjaxResult.success(nsHouseService.deleteNsHouse(id)).toString());
    }

    /**
     * 新增或保存测试（大棚）<br>
     * ID 为空即为新增，否则为更新
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-10 14:55:56
     */
    @Test
    public void saveNsHouseTest () {
        NsHouse nsHouse = new NsHouse();
        // 大棚ID
        // nsHouse.setHouseId(0L);
        // 大棚编号
        nsHouse.setHouseName("大棚编号");
        // 区块ID
        nsHouse.setHouseRegionId(0L);
        // 区块名称
        nsHouse.setHouseRegionName("区块名称");
        // 大棚面积
        nsHouse.setHouseArea("大棚面积");
        // 生产技术人员ID
        nsHouse.setTechnicianId(0L);
        // 生产技术人员姓名
        nsHouse.setTechnicianName("生产技术人员姓名");
        // 代理人ID
        nsHouse.setTechnicianAgentId(0L);
        // 代理人名称
        nsHouse.setTechnicianAgentName("代理人名称");
        // 区域坐标点
        nsHouse.setHousePoints("区域坐标点");
        // 删除标志（0代表存在2代表删除）
        nsHouse.setDeleted(0);
        // 创建人
        nsHouse.setCreateBy(0L);
        // 创建时间
        nsHouse.setCreateTime(new Date());
        // 更新人
        nsHouse.setUpdateBy(0L);
        // 更新时间
        nsHouse.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsHouseApi.validatePo(nsHouse);

        // 名称唯一性检查?
        // nsHouseService.checkNameExisted(nsHouse);

        NsHouse result = nsHouseService.saveNsHouse(nsHouse);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（大棚）<br>
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-10 14:55:56
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("大棚");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsHouse");
        // 组件路径
        menu.setComponent("business/nsHouse/nsHouse");
        // 权限标识
        menu.setPerms("business:nsHouse:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("ChenLiFeng");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("大棚", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "大棚-查询", 1, "business:nsHouse:list");
            addOperationMenu(menu.getMenuId(), "大棚-新增", 2, "business:nsHouse:add");
            addOperationMenu(menu.getMenuId(), "大棚-修改", 3, "business:nsHouse:edit");
            addOperationMenu(menu.getMenuId(), "大棚-删除", 4, "business:nsHouse:remove");
//            addOperationMenu(menu.getMenuId(), "大棚-导出", 5, "business:nsHouse:export");
//            addOperationMenu(menu.getMenuId(), "大棚-导入", 6, "business:nsHouse:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsHouse ok");
        }
    }

    /**
     * 创建操作按钮权限（大棚）<br>
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-10 14:55:56
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（大棚）<br>
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-10 14:55:56
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("大棚", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsHouse menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
