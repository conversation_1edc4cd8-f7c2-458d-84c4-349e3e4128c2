package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsAgriculturalMaterials;
import com.jorchi.business.service.NsAgriculturalMaterialsService;
import com.jorchi.business.form.NsAgriculturalMaterialsForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartHttpServletRequest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.util.Date;

/**
 * ns_agricultural_materials-模块测试<br/>
 * 对应表名：ns_agricultural_materials，表备注：农资管理主表
 * @author: GeSenQi at jorchi
 * @date: 2022-10-27 17:50:15
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsAgriculturalMaterialsTester extends BaseApi {

    @Resource
    NsAgriculturalMaterialsService nsAgriculturalMaterialsService;

    @Resource
    NsAgriculturalMaterialsApi nsAgriculturalMaterialsApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（农资管理主表）<br>
     * @author: GeSenQi at jorchi
     * @date: 2022-10-27 17:50:15
     */
    @Test
    public void findByIdNsAgriculturalMaterialsTest() {
        Long agriculturalMaterialsId;
        agriculturalMaterialsId = 1L;

        NsAgriculturalMaterials bean = nsAgriculturalMaterialsService.findById(agriculturalMaterialsId);
        if (bean == null)
            throw ClientException.of("NsAgriculturalMaterials not found error!");

        log.info("findById NsAgriculturalMaterialsTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（农资管理主表）<br>
     * @author: GeSenQi at jorchi
     * @date: 2022-10-27 17:50:15
     */
    @Test
    public void listPageNsAgriculturalMaterialsTest() {
        NsAgriculturalMaterialsForm nsAgriculturalMaterialsVo = new NsAgriculturalMaterialsForm();
        nsAgriculturalMaterialsVo.setPageNum(1);
        nsAgriculturalMaterialsVo.setPageSize(10);
        log.info("listPage NsAgriculturalMaterialsTest result:");
/*        log.info(getDataTable(nsAgriculturalMaterialsService.listPage(nsAgriculturalMaterialsVo)).toString());*/
    }

//    /**
//     * 删除测试（农资管理主表）<br>
//     * @author: GeSenQi at jorchi
//     * @date: 2022-10-27 17:50:15
//     */
//    @Test
//    public void deleteNsAgriculturalMaterialsTest() {
//        Long id = 0L;
//        log.info("delete NsAgriculturalMaterialsTest result:");
//        log.info(AjaxResult.success(nsAgriculturalMaterialsService.deleteNsAgriculturalMaterials(id)).toString());
//    }
//
//    /**
//     * 新增或保存测试（农资管理主表）<br>
//     * ID 为空即为新增，否则为更新
//     * @author: GeSenQi at jorchi
//     * @date: 2022-10-27 17:50:15
//     */
//    @Test
//    public void saveNsAgriculturalMaterialsTest () {
//        NsAgriculturalMaterials nsAgriculturalMaterials = new NsAgriculturalMaterials();
//        // 主键
//        // nsAgriculturalMaterials.setAgriculturalMaterialsId(0L);
//        // 厂牌名称（公司名称）
//        nsAgriculturalMaterials.setBrandName("厂牌名称（公司名称）");
//        // 有效成分
//        nsAgriculturalMaterials.setActiveIngredient("有效成分");
//        // 登记证号
//        nsAgriculturalMaterials.setRegistrationNo("登记证号");
//        // 含量剂型
//        nsAgriculturalMaterials.setDosageForm("含量剂型");
//        // 包装量
//        nsAgriculturalMaterials.setPackQuantity(0);
//        // 包装单位
//        nsAgriculturalMaterials.setPackUnit("包装单位");
//        // 库存量
//        nsAgriculturalMaterials.setStock(0);
//        // 库存单位
//        nsAgriculturalMaterials.setStockUnit("库存单位");
//        // 库存重量
//        nsAgriculturalMaterials.setStockWeight(0);
//        // 有效期
//        nsAgriculturalMaterials.setValidity(new Date());
//        // 删除标志（0代表存在2代表删除）
//        nsAgriculturalMaterials.setDeleted(0);
//        // 创建人
//        nsAgriculturalMaterials.setCreateBy(0L);
//        // 创建时间
//        nsAgriculturalMaterials.setCreateTime(new Date());
//        // 更新人
//        nsAgriculturalMaterials.setUpdateBy(0L);
//        // 更新时间
//        nsAgriculturalMaterials.setUpdateTime(new Date());
//
//        // 检查待保存数据的合法性
//        nsAgriculturalMaterialsApi.validatePo(nsAgriculturalMaterials);
//
//        // 名称唯一性检查?
//        // nsAgriculturalMaterialsService.checkNameExisted(nsAgriculturalMaterials);
//
//        NsAgriculturalMaterials result = nsAgriculturalMaterialsService.saveNsAgriculturalMaterials(nsAgriculturalMaterials);
//        log.info("save result:");
//        log.info(result.toString());
//    }


    /**
     * 生成菜单（农资管理主表）<br>
     * @author: GeSenQi at jorchi
     * @date: 2022-10-27 17:50:15
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("农资管理主表");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsAgriculturalMaterials");
        // 组件路径
        menu.setComponent("business/nsAgriculturalMaterials/nsAgriculturalMaterials");
        // 权限标识
        menu.setPerms("business:nsAgriculturalMaterials:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("GeSenQi");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("农资管理主表", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "农资管理主表-查询", 1, "business:nsAgriculturalMaterials:list");
            addOperationMenu(menu.getMenuId(), "农资管理主表-新增", 2, "business:nsAgriculturalMaterials:add");
            addOperationMenu(menu.getMenuId(), "农资管理主表-修改", 3, "business:nsAgriculturalMaterials:edit");
            addOperationMenu(menu.getMenuId(), "农资管理主表-删除", 4, "business:nsAgriculturalMaterials:remove");
            addOperationMenu(menu.getMenuId(), "农资管理主表-导出", 5, "business:nsAgriculturalMaterials:export");
            addOperationMenu(menu.getMenuId(), "农资管理主表-导入", 6, "business:nsAgriculturalMaterials:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsAgriculturalMaterials ok");
        }
    }

    /**
     * 创建操作按钮权限（农资管理主表）<br>
     * @author: GeSenQi at jorchi
     * @date: 2022-10-27 17:50:15
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（农资管理主表）<br>
     * @author: GeSenQi at jorchi
     * @date: 2022-10-27 17:50:15
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("农资管理主表", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsAgriculturalMaterials menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
