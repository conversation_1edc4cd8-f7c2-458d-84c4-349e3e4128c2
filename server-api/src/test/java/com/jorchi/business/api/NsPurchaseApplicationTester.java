package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.dto.AddPurchaseApplicationDto;
import com.jorchi.business.po.NsPurchaseApplication;
import com.jorchi.business.service.NsPurchaseApplicationService;
import com.jorchi.business.form.NsPurchaseApplicationForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartHttpServletRequest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ns_purchase_application-模块测试<br/>
 * 对应表名：ns_purchase_application，表备注：采购申请表
 * @author: 周建宇 at jorchi
 * @date: 2024-12-25 09:16:48
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsPurchaseApplicationTester extends BaseApi {

    @Resource
    NsPurchaseApplicationService nsPurchaseApplicationService;

    @Resource
    NsPurchaseApplicationApi nsPurchaseApplicationApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（采购申请表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 09:16:48
     */
    @Test
    public void findByIdNsPurchaseApplicationTest() {
        Long purchaseId;
        purchaseId = 1L;

        NsPurchaseApplication bean = nsPurchaseApplicationService.findById(purchaseId);
        if (bean == null)
            throw ClientException.of("NsPurchaseApplication not found error!");

        log.info("findById NsPurchaseApplicationTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（采购申请表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 09:16:48
     */
    @Test
    public void listPageNsPurchaseApplicationTest() {
        NsPurchaseApplicationForm nsPurchaseApplicationVo = new NsPurchaseApplicationForm();
        nsPurchaseApplicationVo.setPageNum(1);
        nsPurchaseApplicationVo.setPageSize(10);
        log.info("listPage NsPurchaseApplicationTest result:");
        log.info(getDataTable(nsPurchaseApplicationService.listPage(nsPurchaseApplicationVo)).toString());
    }

    /**
     * 删除测试（采购申请表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 09:16:48
     */
    @Test
    public void deleteNsPurchaseApplicationTest() {
        Long id = 0L;
        log.info("delete NsPurchaseApplicationTest result:");
        log.info(AjaxResult.success(nsPurchaseApplicationService.deleteNsPurchaseApplication(id)).toString());
    }

    /**
     * 新增或保存测试（采购申请表）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 09:16:48
     */
    @Test
    public void saveNsPurchaseApplicationTest () {
        AddPurchaseApplicationDto nsPurchaseApplication = new AddPurchaseApplicationDto();
        // 采购单号
        // nsPurchaseApplication.setPurchaseId(0L);
        // 申请人
        nsPurchaseApplication.setApplicant("申请人");
        // 申请日期
        nsPurchaseApplication.setApplicationDate(new Date());
        // 申请事由
        nsPurchaseApplication.setApplicationReason("申请事由");
        // 申请状态
        nsPurchaseApplication.setApplicationStatus(0);
        // 审批人
        nsPurchaseApplication.setApprovedBy(0L);
        // 审批时间
        nsPurchaseApplication.setApprovedTime(new Date());
        // 删除标志（0代表存在，2代表删除）
        nsPurchaseApplication.setDeleted(0);
        // 创建人
        nsPurchaseApplication.setCreateBy(0L);
        // 创建时间
        nsPurchaseApplication.setCreateTime(new Date());
        // 更新人
        nsPurchaseApplication.setUpdateBy(0L);
        // 更新时间
        nsPurchaseApplication.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsPurchaseApplicationApi.validatePo(nsPurchaseApplication);

        // 名称唯一性检查?
        // nsPurchaseApplicationService.checkNameExisted(nsPurchaseApplication);

        NsPurchaseApplication result = nsPurchaseApplicationService.saveNsPurchaseApplication(nsPurchaseApplication);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（采购申请表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 09:16:48
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("采购申请表");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsPurchaseApplication");
        // 组件路径
        menu.setComponent("business/nsPurchaseApplication/nsPurchaseApplication");
        // 权限标识
        menu.setPerms("business:nsPurchaseApplication:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("周建宇");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("采购申请表", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "采购申请表-查询", 1, "business:nsPurchaseApplication:list");
            addOperationMenu(menu.getMenuId(), "采购申请表-新增", 2, "business:nsPurchaseApplication:add");
            addOperationMenu(menu.getMenuId(), "采购申请表-修改", 3, "business:nsPurchaseApplication:edit");
            addOperationMenu(menu.getMenuId(), "采购申请表-删除", 4, "business:nsPurchaseApplication:remove");
            addOperationMenu(menu.getMenuId(), "采购申请表-导出", 5, "business:nsPurchaseApplication:export");
            addOperationMenu(menu.getMenuId(), "采购申请表-导入", 6, "business:nsPurchaseApplication:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsPurchaseApplication ok");
        }
    }

    /**
     * 创建操作按钮权限（采购申请表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 09:16:48
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（采购申请表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-25 09:16:48
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("采购申请表", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsPurchaseApplication menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
