package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsPesticideSelect;
import com.jorchi.business.service.NsPesticideSelectService;
import com.jorchi.business.form.NsPesticideSelectForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.util.Date;

/**
 * ns_pesticide_select-模块测试<br/>
 * 对应表名：ns_pesticide_select，表备注：种植项配置（选择农药）
 * @author: xubinbin at jorchi
 * @date: 2023-01-16 15:46:12
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsPesticideSelectTester extends BaseApi {

    @Resource
    NsPesticideSelectService nsPesticideSelectService;

    @Resource
    NsPesticideSelectApi nsPesticideSelectApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（种植项配置（选择农药））<br>
     * @author: xubinbin at jorchi
     * @date: 2023-01-16 15:46:12
     */
    @Test
    public void findByIdNsPesticideSelectTest() {
        Long pesticideId;
        pesticideId = 1L;

        NsPesticideSelect bean = nsPesticideSelectService.findById(pesticideId);
        if (bean == null)
            throw ClientException.of("NsPesticideSelect not found error!");

        log.info("findById NsPesticideSelectTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（种植项配置（选择农药））<br>
     * @author: xubinbin at jorchi
     * @date: 2023-01-16 15:46:12
     */
    @Test
    public void listPageNsPesticideSelectTest() {
        NsPesticideSelectForm nsPesticideSelectVo = new NsPesticideSelectForm();
        nsPesticideSelectVo.setPageNum(1);
        nsPesticideSelectVo.setPageSize(10);
        log.info("listPage NsPesticideSelectTest result:");
        log.info(getDataTable(nsPesticideSelectService.listPage(nsPesticideSelectVo)).toString());
    }

    /**
     * 删除测试（种植项配置（选择农药））<br>
     * @author: xubinbin at jorchi
     * @date: 2023-01-16 15:46:12
     */
    @Test
    public void deleteNsPesticideSelectTest() {
        Long id = 0L;
        log.info("delete NsPesticideSelectTest result:");
        log.info(AjaxResult.success(nsPesticideSelectService.deleteNsPesticideSelect(id)).toString());
    }

    /**
     * 新增或保存测试（种植项配置（选择农药））<br>
     * ID 为空即为新增，否则为更新
     * @author: xubinbin at jorchi
     * @date: 2023-01-16 15:46:12
     */
    @Test
    public void saveNsPesticideSelectTest () {
        NsPesticideSelect nsPesticideSelect = new NsPesticideSelect();
        // 选择农药表id
        // nsPesticideSelect.setPesticideId(0L);
        // 种植项id
        // nsPesticideSelect.setPlantingClauseId(0L);
        // 成分含量剂型
        nsPesticideSelect.setComposition("成分含量剂型");
        // 供应商
        nsPesticideSelect.setSuppliers("供应商");
        // 防治对象
        nsPesticideSelect.setPrevention("防治对象");
        // 每平米用量
        nsPesticideSelect.setAvgDosage("每平米用量");
        // 删除标志（0代表存在2代表删除）
        nsPesticideSelect.setDeleted(0);
        // 创建人
        nsPesticideSelect.setCreateBy(0L);
        // 创建时间
        nsPesticideSelect.setCreateTime(new Date());
        // 更新人
        nsPesticideSelect.setUpdateBy(0L);
        // 更新时间
        nsPesticideSelect.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsPesticideSelectApi.validatePo(nsPesticideSelect);

        // 名称唯一性检查?
        // nsPesticideSelectService.checkNameExisted(nsPesticideSelect);

        NsPesticideSelect result = nsPesticideSelectService.saveNsPesticideSelect(nsPesticideSelect);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（种植项配置（选择农药））<br>
     * @author: xubinbin at jorchi
     * @date: 2023-01-16 15:46:12
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("种植项配置（选择农药）");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsPesticideSelect");
        // 组件路径
        menu.setComponent("business/nsPesticideSelect/nsPesticideSelect");
        // 权限标识
        menu.setPerms("business:nsPesticideSelect:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("xubinbin");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("种植项配置（选择农药）", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "种植项配置（选择农药）-查询", 1, "business:nsPesticideSelect:list");
            addOperationMenu(menu.getMenuId(), "种植项配置（选择农药）-新增", 2, "business:nsPesticideSelect:add");
            addOperationMenu(menu.getMenuId(), "种植项配置（选择农药）-修改", 3, "business:nsPesticideSelect:edit");
            addOperationMenu(menu.getMenuId(), "种植项配置（选择农药）-删除", 4, "business:nsPesticideSelect:remove");
            addOperationMenu(menu.getMenuId(), "种植项配置（选择农药）-导出", 5, "business:nsPesticideSelect:export");
            addOperationMenu(menu.getMenuId(), "种植项配置（选择农药）-导入", 6, "business:nsPesticideSelect:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsPesticideSelect ok");
        }
    }

    /**
     * 创建操作按钮权限（种植项配置（选择农药））<br>
     * @author: xubinbin at jorchi
     * @date: 2023-01-16 15:46:12
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（种植项配置（选择农药））<br>
     * @author: xubinbin at jorchi
     * @date: 2023-01-16 15:46:12
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("种植项配置（选择农药）", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsPesticideSelect menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
