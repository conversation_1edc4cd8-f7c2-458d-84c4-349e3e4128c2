package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.dto.AddUseApplicationDto;
import com.jorchi.business.po.NsUseApplication;
import com.jorchi.business.service.NsUseApplicationService;
import com.jorchi.business.form.NsUseApplicationForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartHttpServletRequest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ns_use_application-模块测试<br/>
 * 对应表名：ns_use_application，表备注：领用申请表
 * @author: 周建宇 at jorchi
 * @date: 2025-01-04 12:47:04
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsUseApplicationTester extends BaseApi {

    @Resource
    NsUseApplicationService nsUseApplicationService;

    @Resource
    NsUseApplicationApi nsUseApplicationApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（领用申请表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-04 12:47:04
     */
    @Test
    public void findByIdNsUseApplicationTest() {
        Long useId;
        useId = 1L;

        NsUseApplication bean = nsUseApplicationService.findById(useId);
        if (bean == null)
            throw ClientException.of("NsUseApplication not found error!");

        log.info("findById NsUseApplicationTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（领用申请表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-04 12:47:04
     */
    @Test
    public void listPageNsUseApplicationTest() {
        NsUseApplicationForm nsUseApplicationVo = new NsUseApplicationForm();
        nsUseApplicationVo.setPageNum(1);
        nsUseApplicationVo.setPageSize(10);
        log.info("listPage NsUseApplicationTest result:");
        log.info(getDataTable(nsUseApplicationService.listPage(nsUseApplicationVo)).toString());
    }

    /**
     * 删除测试（领用申请表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-04 12:47:04
     */
    @Test
    public void deleteNsUseApplicationTest() {
        Long id = 0L;
        log.info("delete NsUseApplicationTest result:");
        log.info(AjaxResult.success(nsUseApplicationService.deleteNsUseApplication(id)).toString());
    }

    /**
     * 新增或保存测试（领用申请表）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2025-01-04 12:47:04
     */
    @Test
    public void saveNsUseApplicationTest () {
        AddUseApplicationDto nsUseApplication = new AddUseApplicationDto();
        // 采购id
        // nsUseApplication.setUseId(0L);
        // 采购单号
        nsUseApplication.setUseCode("采购单号");
        // 申请人
        nsUseApplication.setApplicant("申请人");
        // 申请日期
        nsUseApplication.setApplicationDate(new Date());
        // 申请人手机号
        nsUseApplication.setApplicantMobile("申请人手机号");
        // 申请人ID
        nsUseApplication.setApplicantId(0L);
        // 申请事由
        nsUseApplication.setApplicationReason("申请事由");
        // 申请状态:0-未发起，1-待审批，2-已驳回，3-已完结
        nsUseApplication.setApplicationStatus(0);
        // 审批人
        nsUseApplication.setApprovedBy(0L);
        // 审批时间
        nsUseApplication.setApprovedTime(new Date());
        // 删除标志（0代表存在，2代表删除）
        nsUseApplication.setDeleted(0);
        // 创建人
        nsUseApplication.setCreateBy(0L);
        // 创建时间
        nsUseApplication.setCreateTime(new Date());
        // 更新人
        nsUseApplication.setUpdateBy(0L);
        // 更新时间
        nsUseApplication.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsUseApplicationApi.validatePo(nsUseApplication);

        // 名称唯一性检查?
        // nsUseApplicationService.checkNameExisted(nsUseApplication);

        NsUseApplication result = nsUseApplicationService.saveNsUseApplication(nsUseApplication);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（领用申请表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-04 12:47:04
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("领用申请表");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsUseApplication");
        // 组件路径
        menu.setComponent("business/nsUseApplication/nsUseApplication");
        // 权限标识
        menu.setPerms("business:nsUseApplication:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("周建宇");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("领用申请表", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "领用申请表-查询", 1, "business:nsUseApplication:list");
            addOperationMenu(menu.getMenuId(), "领用申请表-新增", 2, "business:nsUseApplication:add");
            addOperationMenu(menu.getMenuId(), "领用申请表-修改", 3, "business:nsUseApplication:edit");
            addOperationMenu(menu.getMenuId(), "领用申请表-删除", 4, "business:nsUseApplication:remove");
            addOperationMenu(menu.getMenuId(), "领用申请表-导出", 5, "business:nsUseApplication:export");
            addOperationMenu(menu.getMenuId(), "领用申请表-导入", 6, "business:nsUseApplication:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsUseApplication ok");
        }
    }

    /**
     * 创建操作按钮权限（领用申请表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-04 12:47:04
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（领用申请表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-04 12:47:04
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("领用申请表", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsUseApplication menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
