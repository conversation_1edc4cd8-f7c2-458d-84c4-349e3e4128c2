package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsProdAudit;
import com.jorchi.business.service.NsProdAuditService;
import com.jorchi.business.form.NsProdAuditForm;
import com.jorchi.framework.security.LoginUser;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.util.Date;

/**
 * ns_prod_audit-模块测试<br/>
 * 对应表名：ns_prod_audit，表备注：生产日常稽核表
 * @author: xubinbin at jorchi
 * @date: 2023-05-11 16:09:21
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsProdAuditTester extends BaseApi {

    @Resource
    NsProdAuditService nsProdAuditService;

    @Resource
    NsProdAuditApi nsProdAuditApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（生产日常稽核表）<br>
     * @author: xubinbin at jorchi
     * @date: 2023-05-11 16:09:21
     */
    @Test
    public void findByIdNsProdAuditTest() {
        Long id;
        id = 1L;

        NsProdAudit bean = nsProdAuditService.findById(id);
        if (bean == null)
            throw ClientException.of("NsProdAudit not found error!");

        log.info("findById NsProdAuditTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（生产日常稽核表）<br>
     * @author: xubinbin at jorchi
     * @date: 2023-05-11 16:09:21
     */
    @Test
    public void listPageNsProdAuditTest() {
        NsProdAuditForm nsProdAuditVo = new NsProdAuditForm();
        nsProdAuditVo.setPageNum(1);
        nsProdAuditVo.setPageSize(10);
        log.info("listPage NsProdAuditTest result:");
        log.info(getDataTable(nsProdAuditService.listPage(nsProdAuditVo)).toString());
    }

    /**
     * 删除测试（生产日常稽核表）<br>
     * @author: xubinbin at jorchi
     * @date: 2023-05-11 16:09:21
     */
    @Test
    public void deleteNsProdAuditTest() {
        Long id = 0L;
        log.info("delete NsProdAuditTest result:");
        log.info(AjaxResult.success(nsProdAuditService.deleteNsProdAudit(id)).toString());
    }

    /**
     * 新增或保存测试（生产日常稽核表）<br>
     * ID 为空即为新增，否则为更新
     * @author: xubinbin at jorchi
     * @date: 2023-05-11 16:09:21
     */
    @Test
    public void saveNsProdAuditTest () {
        NsProdAudit nsProdAudit = new NsProdAudit();
        // 日常稽核表主键
        // nsProdAudit.setId(0L);
        // 种植区id
        // nsProdAudit.setRegionId(0L);
        // 种植区名称
        nsProdAudit.setRegionName("种植区名称");
        // 大棚id
        nsProdAudit.setHouseId(0L);
        // 大棚编号
        nsProdAudit.setHouseName("大棚编号");
        // 作物名称
//        nsProdAudit.setCropName(0);
        // 外观
        nsProdAudit.setAspect("外观");
        // 病害
        nsProdAudit.setDisease("病害");
        // 虫害
        nsProdAudit.setInsectPest("虫害");
        // 棚膜
        nsProdAudit.setCanopyFilm("棚膜");
        // 大门
        nsProdAudit.setDoor("大门");
        // 异常说明
        nsProdAudit.setExceptionDescription("异常说明");
        // 删除标志（0代表存在2代表删除）
        nsProdAudit.setDeleted(0);
        // 创建人
        nsProdAudit.setCreateBy(0L);
        // 创建时间
        nsProdAudit.setCreateTime(new Date());
        // 更新人
        nsProdAudit.setUpdateBy(0L);
        // 更新时间
        nsProdAudit.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsProdAuditApi.validatePo(nsProdAudit);

        // 名称唯一性检查?
        // nsProdAuditService.checkNameExisted(nsProdAudit);

//        NsProdAudit result = nsProdAuditService.saveNsProdAudit(nsProdAudit);
        log.info("save result:");
//        log.info(result.toString());
    }


    /**
     * 生成菜单（生产日常稽核表）<br>
     * @author: xubinbin at jorchi
     * @date: 2023-05-11 16:09:21
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("生产日常稽核表");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsProdAudit");
        // 组件路径
        menu.setComponent("business/nsProdAudit/nsProdAudit");
        // 权限标识
        menu.setPerms("business:nsProdAudit:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("xubinbin");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("生产日常稽核表", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "生产日常稽核表-查询", 1, "business:nsProdAudit:list");
            addOperationMenu(menu.getMenuId(), "生产日常稽核表-新增", 2, "business:nsProdAudit:add");
            addOperationMenu(menu.getMenuId(), "生产日常稽核表-修改", 3, "business:nsProdAudit:edit");
            addOperationMenu(menu.getMenuId(), "生产日常稽核表-删除", 4, "business:nsProdAudit:remove");
            addOperationMenu(menu.getMenuId(), "生产日常稽核表-导出", 5, "business:nsProdAudit:export");
            addOperationMenu(menu.getMenuId(), "生产日常稽核表-导入", 6, "business:nsProdAudit:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsProdAudit ok");
        }
    }

    /**
     * 创建操作按钮权限（生产日常稽核表）<br>
     * @author: xubinbin at jorchi
     * @date: 2023-05-11 16:09:21
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（生产日常稽核表）<br>
     * @author: xubinbin at jorchi
     * @date: 2023-05-11 16:09:21
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("生产日常稽核表", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsProdAudit menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
