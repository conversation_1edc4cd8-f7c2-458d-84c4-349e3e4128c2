package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsInputStockTaking;
import com.jorchi.business.service.NsInputStockTakingService;
import com.jorchi.business.form.NsInputStockTakingForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartHttpServletRequest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;

/**
 * ns_input_stock_taking-模块测试<br/>
 * 对应表名：ns_input_stock_taking，表备注：投入品库存盘点表
 * @author: 周建宇 at jorchi
 * @date: 2025-01-09 09:35:47
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsInputStockTakingTester extends BaseApi {

    @Resource
    NsInputStockTakingService nsInputStockTakingService;

    @Resource
    NsInputStockTakingApi nsInputStockTakingApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（投入品库存盘点表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-09 09:35:47
     */
    @Test
    public void findByIdNsInputStockTakingTest() {
        Long takingId;
        takingId = 1L;

        NsInputStockTaking bean = nsInputStockTakingService.findById(takingId);
        if (bean == null)
            throw ClientException.of("NsInputStockTaking not found error!");

        log.info("findById NsInputStockTakingTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（投入品库存盘点表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-09 09:35:47
     */
    @Test
    public void listPageNsInputStockTakingTest() {
        NsInputStockTakingForm nsInputStockTakingVo = new NsInputStockTakingForm();
        nsInputStockTakingVo.setPageNum(1);
        nsInputStockTakingVo.setPageSize(10);
        log.info("listPage NsInputStockTakingTest result:");
        log.info(getDataTable(nsInputStockTakingService.listPage(nsInputStockTakingVo)).toString());
    }

    /**
     * 删除测试（投入品库存盘点表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-09 09:35:47
     */
    @Test
    public void deleteNsInputStockTakingTest() {
        Long id = 0L;
        log.info("delete NsInputStockTakingTest result:");
        log.info(AjaxResult.success(nsInputStockTakingService.deleteNsInputStockTaking(id)).toString());
    }

    /**
     * 新增或保存测试（投入品库存盘点表）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2025-01-09 09:35:47
     */
    @Test
    public void saveNsInputStockTakingTest () {
        NsInputStockTaking nsInputStockTaking = new NsInputStockTaking();
        // 投入品库存盘点ID
        // nsInputStockTaking.setTakingId(0L);
        // 业务ID
        // nsInputStockTaking.setBusinessId(0L);
        // 投入品类型：2=肥料，3=植保剂，4=种子，5=农膜，6=其他
        nsInputStockTaking.setInputType(0);
        // 投入品分类
        nsInputStockTaking.setInputCategory("投入品分类");
        // 投入品类别
        nsInputStockTaking.setInputSubCategory("投入品类别");
        // 投入品名称
        nsInputStockTaking.setInputName("投入品名称");
        // 库存量
        nsInputStockTaking.setStockQuantity(new BigDecimal(0));
        // 盘点数量
        nsInputStockTaking.setTakingStockQuantity(new BigDecimal(0));
        // 仓库ID
        nsInputStockTaking.setHouseId(0L);
        // 仓库名称
        nsInputStockTaking.setHouseName("仓库名称");
        // 盘点结果:1-盘亏，2-盘盈
        nsInputStockTaking.setTakingResult(0);
        // 盘点日期
        nsInputStockTaking.setTakingDate(new Date());
        // 盘点人
        nsInputStockTaking.setTakingOperator("盘点人");
        // 盘点人ID
        nsInputStockTaking.setTakingOperatorId(0L);
        // 删除标记
        nsInputStockTaking.setDeleted(0);
        // 创建人
        nsInputStockTaking.setCreateBy(0L);
        // 创建时间
        nsInputStockTaking.setCreateTime(new Date());
        // 更新人
        nsInputStockTaking.setUpdateBy(0L);
        // 更新时间
        nsInputStockTaking.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsInputStockTakingApi.validatePo(Arrays.asList(nsInputStockTaking));

        // 名称唯一性检查?
        // nsInputStockTakingService.checkNameExisted(nsInputStockTaking);

        NsInputStockTaking result = nsInputStockTakingService.saveNsInputStockTaking(nsInputStockTaking);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（投入品库存盘点表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-09 09:35:47
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("投入品库存盘点表");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsInputStockTaking");
        // 组件路径
        menu.setComponent("business/nsInputStockTaking/nsInputStockTaking");
        // 权限标识
        menu.setPerms("business:nsInputStockTaking:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("周建宇");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("投入品库存盘点表", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "投入品库存盘点表-查询", 1, "business:nsInputStockTaking:list");
            addOperationMenu(menu.getMenuId(), "投入品库存盘点表-新增", 2, "business:nsInputStockTaking:add");
            addOperationMenu(menu.getMenuId(), "投入品库存盘点表-修改", 3, "business:nsInputStockTaking:edit");
            addOperationMenu(menu.getMenuId(), "投入品库存盘点表-删除", 4, "business:nsInputStockTaking:remove");
            addOperationMenu(menu.getMenuId(), "投入品库存盘点表-导出", 5, "business:nsInputStockTaking:export");
            addOperationMenu(menu.getMenuId(), "投入品库存盘点表-导入", 6, "business:nsInputStockTaking:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsInputStockTaking ok");
        }
    }

    /**
     * 创建操作按钮权限（投入品库存盘点表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-09 09:35:47
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（投入品库存盘点表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-09 09:35:47
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("投入品库存盘点表", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsInputStockTaking menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
