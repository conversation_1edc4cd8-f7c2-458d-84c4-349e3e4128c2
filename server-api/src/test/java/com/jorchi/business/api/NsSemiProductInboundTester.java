package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.form.NsSemiProductInboundForm;
import com.jorchi.business.po.NsSemiProductInbound;
import com.jorchi.business.service.NsSemiProductInboundService;
import com.jorchi.business.vo.NsSemiProductInboundVo;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ns_semi_product_inbound-模块测试<br/>
 * 对应表名：ns_semi_product_inbound，表备注：半成品入库表
 * @author: 周建宇 at jorchi
 * @date: 2024-12-05 14:38:40
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class NsSemiProductInboundTester extends BaseApi {

    @Resource
    NsSemiProductInboundService nsSemiProductInboundService;

    @Resource
    NsSemiProductInboundApi nsSemiProductInboundApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（半成品入库表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-05 14:38:40
     */
    @Test
    public void findByIdNsSemiProductInboundTest() {
        Long inboundId;
        inboundId = 1L;

        NsSemiProductInboundVo bean = nsSemiProductInboundService.findById(inboundId);
        if (bean == null)
            throw ClientException.of("NsSemiProductInbound not found error!");

        log.info("findById NsSemiProductInboundTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（半成品入库表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-05 14:38:40
     */
    @Test
    public void listPageNsSemiProductInboundTest() {
        NsSemiProductInboundForm nsSemiProductInboundVo = new NsSemiProductInboundForm();
        nsSemiProductInboundVo.setPageNum(1);
        nsSemiProductInboundVo.setPageSize(10);
        log.info("listPage NsSemiProductInboundTest result:");
        log.info(getDataTable(nsSemiProductInboundService.listPage(nsSemiProductInboundVo)).toString());
    }

    /**
     * 删除测试（半成品入库表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-05 14:38:40
     */
    @Test
    public void deleteNsSemiProductInboundTest() {
        Long id = 0L;
        log.info("delete NsSemiProductInboundTest result:");
        log.info(AjaxResult.success(nsSemiProductInboundService.deleteNsSemiProductInbound(id)).toString());
    }

    /**
     * 新增或保存测试（半成品入库表）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2024-12-05 14:38:40
     */
    @Test
    public void saveNsSemiProductInboundTest () {
        NsSemiProductInbound nsSemiProductInbound = new NsSemiProductInbound();
        // 入库ID
        // nsSemiProductInbound.setInboundId(0L);
        // 采摘人
        nsSemiProductInbound.setPicker(0L);
        // 采摘日期
        nsSemiProductInbound.setPickDate(new Date());
        // 来源大棚
        nsSemiProductInbound.setHouseId(0L);
        // 生产计划
        nsSemiProductInbound.setProductionPlanId(0L);
        // 实际入库量
        nsSemiProductInbound.setActualQuantity(BigDecimal.ONE);
        // 删除标志
        nsSemiProductInbound.setDeleted(0);
        // 创建人
        nsSemiProductInbound.setCreateBy(0L);
        // 创建时间
        nsSemiProductInbound.setCreateTime(new Date());
        // 更新人
        nsSemiProductInbound.setUpdateBy(0L);
        // 更新时间
        nsSemiProductInbound.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsSemiProductInboundApi.validatePo(nsSemiProductInbound);

        // 名称唯一性检查?
        // nsSemiProductInboundService.checkNameExisted(nsSemiProductInbound);

        NsSemiProductInbound result = nsSemiProductInboundService.saveNsSemiProductInbound(nsSemiProductInbound);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（半成品入库表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-05 14:38:40
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("半成品入库表");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsSemiProductInbound");
        // 组件路径
        menu.setComponent("business/nsSemiProductInbound/nsSemiProductInbound");
        // 权限标识
        menu.setPerms("business:nsSemiProductInbound:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("zhoujianyu");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("半成品入库表", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "半成品入库表-查询", 1, "business:nsSemiProductInbound:list");
            addOperationMenu(menu.getMenuId(), "半成品入库表-新增", 2, "business:nsSemiProductInbound:add");
            addOperationMenu(menu.getMenuId(), "半成品入库表-修改", 3, "business:nsSemiProductInbound:edit");
            addOperationMenu(menu.getMenuId(), "半成品入库表-删除", 4, "business:nsSemiProductInbound:remove");
            addOperationMenu(menu.getMenuId(), "半成品入库表-导出", 5, "business:nsSemiProductInbound:export");
            addOperationMenu(menu.getMenuId(), "半成品入库表-导入", 6, "business:nsSemiProductInbound:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsSemiProductInbound ok");
        }
    }

    /**
     * 创建操作按钮权限（半成品入库表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-05 14:38:40
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("zhoujianyu");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（半成品入库表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-05 14:38:40
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("半成品入库表", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsSemiProductInbound menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
