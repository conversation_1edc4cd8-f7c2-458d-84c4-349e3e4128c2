package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsSupplier;
import com.jorchi.business.service.NsSupplierService;
import com.jorchi.business.form.NsSupplierForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartHttpServletRequest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ns_supplier-模块测试<br/>
 * 对应表名：ns_supplier，表备注：供应商表
 * @author: 周建宇 at jorchi
 * @date: 2024-12-24 16:18:12
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsSupplierTester extends BaseApi {

    @Resource
    NsSupplierService nsSupplierService;

    @Resource
    NsSupplierApi nsSupplierApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（供应商表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-24 16:18:12
     */
    @Test
    public void findByIdNsSupplierTest() {
        Long supplierId;
        supplierId = 1L;

        NsSupplier bean = nsSupplierService.findById(supplierId);
        if (bean == null)
            throw ClientException.of("NsSupplier not found error!");

        log.info("findById NsSupplierTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（供应商表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-24 16:18:12
     */
    @Test
    public void listPageNsSupplierTest() {
        NsSupplierForm nsSupplierVo = new NsSupplierForm();
        nsSupplierVo.setPageNum(1);
        nsSupplierVo.setPageSize(10);
        log.info("listPage NsSupplierTest result:");
        log.info(getDataTable(nsSupplierService.listPage(nsSupplierVo)).toString());
    }

    /**
     * 删除测试（供应商表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-24 16:18:12
     */
    @Test
    public void deleteNsSupplierTest() {
        Long id = 0L;
        log.info("delete NsSupplierTest result:");
        log.info(AjaxResult.success(nsSupplierService.deleteNsSupplier(id)).toString());
    }

    /**
     * 新增或保存测试（供应商表）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2024-12-24 16:18:12
     */
    @Test
    public void saveNsSupplierTest () {
        NsSupplier nsSupplier = new NsSupplier();
        // 供应商ID
        // nsSupplier.setSupplierId(0L);
        // 供应商类别
        nsSupplier.setSupplierCategory("供应商类别");
        // 供应商名称
        nsSupplier.setSupplierName("供应商名称");
        // 税号
        nsSupplier.setTaxNumber("税号");
        // 地址
        nsSupplier.setAddress("地址");
        // 开户银行
        nsSupplier.setBankName("开户银行");
        // 银行账号
        nsSupplier.setBankAccount("银行账号");
        // 联系人
        nsSupplier.setContactPerson("联系人");
        // 联系人电话
        nsSupplier.setContactPhone("联系人电话");
        // 删除标志（0代表存在，2代表删除）
        nsSupplier.setDeleted(0);
        // 创建人
        nsSupplier.setCreateBy(0L);
        // 创建时间
        nsSupplier.setCreateTime(new Date());
        // 更新人
        nsSupplier.setUpdateBy(0L);
        // 更新时间
        nsSupplier.setUpdateTime(new Date());
        // 备注
        nsSupplier.setNote("备注");

        // 检查待保存数据的合法性
        nsSupplierApi.validatePo(nsSupplier);

        // 名称唯一性检查?
        // nsSupplierService.checkNameExisted(nsSupplier);

        NsSupplier result = nsSupplierService.saveNsSupplier(nsSupplier);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（供应商表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-24 16:18:12
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("供应商表");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsSupplier");
        // 组件路径
        menu.setComponent("business/nsSupplier/nsSupplier");
        // 权限标识
        menu.setPerms("business:nsSupplier:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("周建宇");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("供应商表", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "供应商表-查询", 1, "business:nsSupplier:list");
            addOperationMenu(menu.getMenuId(), "供应商表-新增", 2, "business:nsSupplier:add");
            addOperationMenu(menu.getMenuId(), "供应商表-修改", 3, "business:nsSupplier:edit");
            addOperationMenu(menu.getMenuId(), "供应商表-删除", 4, "business:nsSupplier:remove");
            addOperationMenu(menu.getMenuId(), "供应商表-导出", 5, "business:nsSupplier:export");
            addOperationMenu(menu.getMenuId(), "供应商表-导入", 6, "business:nsSupplier:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsSupplier ok");
        }
    }

    /**
     * 创建操作按钮权限（供应商表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-24 16:18:12
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（供应商表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-24 16:18:12
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("供应商表", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsSupplier menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
