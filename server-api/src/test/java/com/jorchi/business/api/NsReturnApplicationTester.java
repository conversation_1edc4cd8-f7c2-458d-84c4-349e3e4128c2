package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsReturnApplication;
import com.jorchi.business.service.NsReturnApplicationService;
import com.jorchi.business.form.NsReturnApplicationForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartHttpServletRequest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ns_return_application-模块测试<br/>
 * 对应表名：ns_return_application，表备注：退货申请表
 * @author: 周建宇 at jorchi
 * @date: 2024-12-30 10:20:29
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsReturnApplicationTester extends BaseApi {

    @Resource
    NsReturnApplicationService nsReturnApplicationService;

    @Resource
    NsReturnApplicationApi nsReturnApplicationApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（退货申请表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-30 10:20:29
     */
    @Test
    public void findByIdNsReturnApplicationTest() {
        Long returnId;
        returnId = 1L;

        NsReturnApplication bean = nsReturnApplicationService.findById(returnId);
        if (bean == null)
            throw ClientException.of("NsReturnApplication not found error!");

        log.info("findById NsReturnApplicationTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（退货申请表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-30 10:20:29
     */
    @Test
    public void listPageNsReturnApplicationTest() {
        NsReturnApplicationForm nsReturnApplicationVo = new NsReturnApplicationForm();
        nsReturnApplicationVo.setPageNum(1);
        nsReturnApplicationVo.setPageSize(10);
        log.info("listPage NsReturnApplicationTest result:");
        log.info(getDataTable(nsReturnApplicationService.listPage(nsReturnApplicationVo)).toString());
    }

    /**
     * 删除测试（退货申请表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-30 10:20:29
     */
    @Test
    public void deleteNsReturnApplicationTest() {
        Long id = 0L;
        log.info("delete NsReturnApplicationTest result:");
        log.info(AjaxResult.success(nsReturnApplicationService.deleteNsReturnApplication(id)).toString());
    }

    /**
     * 新增或保存测试（退货申请表）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2024-12-30 10:20:29
     */
    @Test
    public void saveNsReturnApplicationTest () {
        NsReturnApplication nsReturnApplication = new NsReturnApplication();
        // 退货id
        // nsReturnApplication.setReturnId(0L);
        // 申请人
        nsReturnApplication.setApplicant("申请人");
        // 退货日期
        nsReturnApplication.setReturnDate(new Date());
        // 申请人手机号
        nsReturnApplication.setApplicantMobile("申请人手机号");
        // 申请人ID
        nsReturnApplication.setApplicantId(0L);
        // 退货事由
        nsReturnApplication.setReturnReason("退货事由");
        // 退货状态:0-未发起，1-待审批，2-已驳回，3-已完结
        nsReturnApplication.setReturnStatus(0);
        // 审批人
        nsReturnApplication.setApprovedBy(0L);
        // 审批时间
        nsReturnApplication.setApprovedTime(new Date());
        // 删除标志（0代表存在，2代表删除）
        nsReturnApplication.setDeleted(0);
        // 创建人
        nsReturnApplication.setCreateBy(0L);
        // 创建时间
        nsReturnApplication.setCreateTime(new Date());
        // 更新人
        nsReturnApplication.setUpdateBy(0L);
        // 更新时间
        nsReturnApplication.setUpdateTime(new Date());
        // 关联采购id
        nsReturnApplication.setPurchaseId(0L);
        // 采购编号
        nsReturnApplication.setPurchaseCode("采购编号");

        // 检查待保存数据的合法性
        nsReturnApplicationApi.validatePo(nsReturnApplication);

        // 名称唯一性检查?
        // nsReturnApplicationService.checkNameExisted(nsReturnApplication);

        NsReturnApplication result = nsReturnApplicationService.saveNsReturnApplication(nsReturnApplication);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（退货申请表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-30 10:20:29
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("退货申请表");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsReturnApplication");
        // 组件路径
        menu.setComponent("business/nsReturnApplication/nsReturnApplication");
        // 权限标识
        menu.setPerms("business:nsReturnApplication:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("周建宇");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("退货申请表", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "退货申请表-查询", 1, "business:nsReturnApplication:list");
            addOperationMenu(menu.getMenuId(), "退货申请表-新增", 2, "business:nsReturnApplication:add");
            addOperationMenu(menu.getMenuId(), "退货申请表-修改", 3, "business:nsReturnApplication:edit");
            addOperationMenu(menu.getMenuId(), "退货申请表-删除", 4, "business:nsReturnApplication:remove");
            addOperationMenu(menu.getMenuId(), "退货申请表-导出", 5, "business:nsReturnApplication:export");
            addOperationMenu(menu.getMenuId(), "退货申请表-导入", 6, "business:nsReturnApplication:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsReturnApplication ok");
        }
    }

    /**
     * 创建操作按钮权限（退货申请表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-30 10:20:29
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（退货申请表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-30 10:20:29
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("退货申请表", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsReturnApplication menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
