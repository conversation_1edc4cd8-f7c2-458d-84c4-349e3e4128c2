package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsAuditExecute;
import com.jorchi.business.service.NsAuditExecuteService;
import com.jorchi.business.form.NsAuditExecuteForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.util.Date;

/**
 * ns_audit_execute-模块测试<br/>
 * 对应表名：ns_audit_execute，表备注：稽核执行
 * @author: xubinbin at jorchi
 * @date: 2022-11-17 09:42:32
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsAuditExecuteTester extends BaseApi {

    @Resource
    NsAuditExecuteService nsAuditExecuteService;

    @Resource
    NsAuditExecuteApi nsAuditExecuteApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（稽核执行）<br>
     * @author: xubinbin at jorchi
     * @date: 2022-11-17 09:42:32
     */
    @Test
    public void findByIdNsAuditExecuteTest() {
        Long auditId;
        auditId = 1L;

        NsAuditExecute bean = nsAuditExecuteService.findById(auditId);
        if (bean == null)
            throw ClientException.of("NsAuditExecute not found error!");

        log.info("findById NsAuditExecuteTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（稽核执行）<br>
     * @author: xubinbin at jorchi
     * @date: 2022-11-17 09:42:32
     */
    @Test
    public void listPageNsAuditExecuteTest() {
        NsAuditExecuteForm nsAuditExecuteVo = new NsAuditExecuteForm();
        nsAuditExecuteVo.setPageNum(1);
        nsAuditExecuteVo.setPageSize(10);
        log.info("listPage NsAuditExecuteTest result:");
        log.info(getDataTable(nsAuditExecuteService.listPage(nsAuditExecuteVo)).toString());
    }

    /**
     * 删除测试（稽核执行）<br>
     * @author: xubinbin at jorchi
     * @date: 2022-11-17 09:42:32
     */
    @Test
    public void deleteNsAuditExecuteTest() {
/*        Long id = 0L;
        log.info("delete NsAuditExecuteTest result:");
        log.info(AjaxResult.success(nsAuditExecuteService.deleteNsAuditExecute(id)).toString());*/
    }

    /**
     * 新增或保存测试（稽核执行）<br>
     * ID 为空即为新增，否则为更新
     * @author: xubinbin at jorchi
     * @date: 2022-11-17 09:42:32
     */
    @Test
    public void saveNsAuditExecuteTest () {
        NsAuditExecute nsAuditExecute = new NsAuditExecute();
        // 稽核ID
        // nsAuditExecute.setAuditId(0L);
        // 稽核总项
        nsAuditExecute.setAuditTotal("稽核总项");
        // 稽核事项
        nsAuditExecute.setAuditName("稽核事项");
        // 备注信息
        nsAuditExecute.setAuditRemarks("备注信息");
        // 种植区
        nsAuditExecute.setPlantingArea("种植区");
        // 大棚编号
        nsAuditExecute.setHouseName("大棚编号");
        // 执行日期
        nsAuditExecute.setExecuteTime(new Date());
        // 技术人员
        nsAuditExecute.setTechnicalPersonnel("技术人员");
        // 下发层级
        nsAuditExecute.setSendingLevel("下发层级");
        // 删除标志（0代表存在2代表删除）
        nsAuditExecute.setDeleted(0);
        // 处理状态(0代表未完成1代表已完成)
        nsAuditExecute.setCurrentStatu(0);
        // 创建人
        nsAuditExecute.setCreateBy(0L);
        // 创建时间
        nsAuditExecute.setCreateTime(new Date());
        // 更新人
        nsAuditExecute.setUpdateBy(0L);
        // 更新时间
        nsAuditExecute.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsAuditExecuteApi.validatePo(nsAuditExecute);

        // 名称唯一性检查?
        // nsAuditExecuteService.checkNameExisted(nsAuditExecute);

/*        NsAuditExecute result = nsAuditExecuteService.saveNsAuditExecute(nsAuditExecute);
        log.info("save result:");
        log.info(result.toString());*/
    }


    /**
     * 生成菜单（稽核执行）<br>
     * @author: xubinbin at jorchi
     * @date: 2022-11-17 09:42:32
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("稽核执行");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsAuditExecute");
        // 组件路径
        menu.setComponent("business/nsAuditExecute/nsAuditExecute");
        // 权限标识
        menu.setPerms("business:nsAuditExecute:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("xubinbin");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("稽核执行", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "稽核执行-查询", 1, "business:nsAuditExecute:list");
            addOperationMenu(menu.getMenuId(), "稽核执行-新增", 2, "business:nsAuditExecute:add");
            addOperationMenu(menu.getMenuId(), "稽核执行-修改", 3, "business:nsAuditExecute:edit");
            addOperationMenu(menu.getMenuId(), "稽核执行-删除", 4, "business:nsAuditExecute:remove");
/*            addOperationMenu(menu.getMenuId(), "稽核执行-导出", 5, "business:nsAuditExecute:export");
            addOperationMenu(menu.getMenuId(), "稽核执行-导入", 6, "business:nsAuditExecute:import");*/
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsAuditExecute ok");
        }
    }

    /**
     * 创建操作按钮权限（稽核执行）<br>
     * @author: xubinbin at jorchi
     * @date: 2022-11-17 09:42:32
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（稽核执行）<br>
     * @author: xubinbin at jorchi
     * @date: 2022-11-17 09:42:32
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("稽核执行", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsAuditExecute menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
