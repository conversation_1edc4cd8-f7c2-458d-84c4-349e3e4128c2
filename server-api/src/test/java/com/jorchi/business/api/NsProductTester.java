package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.form.NsProductForm;
import com.jorchi.business.po.NsProduct;
import com.jorchi.business.service.NsProductService;
import com.jorchi.business.vo.NsProductVo;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ns_product-模块测试<br/>
 * 对应表名：ns_product，表备注：半成品转成品表
 * @author: 周建宇 at jorchi
 * @date: 2024-12-10 09:22:30
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsProductTester extends BaseApi {

    @Resource
    NsProductService nsProductService;

    @Resource
    NsProductApi nsProductApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（半成品转成品表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-10 09:22:30
     */
    @Test
    public void findByIdNsProductTest() {
        Long productId;
        productId = 1L;

        NsProductVo bean = nsProductService.findById(productId);
        if (bean == null)
            throw ClientException.of("NsProduct not found error!");

        log.info("findById NsProductTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（半成品转成品表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-10 09:22:30
     */
    @Test
    public void listPageNsProductTest() {
        NsProductForm nsProductVo = new NsProductForm();
        nsProductVo.setPageNum(1);
        nsProductVo.setPageSize(10);
        log.info("listPage NsProductTest result:");
        log.info(getDataTable(nsProductService.listPage(nsProductVo)).toString());
    }

    /**
     * 删除测试（半成品转成品表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-10 09:22:30
     */
    @Test
    public void deleteNsProductTest() {
        Long id = 0L;
        log.info("delete NsProductTest result:");
        log.info(AjaxResult.success(nsProductService.deleteNsProduct(id)).toString());
    }

    /**
     * 新增或保存测试（半成品转成品表）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2024-12-10 09:22:30
     */
    @Test
    public void saveNsProductTest () {
        NsProduct nsProduct = new NsProduct();
        // 入库ID
        // nsProduct.setProductId(0L);
        // 入库ID
        // nsProduct.setWarehouseId(0L);
        // 来源大棚
        nsProduct.setHouseId(0L);
        // 生产计划
        nsProduct.setProductionPlanId(0L);
        // 实际入库量
        nsProduct.setActualQuantity(new BigDecimal(0));
        // 半成品ID
        nsProduct.setSemiProductId(0L);
        // 删除标志
        nsProduct.setDeleted(0);
        // 创建人
        nsProduct.setCreateBy(0L);
        // 创建时间
        nsProduct.setCreateTime(new Date());
        // 更新人
        nsProduct.setUpdateBy(0L);
        // 更新时间
        nsProduct.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsProductApi.validatePo(nsProduct);

        // 名称唯一性检查?
        // nsProductService.checkNameExisted(nsProduct);

        NsProduct result = nsProductService.saveNsProduct(nsProduct);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（半成品转成品表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-10 09:22:30
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("半成品转成品表");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsProduct");
        // 组件路径
        menu.setComponent("business/nsProduct/nsProduct");
        // 权限标识
        menu.setPerms("business:nsProduct:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("周建宇");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("半成品转成品表", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "半成品转成品表-查询", 1, "business:nsProduct:list");
            addOperationMenu(menu.getMenuId(), "半成品转成品表-新增", 2, "business:nsProduct:add");
            addOperationMenu(menu.getMenuId(), "半成品转成品表-修改", 3, "business:nsProduct:edit");
            addOperationMenu(menu.getMenuId(), "半成品转成品表-删除", 4, "business:nsProduct:remove");
            addOperationMenu(menu.getMenuId(), "半成品转成品表-导出", 5, "business:nsProduct:export");
            addOperationMenu(menu.getMenuId(), "半成品转成品表-导入", 6, "business:nsProduct:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsProduct ok");
        }
    }

    /**
     * 创建操作按钮权限（半成品转成品表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-10 09:22:30
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（半成品转成品表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-10 09:22:30
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("半成品转成品表", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsProduct menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
