package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsBreedsSelect;
import com.jorchi.business.service.NsBreedsSelectService;
import com.jorchi.business.form.NsBreedsSelectForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.util.Date;

/**
 * ns_breeds_select-模块测试<br/>
 * 对应表名：ns_breeds_select，表备注：种植项配置（选择种子）
 * @author: xubinbin at jorchi
 * @date: 2023-01-16 15:46:39
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsBreedsSelectTester extends BaseApi {

    @Resource
    NsBreedsSelectService nsBreedsSelectService;

    @Resource
    NsBreedsSelectApi nsBreedsSelectApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（种植项配置（选择种子））<br>
     * @author: xubinbin at jorchi
     * @date: 2023-01-16 15:46:39
     */
    @Test
    public void findByIdNsBreedsSelectTest() {
        Long breedsId;
        breedsId = 1L;

        NsBreedsSelect bean = nsBreedsSelectService.findById(breedsId);
        if (bean == null)
            throw ClientException.of("NsBreedsSelect not found error!");

        log.info("findById NsBreedsSelectTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（种植项配置（选择种子））<br>
     * @author: xubinbin at jorchi
     * @date: 2023-01-16 15:46:39
     */
    @Test
    public void listPageNsBreedsSelectTest() {
        NsBreedsSelectForm nsBreedsSelectVo = new NsBreedsSelectForm();
        nsBreedsSelectVo.setPageNum(1);
        nsBreedsSelectVo.setPageSize(10);
        log.info("listPage NsBreedsSelectTest result:");
        log.info(getDataTable(nsBreedsSelectService.listPage(nsBreedsSelectVo)).toString());
    }

    /**
     * 删除测试（种植项配置（选择种子））<br>
     * @author: xubinbin at jorchi
     * @date: 2023-01-16 15:46:39
     */
    @Test
    public void deleteNsBreedsSelectTest() {
        Long id = 0L;
        log.info("delete NsBreedsSelectTest result:");
        log.info(AjaxResult.success(nsBreedsSelectService.deleteNsBreedsSelect(id)).toString());
    }

    /**
     * 新增或保存测试（种植项配置（选择种子））<br>
     * ID 为空即为新增，否则为更新
     * @author: xubinbin at jorchi
     * @date: 2023-01-16 15:46:39
     */
    @Test
    public void saveNsBreedsSelectTest () {
        NsBreedsSelect nsBreedsSelect = new NsBreedsSelect();
        // 选择种子表id
        // nsBreedsSelect.setBreedsId(0L);
        // 种植项id
        // nsBreedsSelect.setPlantingClauseId(0L);
        // 作物名称
        nsBreedsSelect.setCropName("作物名称");
        // 品种
        nsBreedsSelect.setBreeds("品种");
        // 使用量
        nsBreedsSelect.setDosage("使用量");
        // 删除标志（0代表存在2代表删除）
        nsBreedsSelect.setDeleted(0);
        // 创建人
        nsBreedsSelect.setCreateBy(0L);
        // 创建时间
        nsBreedsSelect.setCreateTime(new Date());
        // 更新人
        nsBreedsSelect.setUpdateBy(0L);
        // 更新时间
        nsBreedsSelect.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsBreedsSelectApi.validatePo(nsBreedsSelect);

        // 名称唯一性检查?
        // nsBreedsSelectService.checkNameExisted(nsBreedsSelect);

        NsBreedsSelect result = nsBreedsSelectService.saveNsBreedsSelect(nsBreedsSelect);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（种植项配置（选择种子））<br>
     * @author: xubinbin at jorchi
     * @date: 2023-01-16 15:46:39
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("种植项配置（选择种子）");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsBreedsSelect");
        // 组件路径
        menu.setComponent("business/nsBreedsSelect/nsBreedsSelect");
        // 权限标识
        menu.setPerms("business:nsBreedsSelect:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("xubinbin");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("种植项配置（选择种子）", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "种植项配置（选择种子）-查询", 1, "business:nsBreedsSelect:list");
            addOperationMenu(menu.getMenuId(), "种植项配置（选择种子）-新增", 2, "business:nsBreedsSelect:add");
            addOperationMenu(menu.getMenuId(), "种植项配置（选择种子）-修改", 3, "business:nsBreedsSelect:edit");
            addOperationMenu(menu.getMenuId(), "种植项配置（选择种子）-删除", 4, "business:nsBreedsSelect:remove");
            addOperationMenu(menu.getMenuId(), "种植项配置（选择种子）-导出", 5, "business:nsBreedsSelect:export");
            addOperationMenu(menu.getMenuId(), "种植项配置（选择种子）-导入", 6, "business:nsBreedsSelect:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsBreedsSelect ok");
        }
    }

    /**
     * 创建操作按钮权限（种植项配置（选择种子））<br>
     * @author: xubinbin at jorchi
     * @date: 2023-01-16 15:46:39
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（种植项配置（选择种子））<br>
     * @author: xubinbin at jorchi
     * @date: 2023-01-16 15:46:39
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("种植项配置（选择种子）", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsBreedsSelect menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
