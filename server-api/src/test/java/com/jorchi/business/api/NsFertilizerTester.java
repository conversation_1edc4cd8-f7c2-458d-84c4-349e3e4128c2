package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.form.NsFertilizerForm;
import com.jorchi.business.po.NsFertilizer;
import com.jorchi.business.service.NsFertilizerService;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.util.Date;

/**
 * ns_fertilizer-模块测试<br/>
 * 对应表名：ns_fertilizer，表备注：肥料管理
 * @author: GeSenQi at jorchi
 * @date: 2022-11-10 11:33:05
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsFertilizerTester extends BaseApi {

    @Resource
    NsFertilizerService nsFertilizerService;

    @Resource
    NsFertilizerApi nsFertilizerApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（肥料管理）<br>
     * @author: GeSenQi at jorchi
     * @date: 2022-11-10 11:33:05
     */
    @Test
    public void findByIdNsFertilizerTest() {
        Long fertilizerId;
        fertilizerId = 1L;

        NsFertilizer bean = nsFertilizerService.findById(fertilizerId);
        if (bean == null)
            throw ClientException.of("NsFertilizer not found error!");

        log.info("findById NsFertilizerTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（肥料管理）<br>
     * @author: GeSenQi at jorchi
     * @date: 2022-11-10 11:33:05
     */
    @Test
    public void listPageNsFertilizerTest() {
        NsFertilizerForm nsFertilizerVo = new NsFertilizerForm();
        nsFertilizerVo.setPageNum(1);
        nsFertilizerVo.setPageSize(10);
        log.info("listPage NsFertilizerTest result:");
        log.info(getDataTable(nsFertilizerService.listPage(nsFertilizerVo)).toString());
    }

//    /**
//     * 删除测试（肥料管理）<br>
//     * @author: GeSenQi at jorchi
//     * @date: 2022-11-10 11:33:05
//     */
//    @Test
//    public void deleteNsFertilizerTest() {
//        Long id = 0L;
//        log.info("delete NsFertilizerTest result:");
//        log.info(AjaxResult.success(nsFertilizerService.deleteNsFertilizer(id)).toString());
//    }

//    /**
//     * 新增或保存测试（肥料管理）<br>
//     * ID 为空即为新增，否则为更新
//     * @author: GeSenQi at jorchi
//     * @date: 2022-11-10 11:33:05
//     */
//    @Test
//    public void saveNsFertilizerTest () {
//        NsFertilizer nsFertilizer = new NsFertilizer();
//        // 肥料主键
//        // nsFertilizer.setFertilizerId(0L);
//        // 供应商
//        nsFertilizer.setSupplierName("供应商");
//        // 肥料信息
//        nsFertilizer.setDetails("肥料信息");
//        // 肥料单位
//        nsFertilizer.setFertilizerUnit("肥料单位");
//        // 删除标志（0代表存在2代表删除）
//        nsFertilizer.setDeleted(0);
//        // 创建人
//        nsFertilizer.setCreateBy(0L);
//        // 创建时间
//        nsFertilizer.setCreateTime(new Date());
//        // 更新人
//        nsFertilizer.setUpdateBy(0L);
//        // 更新时间
//        nsFertilizer.setUpdateTime(new Date());
//
//        // 检查待保存数据的合法性
//        nsFertilizerApi.validatePo(nsFertilizer);
//
//        // 名称唯一性检查?
//        // nsFertilizerService.checkNameExisted(nsFertilizer);
//
//        NsFertilizer result = nsFertilizerService.saveNsFertilizer(nsFertilizer);
//        log.info("save result:");
//        log.info(result.toString());
//    }


    /**
     * 生成菜单（肥料管理）<br>
     * @author: GeSenQi at jorchi
     * @date: 2022-11-10 11:33:05
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("肥料管理");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsFertilizer");
        // 组件路径
        menu.setComponent("business/nsFertilizer/nsFertilizer");
        // 权限标识
        menu.setPerms("business:nsFertilizer:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("GeSenQi");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("肥料管理", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "肥料管理-查询", 1, "business:nsFertilizer:list");
            addOperationMenu(menu.getMenuId(), "肥料管理-新增", 2, "business:nsFertilizer:add");
            addOperationMenu(menu.getMenuId(), "肥料管理-修改", 3, "business:nsFertilizer:edit");
            addOperationMenu(menu.getMenuId(), "肥料管理-删除", 4, "business:nsFertilizer:remove");
            addOperationMenu(menu.getMenuId(), "肥料管理-导出", 5, "business:nsFertilizer:export");
            addOperationMenu(menu.getMenuId(), "肥料管理-导入", 6, "business:nsFertilizer:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsFertilizer ok");
        }
    }

    /**
     * 创建操作按钮权限（肥料管理）<br>
     * @author: GeSenQi at jorchi
     * @date: 2022-11-10 11:33:05
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（肥料管理）<br>
     * @author: GeSenQi at jorchi
     * @date: 2022-11-10 11:33:05
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("肥料管理", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsFertilizer menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
