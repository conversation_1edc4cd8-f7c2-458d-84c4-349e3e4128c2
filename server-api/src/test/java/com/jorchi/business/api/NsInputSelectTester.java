package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsInputSelect;
import com.jorchi.business.service.NsInputSelectService;
import com.jorchi.business.form.NsInputSelectForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ns_input_select-模块测试<br/>
 * 对应表名：ns_input_select，表备注：种植项关联投入品表
 * @author: 周建宇 at jorchi
 * @date: 2024-12-22 10:59:11
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsInputSelectTester extends BaseApi {

    @Resource
    NsInputSelectService nsInputSelectService;

    @Resource
    NsInputSelectApi nsInputSelectApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（种植项关联投入品表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-22 10:59:11
     */
    @Test
    public void findByIdNsInputsSelectTest() {
        Long selectId;
        selectId = 1L;

        NsInputSelect bean = nsInputSelectService.findById(selectId);
        if (bean == null)
            throw ClientException.of("NsInputsSelect not found error!");

        log.info("findById NsInputsSelectTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（种植项关联投入品表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-22 10:59:11
     */
    @Test
    public void listPageNsInputsSelectTest() {
        NsInputSelectForm nsInputsSelectVo = new NsInputSelectForm();
        nsInputsSelectVo.setPageNum(1);
        nsInputsSelectVo.setPageSize(10);
        log.info("listPage NsInputsSelectTest result:");
        log.info(getDataTable(nsInputSelectService.listPage(nsInputsSelectVo)).toString());
    }

    /**
     * 删除测试（种植项关联投入品表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-22 10:59:11
     */
    @Test
    public void deleteNsInputsSelectTest() {
        Long id = 0L;
        log.info("delete NsInputsSelectTest result:");
        log.info(AjaxResult.success(nsInputSelectService.deleteNsInputsSelect(id)).toString());
    }

    /**
     * 新增或保存测试（种植项关联投入品表）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2024-12-22 10:59:11
     */
    @Test
    public void saveNsInputsSelectTest () {
        NsInputSelect nsInputSelect = new NsInputSelect();
        // 主键id
        // nsInputsSelect.setSelectId(0L);
        // 种植项id
        // nsInputsSelect.setPlantingClauseId(0L);
        // 投入品类型:1=农膜，2=其他
        nsInputSelect.setType(0);
        // 投入品分类
        nsInputSelect.setInputsType("投入品分类");
        // 投入品名称
        nsInputSelect.setInputsName("投入品名称");
        // 每平米使用量
        nsInputSelect.setAvgDosage(new BigDecimal(0));
        // 删除标志（0代表存在2代表删除）
        nsInputSelect.setDeleted(0);
        // 创建人
        nsInputSelect.setCreateBy(0L);
        // 创建时间
        nsInputSelect.setCreateTime(new Date());
        // 更新人
        nsInputSelect.setUpdateBy(0L);
        // 更新时间
        nsInputSelect.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsInputSelectApi.validatePo(nsInputSelect);

        // 名称唯一性检查?
        // nsInputsSelectService.checkNameExisted(nsInputsSelect);

        NsInputSelect result = nsInputSelectService.saveNsInputsSelect(nsInputSelect);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（种植项关联投入品表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-22 10:59:11
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("种植项关联投入品表");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsInputsSelect");
        // 组件路径
        menu.setComponent("business/nsInputsSelect/nsInputsSelect");
        // 权限标识
        menu.setPerms("business:nsInputsSelect:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("周建宇");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("种植项关联投入品表", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "种植项关联投入品表-查询", 1, "business:nsInputsSelect:list");
            addOperationMenu(menu.getMenuId(), "种植项关联投入品表-新增", 2, "business:nsInputsSelect:add");
            addOperationMenu(menu.getMenuId(), "种植项关联投入品表-修改", 3, "business:nsInputsSelect:edit");
            addOperationMenu(menu.getMenuId(), "种植项关联投入品表-删除", 4, "business:nsInputsSelect:remove");
            addOperationMenu(menu.getMenuId(), "种植项关联投入品表-导出", 5, "business:nsInputsSelect:export");
            addOperationMenu(menu.getMenuId(), "种植项关联投入品表-导入", 6, "business:nsInputsSelect:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsInputsSelect ok");
        }
    }

    /**
     * 创建操作按钮权限（种植项关联投入品表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-22 10:59:11
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（种植项关联投入品表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-22 10:59:11
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("种植项关联投入品表", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsInputsSelect menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
