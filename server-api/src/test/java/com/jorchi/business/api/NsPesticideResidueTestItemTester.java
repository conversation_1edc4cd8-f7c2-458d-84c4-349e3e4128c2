package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsPesticideResidueTestItem;
import com.jorchi.business.service.NsPesticideResidueTestItemService;
import com.jorchi.business.form.NsPesticideResidueTestItemForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartHttpServletRequest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ns_pesticide_residue_test_item-模块测试<br/>
 * 对应表名：ns_pesticide_residue_test_item，表备注：农残检测子项表
 * @author: 周建宇 at jorchi
 * @date: 2025-01-21 16:33:16
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsPesticideResidueTestItemTester extends BaseApi {

    @Resource
    NsPesticideResidueTestItemService nsPesticideResidueTestItemService;

    @Resource
    NsPesticideResidueTestItemApi nsPesticideResidueTestItemApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（农残检测子项表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-21 16:33:16
     */
    @Test
    public void findByIdNsPesticideResidueTestItemTest() {
        Long itemId;
        itemId = 1L;

        NsPesticideResidueTestItem bean = nsPesticideResidueTestItemService.findById(itemId);
        if (bean == null)
            throw ClientException.of("NsPesticideResidueTestItem not found error!");

        log.info("findById NsPesticideResidueTestItemTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（农残检测子项表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-21 16:33:16
     */
    @Test
    public void listPageNsPesticideResidueTestItemTest() {
        NsPesticideResidueTestItemForm nsPesticideResidueTestItemVo = new NsPesticideResidueTestItemForm();
        nsPesticideResidueTestItemVo.setPageNum(1);
        nsPesticideResidueTestItemVo.setPageSize(10);
        log.info("listPage NsPesticideResidueTestItemTest result:");
        log.info(getDataTable(nsPesticideResidueTestItemService.listPage(nsPesticideResidueTestItemVo)).toString());
    }

    /**
     * 删除测试（农残检测子项表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-21 16:33:16
     */
    @Test
    public void deleteNsPesticideResidueTestItemTest() {
        Long id = 0L;
        log.info("delete NsPesticideResidueTestItemTest result:");
        log.info(AjaxResult.success(nsPesticideResidueTestItemService.deleteNsPesticideResidueTestItem(id)).toString());
    }

    /**
     * 新增或保存测试（农残检测子项表）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2025-01-21 16:33:16
     */
    @Test
    public void saveNsPesticideResidueTestItemTest () {
        NsPesticideResidueTestItem nsPesticideResidueTestItem = new NsPesticideResidueTestItem();
        // 农残检测子项id
        // nsPesticideResidueTestItem.setItemId(0L);
        // 检测id，关联农残检测表的检测id
        // nsPesticideResidueTestItem.setTestId(0L);
        // 农产品ID
        nsPesticideResidueTestItem.setProductStockId(0L);
        // 品种
        nsPesticideResidueTestItem.setBreeds("品种");
        // 农产品名称
        nsPesticideResidueTestItem.setCropName("农产品名称");
        // 农残抑制率
        nsPesticideResidueTestItem.setResidue(new BigDecimal(0));
        // 农残浓度
        nsPesticideResidueTestItem.setConcentration(new BigDecimal(0));
        // 检测结果,1-及格，2-不及格
        nsPesticideResidueTestItem.setResult(0);
        // 删除标志（0代表存在，2代表删除）
        nsPesticideResidueTestItem.setDeleted(0);
        // 创建人
        nsPesticideResidueTestItem.setCreateBy(0L);
        // 创建时间
        nsPesticideResidueTestItem.setCreateTime(new Date());
        // 更新人
        nsPesticideResidueTestItem.setUpdateBy(0L);
        // 更新时间
        nsPesticideResidueTestItem.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsPesticideResidueTestItemApi.validatePo(nsPesticideResidueTestItem);

        // 名称唯一性检查?
        // nsPesticideResidueTestItemService.checkNameExisted(nsPesticideResidueTestItem);

        NsPesticideResidueTestItem result = nsPesticideResidueTestItemService.saveNsPesticideResidueTestItem(nsPesticideResidueTestItem);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（农残检测子项表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-21 16:33:16
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("农残检测子项表");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsPesticideResidueTestItem");
        // 组件路径
        menu.setComponent("business/nsPesticideResidueTestItem/nsPesticideResidueTestItem");
        // 权限标识
        menu.setPerms("business:nsPesticideResidueTestItem:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("周建宇");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("农残检测子项表", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "农残检测子项表-查询", 1, "business:nsPesticideResidueTestItem:list");
            addOperationMenu(menu.getMenuId(), "农残检测子项表-新增", 2, "business:nsPesticideResidueTestItem:add");
            addOperationMenu(menu.getMenuId(), "农残检测子项表-修改", 3, "business:nsPesticideResidueTestItem:edit");
            addOperationMenu(menu.getMenuId(), "农残检测子项表-删除", 4, "business:nsPesticideResidueTestItem:remove");
            addOperationMenu(menu.getMenuId(), "农残检测子项表-导出", 5, "business:nsPesticideResidueTestItem:export");
            addOperationMenu(menu.getMenuId(), "农残检测子项表-导入", 6, "business:nsPesticideResidueTestItem:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsPesticideResidueTestItem ok");
        }
    }

    /**
     * 创建操作按钮权限（农残检测子项表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-21 16:33:16
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（农残检测子项表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-21 16:33:16
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("农残检测子项表", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsPesticideResidueTestItem menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
