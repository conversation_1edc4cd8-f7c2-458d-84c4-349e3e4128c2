package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.dto.AddSalesOrderDto;
import com.jorchi.business.po.NsSalesOrder;
import com.jorchi.business.service.NsSalesOrderService;
import com.jorchi.business.form.NsSalesOrderForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartHttpServletRequest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ns_sales_order-模块测试<br/>
 * 对应表名：ns_sales_order，表备注：销售订单表
 * @author: 周建宇 at jorchi
 * @date: 2025-01-13 14:24:23
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsSalesOrderTester extends BaseApi {

    @Resource
    NsSalesOrderService nsSalesOrderService;

    @Resource
    NsSalesOrderApi nsSalesOrderApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（销售订单表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-13 14:24:23
     */
    @Test
    public void findByIdNsSalesOrderTest() {
        Long orderId;
        orderId = 1L;

        NsSalesOrder bean = nsSalesOrderService.findById(orderId);
        if (bean == null)
            throw ClientException.of("NsSalesOrder not found error!");

        log.info("findById NsSalesOrderTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（销售订单表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-13 14:24:23
     */
    @Test
    public void listPageNsSalesOrderTest() {
        NsSalesOrderForm nsSalesOrderVo = new NsSalesOrderForm();
        nsSalesOrderVo.setPageNum(1);
        nsSalesOrderVo.setPageSize(10);
        log.info("listPage NsSalesOrderTest result:");
        log.info(getDataTable(nsSalesOrderService.listPage(nsSalesOrderVo)).toString());
    }

    /**
     * 删除测试（销售订单表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-13 14:24:23
     */
    @Test
    public void deleteNsSalesOrderTest() {
        Long id = 0L;
        log.info("delete NsSalesOrderTest result:");
        log.info(AjaxResult.success(nsSalesOrderService.deleteNsSalesOrder(id)).toString());
    }

    /**
     * 新增或保存测试（销售订单表）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2025-01-13 14:24:23
     */
    @Test
    public void saveNsSalesOrderTest () {
        AddSalesOrderDto nsSalesOrder = new AddSalesOrderDto();
        // 订单id
        // nsSalesOrder.setOrderId(0L);
        // 销售单号
        nsSalesOrder.setSalesOrderCode("销售单号");
        // 申请人
        nsSalesOrder.setApplicant("申请人");
        // 申请日期
        nsSalesOrder.setApplicationDate(new Date());
        // 申请人手机号
        nsSalesOrder.setApplicantMobile("申请人手机号");
        // 申请人ID
        nsSalesOrder.setApplicantId(0L);
        // 客户ID
        nsSalesOrder.setCustomerId(0L);
        // 客户名称
        nsSalesOrder.setCustomerName("客户名称");
        // 申请事由
        nsSalesOrder.setApplicationReason("申请事由");
        // 申请状态:0-未发起，1-待审批，2-已驳回，3-已完结
        nsSalesOrder.setApplicationStatus(0);
        // 审批人
        nsSalesOrder.setApprovedBy(0L);
        // 审批时间
        nsSalesOrder.setApprovedTime(new Date());
        // 删除标志（0代表存在，2代表删除）
        nsSalesOrder.setDeleted(0);
        // 创建人
        nsSalesOrder.setCreateBy(0L);
        // 创建时间
        nsSalesOrder.setCreateTime(new Date());
        // 更新人
        nsSalesOrder.setUpdateBy(0L);
        // 更新时间
        nsSalesOrder.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsSalesOrderApi.validatePo(nsSalesOrder);

        // 名称唯一性检查?
        // nsSalesOrderService.checkNameExisted(nsSalesOrder);

        NsSalesOrder result = nsSalesOrderService.saveNsSalesOrder(nsSalesOrder);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（销售订单表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-13 14:24:23
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("销售订单表");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsSalesOrder");
        // 组件路径
        menu.setComponent("business/nsSalesOrder/nsSalesOrder");
        // 权限标识
        menu.setPerms("business:nsSalesOrder:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("周建宇");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("销售订单表", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "销售订单表-查询", 1, "business:nsSalesOrder:list");
            addOperationMenu(menu.getMenuId(), "销售订单表-新增", 2, "business:nsSalesOrder:add");
            addOperationMenu(menu.getMenuId(), "销售订单表-修改", 3, "business:nsSalesOrder:edit");
            addOperationMenu(menu.getMenuId(), "销售订单表-删除", 4, "business:nsSalesOrder:remove");
            addOperationMenu(menu.getMenuId(), "销售订单表-导出", 5, "business:nsSalesOrder:export");
            addOperationMenu(menu.getMenuId(), "销售订单表-导入", 6, "business:nsSalesOrder:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsSalesOrder ok");
        }
    }

    /**
     * 创建操作按钮权限（销售订单表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-13 14:24:23
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（销售订单表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-13 14:24:23
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("销售订单表", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsSalesOrder menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
