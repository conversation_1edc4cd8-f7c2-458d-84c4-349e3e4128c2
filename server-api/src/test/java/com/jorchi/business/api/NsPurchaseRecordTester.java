package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsPurchaseRecord;
import com.jorchi.business.service.NsPurchaseRecordService;
import com.jorchi.business.form.NsPurchaseRecordForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartHttpServletRequest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ns_purchase_record-模块测试<br/>
 * 对应表名：ns_purchase_record，表备注：采购记录表
 * @author: 周建宇 at jorchi
 * @date: 2024-12-29 12:37:04
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsPurchaseRecordTester extends BaseApi {

    @Resource
    NsPurchaseRecordService nsPurchaseRecordService;

    @Resource
    NsPurchaseRecordApi nsPurchaseRecordApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（采购记录表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-29 12:37:04
     */
    @Test
    public void findByIdNsPurchaseRecordTest() {
        Long purchaseRecordId;
        purchaseRecordId = 1L;

        NsPurchaseRecord bean = nsPurchaseRecordService.findById(purchaseRecordId);
        if (bean == null)
            throw ClientException.of("NsPurchaseRecord not found error!");

        log.info("findById NsPurchaseRecordTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（采购记录表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-29 12:37:04
     */
    @Test
    public void listPageNsPurchaseRecordTest() {
        NsPurchaseRecordForm nsPurchaseRecordVo = new NsPurchaseRecordForm();
        nsPurchaseRecordVo.setPageNum(1);
        nsPurchaseRecordVo.setPageSize(10);
        log.info("listPage NsPurchaseRecordTest result:");
        log.info(getDataTable(nsPurchaseRecordService.listPage(nsPurchaseRecordVo)).toString());
    }

    /**
     * 删除测试（采购记录表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-29 12:37:04
     */
    @Test
    public void deleteNsPurchaseRecordTest() {
        Long id = 0L;
        log.info("delete NsPurchaseRecordTest result:");
        log.info(AjaxResult.success(nsPurchaseRecordService.deleteNsPurchaseRecord(id)).toString());
    }

    /**
     * 新增或保存测试（采购记录表）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2024-12-29 12:37:04
     */
    @Test
    public void saveNsPurchaseRecordTest () {
        NsPurchaseRecord nsPurchaseRecord = new NsPurchaseRecord();
        // 采购记录ID
        // nsPurchaseRecord.setPurchaseRecordId(0L);
        // 采购ID
        // nsPurchaseRecord.setPurchaseId(0L);
        // 采购单号
        nsPurchaseRecord.setPurchaseCode("采购单号");
        // 申请人
        nsPurchaseRecord.setApplicant("申请人");
        // 申请日期
        nsPurchaseRecord.setApplicationDate(new Date());
        // 申请人手机号
        nsPurchaseRecord.setApplicantMobile("申请人手机号");
        // 申请人ID
        nsPurchaseRecord.setApplicantId(0L);
        // 申请事由
        nsPurchaseRecord.setApplicationReason("申请事由");
        // 支付状态:0-未支付，1-已支付
        nsPurchaseRecord.setPaymentStatus(0);
        // 支付日期
        nsPurchaseRecord.setPaymentDate(new Date());
        // 支付金额
        nsPurchaseRecord.setPaymentAmount(new BigDecimal(0));
        // 删除标志（0代表存在，2代表删除）
        nsPurchaseRecord.setDeleted(0);
        // 创建人
        nsPurchaseRecord.setCreateBy(0L);
        // 创建时间
        nsPurchaseRecord.setCreateTime(new Date());
        // 更新人
        nsPurchaseRecord.setUpdateBy(0L);
        // 更新时间
        nsPurchaseRecord.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsPurchaseRecordApi.validatePo(nsPurchaseRecord);

        // 名称唯一性检查?
        // nsPurchaseRecordService.checkNameExisted(nsPurchaseRecord);

        NsPurchaseRecord result = nsPurchaseRecordService.saveNsPurchaseRecord(nsPurchaseRecord);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（采购记录表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-29 12:37:04
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("采购记录表");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsPurchaseRecord");
        // 组件路径
        menu.setComponent("business/nsPurchaseRecord/nsPurchaseRecord");
        // 权限标识
        menu.setPerms("business:nsPurchaseRecord:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("周建宇");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("采购记录表", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "采购记录表-查询", 1, "business:nsPurchaseRecord:list");
            addOperationMenu(menu.getMenuId(), "采购记录表-新增", 2, "business:nsPurchaseRecord:add");
            addOperationMenu(menu.getMenuId(), "采购记录表-修改", 3, "business:nsPurchaseRecord:edit");
            addOperationMenu(menu.getMenuId(), "采购记录表-删除", 4, "business:nsPurchaseRecord:remove");
            addOperationMenu(menu.getMenuId(), "采购记录表-导出", 5, "business:nsPurchaseRecord:export");
            addOperationMenu(menu.getMenuId(), "采购记录表-导入", 6, "business:nsPurchaseRecord:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsPurchaseRecord ok");
        }
    }

    /**
     * 创建操作按钮权限（采购记录表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-29 12:37:04
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（采购记录表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-29 12:37:04
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("采购记录表", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsPurchaseRecord menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
