package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsProductStockOut;
import com.jorchi.business.service.NsProductStockOutService;
import com.jorchi.business.form.NsProductStockOutForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartHttpServletRequest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ns_product_stock_out-模块测试<br/>
 * 对应表名：ns_product_stock_out，表备注：农产品出库记录表
 * @author: 周建宇 at jorchi
 * @date: 2025-01-15 15:13:09
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsProductStockOutTester extends BaseApi {

    @Resource
    NsProductStockOutService nsProductStockOutService;

    @Resource
    NsProductStockOutApi nsProductStockOutApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（农产品出库记录表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-15 15:13:09
     */
    @Test
    public void findByIdNsProductStockOutTest() {
        Long stockOutId;
        stockOutId = 1L;

        NsProductStockOut bean = nsProductStockOutService.findById(stockOutId);
        if (bean == null)
            throw ClientException.of("NsProductStockOut not found error!");

        log.info("findById NsProductStockOutTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（农产品出库记录表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-15 15:13:09
     */
    @Test
    public void listPageNsProductStockOutTest() {
        NsProductStockOutForm nsProductStockOutVo = new NsProductStockOutForm();
        nsProductStockOutVo.setPageNum(1);
        nsProductStockOutVo.setPageSize(10);
        log.info("listPage NsProductStockOutTest result:");
        log.info(getDataTable(nsProductStockOutService.listPage(nsProductStockOutVo)).toString());
    }

    /**
     * 删除测试（农产品出库记录表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-15 15:13:09
     */
    @Test
    public void deleteNsProductStockOutTest() {
        Long id = 0L;
        log.info("delete NsProductStockOutTest result:");
        log.info(AjaxResult.success(nsProductStockOutService.deleteNsProductStockOut(id)).toString());
    }

    /**
     * 新增或保存测试（农产品出库记录表）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2025-01-15 15:13:09
     */
    @Test
    public void saveNsProductStockOutTest () {
        NsProductStockOut nsProductStockOut = new NsProductStockOut();
        // 农产品出库记录ID
        // nsProductStockOut.setStockOutId(0L);
        // 业务类型:1-销售出库
        // nsProductStockOut.setBusinessType(0);
        // 采购申请
        nsProductStockOut.setOrderId(0L);
        // 关联销售单号
        nsProductStockOut.setSalesOrderCode("关联销售单号");
        // 出库仓库
        nsProductStockOut.setWarehouseId(0L);
        // 出库仓库编码
        nsProductStockOut.setWarehouseCode("出库仓库编码");
        // 出库日期
        nsProductStockOut.setStockOutDate(new Date());
        // 经办人
        nsProductStockOut.setOperator("经办人");
        // 经办人ID
        nsProductStockOut.setOperatorId(0L);
        // 农产品ID
        nsProductStockOut.setProductStockId(0L);
        // 品种
        nsProductStockOut.setBreeds("品种");
        // 农产品名称
        nsProductStockOut.setCropName("农产品名称");
        // 单位
        nsProductStockOut.setInputUnit("单位");
        // 出库数量
        nsProductStockOut.setStockQuantity(new BigDecimal(0));
        // 删除标志（0代表存在2代表删除）
        nsProductStockOut.setDeleted(0);
        // 创建人
        nsProductStockOut.setCreateBy(0L);
        // 创建时间
        nsProductStockOut.setCreateTime(new Date());
        // 更新人
        nsProductStockOut.setUpdateBy(0L);
        // 更新时间
        nsProductStockOut.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsProductStockOutApi.validatePo(nsProductStockOut);

        // 名称唯一性检查?
        // nsProductStockOutService.checkNameExisted(nsProductStockOut);

         nsProductStockOutService.saveNsProductStockOut(nsProductStockOut);
        log.info("save result:");
//        log.info(result.toString());
    }


    /**
     * 生成菜单（农产品出库记录表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-15 15:13:09
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("农产品出库记录表");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsProductStockOut");
        // 组件路径
        menu.setComponent("business/nsProductStockOut/nsProductStockOut");
        // 权限标识
        menu.setPerms("business:nsProductStockOut:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("周建宇");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("农产品出库记录表", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "农产品出库记录表-查询", 1, "business:nsProductStockOut:list");
            addOperationMenu(menu.getMenuId(), "农产品出库记录表-新增", 2, "business:nsProductStockOut:add");
            addOperationMenu(menu.getMenuId(), "农产品出库记录表-修改", 3, "business:nsProductStockOut:edit");
            addOperationMenu(menu.getMenuId(), "农产品出库记录表-删除", 4, "business:nsProductStockOut:remove");
            addOperationMenu(menu.getMenuId(), "农产品出库记录表-导出", 5, "business:nsProductStockOut:export");
            addOperationMenu(menu.getMenuId(), "农产品出库记录表-导入", 6, "business:nsProductStockOut:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsProductStockOut ok");
        }
    }

    /**
     * 创建操作按钮权限（农产品出库记录表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-15 15:13:09
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（农产品出库记录表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-15 15:13:09
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("农产品出库记录表", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsProductStockOut menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
