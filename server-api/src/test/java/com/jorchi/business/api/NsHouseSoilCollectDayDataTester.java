package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsHouseSoilCollectDayData;
import com.jorchi.business.service.NsHouseSoilCollectDayDataService;
import com.jorchi.business.form.NsHouseSoilCollectDayDataForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.util.Date;

/**
 * ns_house_soil_collect_day_data-模块测试<br/>
 * 对应表名：ns_house_soil_collect_day_data，表备注：大棚土壤传感器采集数据（日表）
 * @author: xubinbin at jorchi
 * @date: 2023-02-16 11:36:05
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsHouseSoilCollectDayDataTester extends BaseApi {

    @Resource
    NsHouseSoilCollectDayDataService nsHouseSoilCollectDayDataService;

    @Resource
    NsHouseSoilCollectDayDataApi nsHouseSoilCollectDayDataApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（大棚土壤传感器采集数据（日表））<br>
     * @author: xubinbin at jorchi
     * @date: 2023-02-16 11:36:05
     */
    @Test
    public void findByIdNsHouseSoilCollectDayDataTest() {
        Long soilId;
        soilId = 1L;

        NsHouseSoilCollectDayData bean = nsHouseSoilCollectDayDataService.findById(soilId);
        if (bean == null)
            throw ClientException.of("NsHouseSoilCollectDayData not found error!");

        log.info("findById NsHouseSoilCollectDayDataTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（大棚土壤传感器采集数据（日表））<br>
     * @author: xubinbin at jorchi
     * @date: 2023-02-16 11:36:05
     */
    @Test
    public void listPageNsHouseSoilCollectDayDataTest() {
        NsHouseSoilCollectDayDataForm nsHouseSoilCollectDayDataVo = new NsHouseSoilCollectDayDataForm();
        nsHouseSoilCollectDayDataVo.setPageNum(1);
        nsHouseSoilCollectDayDataVo.setPageSize(10);
        log.info("listPage NsHouseSoilCollectDayDataTest result:");
        log.info(getDataTable(nsHouseSoilCollectDayDataService.listPage(nsHouseSoilCollectDayDataVo)).toString());
    }

    /**
     * 删除测试（大棚土壤传感器采集数据（日表））<br>
     * @author: xubinbin at jorchi
     * @date: 2023-02-16 11:36:05
     */
    @Test
    public void deleteNsHouseSoilCollectDayDataTest() {
        Long id = 0L;
        log.info("delete NsHouseSoilCollectDayDataTest result:");
        log.info(AjaxResult.success(nsHouseSoilCollectDayDataService.deleteNsHouseSoilCollectDayData(id)).toString());
    }

    /**
     * 新增或保存测试（大棚土壤传感器采集数据（日表））<br>
     * ID 为空即为新增，否则为更新
     * @author: xubinbin at jorchi
     * @date: 2023-02-16 11:36:05
     */
    @Test
    public void saveNsHouseSoilCollectDayDataTest () {
        NsHouseSoilCollectDayData nsHouseSoilCollectDayData = new NsHouseSoilCollectDayData();
        // 数据主键
        // nsHouseSoilCollectDayData.setSoilId(0L);
        // 大棚id
        // nsHouseSoilCollectDayData.setHouseId(0L);
        // 大棚编号
        nsHouseSoilCollectDayData.setHouseName("大棚编号");
        // 节点ID
        nsHouseSoilCollectDayData.setN("节点ID");
        // 采集时间
        nsHouseSoilCollectDayData.setD("采集时间");
/*        // 环境传感器和网关检测到的环境温度,只有当节点有该传感器时，这个属性才会被发送
        nsHouseSoilCollectDayData.setT(0);
        // 环境光照
        nsHouseSoilCollectDayData.setL(0);
        // 环境传感器和网关检测到的环境湿度,只有当节点有该传感器时，这个属性才会被发送
        nsHouseSoilCollectDayData.setQ(0);*/
        // 土壤EC,只有当节点有该传感器时，这个属性才会被发送
/*        nsHouseSoilCollectDayData.setE(0);
        // 土壤传感器测得的土壤TAO（温度）,只有当节点有该传感器时，这个属性才会被发送
        nsHouseSoilCollectDayData.setA(0);
        // 壤传感器测得的土壤MO（湿度）,只有当节点有该传感器时，这个属性才会被发送
        nsHouseSoilCollectDayData.setM(0);*/
        // 土壤PH测量范围：0～14，精度：±0.3
/*        nsHouseSoilCollectDayData.setPh(0);*/
        // 下次执行定时任务的时间
        nsHouseSoilCollectDayData.setNextTime(new Date());
        // 删除标志
        nsHouseSoilCollectDayData.setDeleted(0);
        // 创建人
        nsHouseSoilCollectDayData.setCreateBy(0L);
        // 创建时间
        nsHouseSoilCollectDayData.setCreateTime(new Date());
        // 更新人
        nsHouseSoilCollectDayData.setUpdateBy(0L);
        // 更新时间
        nsHouseSoilCollectDayData.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsHouseSoilCollectDayDataApi.validatePo(nsHouseSoilCollectDayData);

        // 名称唯一性检查?
        // nsHouseSoilCollectDayDataService.checkNameExisted(nsHouseSoilCollectDayData);

        NsHouseSoilCollectDayData result = nsHouseSoilCollectDayDataService.saveNsHouseSoilCollectDayData(nsHouseSoilCollectDayData);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（大棚土壤传感器采集数据（日表））<br>
     * @author: xubinbin at jorchi
     * @date: 2023-02-16 11:36:05
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("大棚土壤传感器采集数据（日表）");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsHouseSoilCollectDayData");
        // 组件路径
        menu.setComponent("business/nsHouseSoilCollectDayData/nsHouseSoilCollectDayData");
        // 权限标识
        menu.setPerms("business:nsHouseSoilCollectDayData:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("xubinbin");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("大棚土壤传感器采集数据（日表）", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "大棚土壤传感器采集数据（日表）-查询", 1, "business:nsHouseSoilCollectDayData:list");
            addOperationMenu(menu.getMenuId(), "大棚土壤传感器采集数据（日表）-新增", 2, "business:nsHouseSoilCollectDayData:add");
            addOperationMenu(menu.getMenuId(), "大棚土壤传感器采集数据（日表）-修改", 3, "business:nsHouseSoilCollectDayData:edit");
            addOperationMenu(menu.getMenuId(), "大棚土壤传感器采集数据（日表）-删除", 4, "business:nsHouseSoilCollectDayData:remove");
            addOperationMenu(menu.getMenuId(), "大棚土壤传感器采集数据（日表）-导出", 5, "business:nsHouseSoilCollectDayData:export");
            addOperationMenu(menu.getMenuId(), "大棚土壤传感器采集数据（日表）-导入", 6, "business:nsHouseSoilCollectDayData:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsHouseSoilCollectDayData ok");
        }
    }

    /**
     * 创建操作按钮权限（大棚土壤传感器采集数据（日表））<br>
     * @author: xubinbin at jorchi
     * @date: 2023-02-16 11:36:05
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（大棚土壤传感器采集数据（日表））<br>
     * @author: xubinbin at jorchi
     * @date: 2023-02-16 11:36:05
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("大棚土壤传感器采集数据（日表）", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsHouseSoilCollectDayData menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
