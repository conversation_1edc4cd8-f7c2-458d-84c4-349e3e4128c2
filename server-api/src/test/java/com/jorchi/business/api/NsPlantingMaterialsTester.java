package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsPlantingMaterials;
import com.jorchi.business.service.NsPlantingMaterialsService;
import com.jorchi.business.form.NsPlantingMaterialsForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.util.Date;

/**
 * ns_planting_materials-模块测试<br/>
 * 对应表名：ns_planting_materials，表备注：种植项物料表
 * @author: GeSenQi at jorchi
 * @date: 2022-11-10 23:46:53
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsPlantingMaterialsTester extends BaseApi {

    @Resource
    NsPlantingMaterialsService nsPlantingMaterialsService;

    @Resource
    NsPlantingMaterialsApi nsPlantingMaterialsApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（种植项物料表）<br>
     * @author: GeSenQi at jorchi
     * @date: 2022-11-10 23:46:53
     */
    @Test
    public void findByIdNsPlantingMaterialsTest() {
        Long plantingMaterialsId;
        plantingMaterialsId = 1L;

        NsPlantingMaterials bean = nsPlantingMaterialsService.findById(plantingMaterialsId);
        if (bean == null)
            throw ClientException.of("NsPlantingMaterials not found error!");

        log.info("findById NsPlantingMaterialsTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（种植项物料表）<br>
     * @author: GeSenQi at jorchi
     * @date: 2022-11-10 23:46:53
     */
    @Test
    public void listPageNsPlantingMaterialsTest() {
        NsPlantingMaterialsForm nsPlantingMaterialsVo = new NsPlantingMaterialsForm();
        nsPlantingMaterialsVo.setPageNum(1);
        nsPlantingMaterialsVo.setPageSize(10);
        log.info("listPage NsPlantingMaterialsTest result:");
        log.info(getDataTable(nsPlantingMaterialsService.listPage(nsPlantingMaterialsVo)).toString());
    }

//    /**
//     * 删除测试（种植项物料表）<br>
//     * @author: GeSenQi at jorchi
//     * @date: 2022-11-10 23:46:53
//     */
//    @Test
//    public void deleteNsPlantingMaterialsTest() {
//        Long id = 0L;
//        log.info("delete NsPlantingMaterialsTest result:");
//        log.info(AjaxResult.success(nsPlantingMaterialsService.deleteNsPlantingMaterials(id)).toString());
//    }
//
//    /**
//     * 新增或保存测试（种植项物料表）<br>
//     * ID 为空即为新增，否则为更新
//     * @author: GeSenQi at jorchi
//     * @date: 2022-11-10 23:46:53
//     */
//    @Test
//    public void saveNsPlantingMaterialsTest () {
//        NsPlantingMaterials nsPlantingMaterials = new NsPlantingMaterials();
//        // 种植项物料ID
//        // nsPlantingMaterials.setPlantingMaterialsId(0L);
//        // 种植项ID
//        // nsPlantingMaterials.setPlantingClauseId(0L);
//        // 品牌名称
//        nsPlantingMaterials.setBrandName("品牌名称");
//        // 投入品名称
//        nsPlantingMaterials.setActiveIngredient("投入品名称");
//        // 使用方法
//        nsPlantingMaterials.setUseMethod("使用方法");
//        // 备注
//        nsPlantingMaterials.setRemark("备注");
//        // 删除标志（0代表存在2代表删除）
//        nsPlantingMaterials.setDeleted(0);
//        // 创建人
//        nsPlantingMaterials.setCreateBy(0L);
//        // 创建时间
//        nsPlantingMaterials.setCreateTime(new Date());
//        // 更新人
//        nsPlantingMaterials.setUpdateBy(0L);
//        // 更新时间
//        nsPlantingMaterials.setUpdateTime(new Date());
//
//        // 检查待保存数据的合法性
//        nsPlantingMaterialsApi.validatePo(nsPlantingMaterials);
//
//        // 名称唯一性检查?
//        // nsPlantingMaterialsService.checkNameExisted(nsPlantingMaterials);
//
//        NsPlantingMaterials result = nsPlantingMaterialsService.saveNsPlantingMaterials(nsPlantingMaterials);
//        log.info("save result:");
//        log.info(result.toString());
//    }


    /**
     * 生成菜单（种植项物料表）<br>
     * @author: GeSenQi at jorchi
     * @date: 2022-11-10 23:46:53
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("种植项物料表");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsPlantingMaterials");
        // 组件路径
        menu.setComponent("business/nsPlantingMaterials/nsPlantingMaterials");
        // 权限标识
        menu.setPerms("business:nsPlantingMaterials:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("GeSenQi");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("种植项物料表", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "种植项物料表-查询", 1, "business:nsPlantingMaterials:list");
            addOperationMenu(menu.getMenuId(), "种植项物料表-新增", 2, "business:nsPlantingMaterials:add");
            addOperationMenu(menu.getMenuId(), "种植项物料表-修改", 3, "business:nsPlantingMaterials:edit");
            addOperationMenu(menu.getMenuId(), "种植项物料表-删除", 4, "business:nsPlantingMaterials:remove");
            addOperationMenu(menu.getMenuId(), "种植项物料表-导出", 5, "business:nsPlantingMaterials:export");
            addOperationMenu(menu.getMenuId(), "种植项物料表-导入", 6, "business:nsPlantingMaterials:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsPlantingMaterials ok");
        }
    }

    /**
     * 创建操作按钮权限（种植项物料表）<br>
     * @author: GeSenQi at jorchi
     * @date: 2022-11-10 23:46:53
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（种植项物料表）<br>
     * @author: GeSenQi at jorchi
     * @date: 2022-11-10 23:46:53
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("种植项物料表", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsPlantingMaterials menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
