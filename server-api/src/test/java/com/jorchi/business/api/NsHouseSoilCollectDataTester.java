package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsHouseSoilCollectData;
import com.jorchi.business.service.NsHouseSoilCollectDataService;
import com.jorchi.business.form.NsHouseSoilCollectDataForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.util.Date;

/**
 * ns_house_soil_collect_data-模块测试<br/>
 * 对应表名：ns_house_soil_collect_data，表备注：大棚土壤传感器采集数据
 * @author: xubinbin at jorchi
 * @date: 2023-02-16 11:35:29
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsHouseSoilCollectDataTester extends BaseApi {

    @Resource
    NsHouseSoilCollectDataService nsHouseSoilCollectDataService;

    @Resource
    NsHouseSoilCollectDataApi nsHouseSoilCollectDataApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（大棚土壤传感器采集数据）<br>
     * @author: xubinbin at jorchi
     * @date: 2023-02-16 11:35:29
     */
    @Test
    public void findByIdNsHouseSoilCollectDataTest() {
        Long soilId;
        soilId = 1L;

        NsHouseSoilCollectData bean = nsHouseSoilCollectDataService.findById(soilId);
        if (bean == null)
            throw ClientException.of("NsHouseSoilCollectData not found error!");

        log.info("findById NsHouseSoilCollectDataTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（大棚土壤传感器采集数据）<br>
     * @author: xubinbin at jorchi
     * @date: 2023-02-16 11:35:29
     */
    @Test
    public void listPageNsHouseSoilCollectDataTest() {
        NsHouseSoilCollectDataForm nsHouseSoilCollectDataVo = new NsHouseSoilCollectDataForm();
        nsHouseSoilCollectDataVo.setPageNum(1);
        nsHouseSoilCollectDataVo.setPageSize(10);
        log.info("listPage NsHouseSoilCollectDataTest result:");
        log.info(getDataTable(nsHouseSoilCollectDataService.listPage(nsHouseSoilCollectDataVo)).toString());
    }

    /**
     * 删除测试（大棚土壤传感器采集数据）<br>
     * @author: xubinbin at jorchi
     * @date: 2023-02-16 11:35:29
     */
    @Test
    public void deleteNsHouseSoilCollectDataTest() {
        Long id = 0L;
        log.info("delete NsHouseSoilCollectDataTest result:");
        log.info(AjaxResult.success(nsHouseSoilCollectDataService.deleteNsHouseSoilCollectData(id)).toString());
    }

    /**
     * 新增或保存测试（大棚土壤传感器采集数据）<br>
     * ID 为空即为新增，否则为更新
     * @author: xubinbin at jorchi
     * @date: 2023-02-16 11:35:29
     */
    @Test
    public void saveNsHouseSoilCollectDataTest () {
        NsHouseSoilCollectData nsHouseSoilCollectData = new NsHouseSoilCollectData();
        // 数据主键
        // nsHouseSoilCollectData.setSoilId(0L);
        // 大棚id
        // nsHouseSoilCollectData.setHouseId(0L);
        // 大棚编号
        nsHouseSoilCollectData.setHouseName("大棚编号");
        // 节点ID
        nsHouseSoilCollectData.setN("节点ID");
        // 采集时间
        nsHouseSoilCollectData.setD("采集时间");
/*        // 环境传感器和网关检测到的环境温度,只有当节点有该传感器时，这个属性才会被发送
        nsHouseSoilCollectData.setT(0);
        // 环境光照
        nsHouseSoilCollectData.setL(0);
        // 环境传感器和网关检测到的环境湿度,只有当节点有该传感器时，这个属性才会被发送
        nsHouseSoilCollectData.setQ(0);*/
        // 土壤EC,只有当节点有该传感器时，这个属性才会被发送
        nsHouseSoilCollectData.setE(0);
        // 土壤传感器测得的土壤TAO（温度）,只有当节点有该传感器时，这个属性才会被发送
        nsHouseSoilCollectData.setA(0);
        // 壤传感器测得的土壤MO（湿度）,只有当节点有该传感器时，这个属性才会被发送
        nsHouseSoilCollectData.setM(0);
        // 土壤PH测量范围：0～14，精度：±0.3
/*        nsHouseSoilCollectData.setPh(0);*/
        // 土壤传感器电量
        nsHouseSoilCollectData.setB(0);
        // 删除标志
        nsHouseSoilCollectData.setDeleted(0);
        // 创建人
        nsHouseSoilCollectData.setCreateBy(0L);
        // 创建时间
        nsHouseSoilCollectData.setCreateTime(new Date());
        // 更新人
        nsHouseSoilCollectData.setUpdateBy(0L);
        // 更新时间
        nsHouseSoilCollectData.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsHouseSoilCollectDataApi.validatePo(nsHouseSoilCollectData);

        // 名称唯一性检查?
        // nsHouseSoilCollectDataService.checkNameExisted(nsHouseSoilCollectData);

        NsHouseSoilCollectData result = nsHouseSoilCollectDataService.saveNsHouseSoilCollectData(nsHouseSoilCollectData);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（大棚土壤传感器采集数据）<br>
     * @author: xubinbin at jorchi
     * @date: 2023-02-16 11:35:29
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("大棚土壤传感器采集数据");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsHouseSoilCollectData");
        // 组件路径
        menu.setComponent("business/nsHouseSoilCollectData/nsHouseSoilCollectData");
        // 权限标识
        menu.setPerms("business:nsHouseSoilCollectData:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("xubinbin");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("大棚土壤传感器采集数据", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "大棚土壤传感器采集数据-查询", 1, "business:nsHouseSoilCollectData:list");
            addOperationMenu(menu.getMenuId(), "大棚土壤传感器采集数据-新增", 2, "business:nsHouseSoilCollectData:add");
            addOperationMenu(menu.getMenuId(), "大棚土壤传感器采集数据-修改", 3, "business:nsHouseSoilCollectData:edit");
            addOperationMenu(menu.getMenuId(), "大棚土壤传感器采集数据-删除", 4, "business:nsHouseSoilCollectData:remove");
            addOperationMenu(menu.getMenuId(), "大棚土壤传感器采集数据-导出", 5, "business:nsHouseSoilCollectData:export");
            addOperationMenu(menu.getMenuId(), "大棚土壤传感器采集数据-导入", 6, "business:nsHouseSoilCollectData:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsHouseSoilCollectData ok");
        }
    }

    /**
     * 创建操作按钮权限（大棚土壤传感器采集数据）<br>
     * @author: xubinbin at jorchi
     * @date: 2023-02-16 11:35:29
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（大棚土壤传感器采集数据）<br>
     * @author: xubinbin at jorchi
     * @date: 2023-02-16 11:35:29
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("大棚土壤传感器采集数据", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsHouseSoilCollectData menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
