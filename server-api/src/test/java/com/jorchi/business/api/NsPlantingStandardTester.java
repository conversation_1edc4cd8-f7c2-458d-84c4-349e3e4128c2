package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsPlantingStandard;
import com.jorchi.business.service.NsPlantingStandardService;
import com.jorchi.business.form.NsPlantingStandardForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.util.Date;

/**
 * ns_planting_standard-模块测试<br/>
 * 对应表名：ns_planting_standard，表备注：种植标准
 * @author: GeSenQi at jorchi
 * @date: 2022-11-10 14:44:46
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsPlantingStandardTester extends BaseApi {

    @Resource
    NsPlantingStandardService nsPlantingStandardService;

    @Resource
    NsPlantingStandardApi nsPlantingStandardApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（种植标准）<br>
     * @author: GeSenQi at jorchi
     * @date: 2022-11-10 14:44:46
     */
    @Test
    public void findByIdNsPlantingStandardTest() {
        Long plantingId;
        plantingId = 1L;

        NsPlantingStandard bean = nsPlantingStandardService.findById(plantingId);
        if (bean == null)
            throw ClientException.of("NsPlantingStandard not found error!");

        log.info("findById NsPlantingStandardTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（种植标准）<br>
     * @author: GeSenQi at jorchi
     * @date: 2022-11-10 14:44:46
     */
    @Test
    public void listPageNsPlantingStandardTest() {
        NsPlantingStandardForm nsPlantingStandardVo = new NsPlantingStandardForm();
        nsPlantingStandardVo.setPageNum(1);
        nsPlantingStandardVo.setPageSize(10);
        log.info("listPage NsPlantingStandardTest result:");
        log.info(getDataTable(nsPlantingStandardService.listPage(nsPlantingStandardVo)).toString());
    }
//
//    /**
//     * 删除测试（种植标准）<br>
//     * @author: GeSenQi at jorchi
//     * @date: 2022-11-10 14:44:46
//     */
//    @Test
//    public void deleteNsPlantingStandardTest() {
//        Long id = 0L;
//        log.info("delete NsPlantingStandardTest result:");
//        log.info(AjaxResult.success(nsPlantingStandardService.deleteNsPlantingStandard(id)).toString());
//    }
//
//    /**
//     * 新增或保存测试（种植标准）<br>
//     * ID 为空即为新增，否则为更新
//     * @author: GeSenQi at jorchi
//     * @date: 2022-11-10 14:44:46
//     */
//    @Test
//    public void saveNsPlantingStandardTest () {
//        NsPlantingStandard nsPlantingStandard = new NsPlantingStandard();
//        // 种植标准主键
//        // nsPlantingStandard.setPlantingId(0L);
//        // 种植标准名称
//        nsPlantingStandard.setPlantingName("种植标准名称");
//        // 作物名称
//        nsPlantingStandard.setCropName("作物名称");
//        // 品种
//        nsPlantingStandard.setBreeds("品种");
//        // 种植模式
//        nsPlantingStandard.setCroppingPattern("种植模式");
//        // 删除标志
//        nsPlantingStandard.setDeleted(0);
//        // 创建人
//        nsPlantingStandard.setCreateBy(0L);
//        // 创建时间
//        nsPlantingStandard.setCreateTime(new Date());
//        // 更新人
//        nsPlantingStandard.setUpdateBy(0L);
//        // 更新时间
//        nsPlantingStandard.setUpdateTime(new Date());
//
//        // 检查待保存数据的合法性
//        nsPlantingStandardApi.validatePo(nsPlantingStandard);
//
//        // 名称唯一性检查?
//        // nsPlantingStandardService.checkNameExisted(nsPlantingStandard);
//
//        NsPlantingStandard result = nsPlantingStandardService.saveNsPlantingStandard(nsPlantingStandard);
//        log.info("save result:");
//        log.info(result.toString());
//    }


    /**
     * 生成菜单（种植标准）<br>
     * @author: GeSenQi at jorchi
     * @date: 2022-11-10 14:44:46
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("种植标准");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsPlantingStandard");
        // 组件路径
        menu.setComponent("business/nsPlantingStandard/nsPlantingStandard");
        // 权限标识
        menu.setPerms("business:nsPlantingStandard:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("GeSenQi");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("种植标准", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "种植标准-查询", 1, "business:nsPlantingStandard:list");
            addOperationMenu(menu.getMenuId(), "种植标准-新增", 2, "business:nsPlantingStandard:add");
            addOperationMenu(menu.getMenuId(), "种植标准-修改", 3, "business:nsPlantingStandard:edit");
            addOperationMenu(menu.getMenuId(), "种植标准-删除", 4, "business:nsPlantingStandard:remove");
            addOperationMenu(menu.getMenuId(), "种植标准-导出", 5, "business:nsPlantingStandard:export");
            addOperationMenu(menu.getMenuId(), "种植标准-导入", 6, "business:nsPlantingStandard:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsPlantingStandard ok");
        }
    }

    /**
     * 创建操作按钮权限（种植标准）<br>
     * @author: GeSenQi at jorchi
     * @date: 2022-11-10 14:44:46
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（种植标准）<br>
     * @author: GeSenQi at jorchi
     * @date: 2022-11-10 14:44:46
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("种植标准", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsPlantingStandard menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
