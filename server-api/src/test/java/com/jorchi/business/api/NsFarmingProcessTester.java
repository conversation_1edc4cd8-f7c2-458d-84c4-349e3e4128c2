package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsFarmingProcess;
import com.jorchi.business.service.NsFarmingProcessService;
import com.jorchi.business.form.NsFarmingProcessForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ns_farming_process-模块测试<br/>
 * 对应表名：ns_farming_process，表备注：农事流程
 * @author: zjh at jorchi
 * @date: 2022-11-09 16:32:55
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsFarmingProcessTester extends BaseApi {

    @Resource
    NsFarmingProcessService nsFarmingProcessService;

    @Resource
    NsFarmingProcessApi nsFarmingProcessApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（农事流程）<br>
     * @author: zjh at jorchi
     * @date: 2022-11-09 16:32:55
     */
    @Test
    public void findByIdNsFarmingProcessTest() {
        Long farmingProcessId;
        farmingProcessId = 1L;

        NsFarmingProcess bean = nsFarmingProcessService.findById(farmingProcessId);
        if (bean == null)
            throw ClientException.of("NsFarmingProcess not found error!");

        log.info("findById NsFarmingProcessTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（农事流程）<br>
     * @author: zjh at jorchi
     * @date: 2022-11-09 16:32:55
     */
    @Test
    public void listPageNsFarmingProcessTest() {
        NsFarmingProcessForm nsFarmingProcessVo = new NsFarmingProcessForm();
        nsFarmingProcessVo.setPageNum(1);
        nsFarmingProcessVo.setPageSize(10);
        log.info("listPage NsFarmingProcessTest result:");
        log.info(getDataTable(nsFarmingProcessService.listPage(nsFarmingProcessVo)).toString());
    }

    /**
     * 删除测试（农事流程）<br>
     * @author: zjh at jorchi
     * @date: 2022-11-09 16:32:55
     */
    @Test
    public void deleteNsFarmingProcessTest() {
        Long id = 0L;
        log.info("delete NsFarmingProcessTest result:");
        log.info(AjaxResult.success(nsFarmingProcessService.deleteNsFarmingProcess(id,1L)).toString());
    }

    /**
     * 新增或保存测试（农事流程）<br>
     * ID 为空即为新增，否则为更新
     * @author: zjh at jorchi
     * @date: 2022-11-09 16:32:55
     */
    @Test
    public void saveNsFarmingProcessTest () {
        NsFarmingProcess nsFarmingProcess = new NsFarmingProcess();
        // 农事流程id
        // nsFarmingProcess.setFarmingProcessId(0L);
        // 农事流程名称
        nsFarmingProcess.setProcessName("农事流程名称");
        // 投入品需求1-无需物料2-投入肥料3-投入物料
        nsFarmingProcess.setInputType(0);
        // 作业时间
        nsFarmingProcess.setInputTime("0");
        // 备注
        nsFarmingProcess.setRemark("备注");
        // 创建人id
        nsFarmingProcess.setCreateId(0L);
        // 创建时间
        nsFarmingProcess.setCreateTime(new Date());
        // 修改人id
        nsFarmingProcess.setUpdateId(0L);
        // 修改时间
        nsFarmingProcess.setUpdateTime(new Date());
        // 是否删除0-未删除2-已删除
        nsFarmingProcess.setDeleted(0);

        // 检查待保存数据的合法性
        nsFarmingProcessApi.validatePo(nsFarmingProcess);

        // 名称唯一性检查?
        // nsFarmingProcessService.checkNameExisted(nsFarmingProcess);

        NsFarmingProcess result = nsFarmingProcessService.saveNsFarmingProcess(nsFarmingProcess,1L);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（农事流程）<br>
     * @author: zjh at jorchi
     * @date: 2022-11-09 16:32:55
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("农事流程");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsFarmingProcess");
        // 组件路径
        menu.setComponent("business/nsFarmingProcess/nsFarmingProcess");
        // 权限标识
        menu.setPerms("business:nsFarmingProcess:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("zjh");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("农事流程", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "农事流程-查询", 1, "business:nsFarmingProcess:list");
            addOperationMenu(menu.getMenuId(), "农事流程-新增", 2, "business:nsFarmingProcess:add");
            addOperationMenu(menu.getMenuId(), "农事流程-修改", 3, "business:nsFarmingProcess:edit");
            addOperationMenu(menu.getMenuId(), "农事流程-删除", 4, "business:nsFarmingProcess:remove");
            addOperationMenu(menu.getMenuId(), "农事流程-导出", 5, "business:nsFarmingProcess:export");
            addOperationMenu(menu.getMenuId(), "农事流程-导入", 6, "business:nsFarmingProcess:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsFarmingProcess ok");
        }
    }

    /**
     * 创建操作按钮权限（农事流程）<br>
     * @author: zjh at jorchi
     * @date: 2022-11-09 16:32:55
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（农事流程）<br>
     * @author: zjh at jorchi
     * @date: 2022-11-09 16:32:55
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("农事流程", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsFarmingProcess menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
