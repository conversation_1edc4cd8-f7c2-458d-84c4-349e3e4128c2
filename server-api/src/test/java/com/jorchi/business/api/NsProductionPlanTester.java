package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsProductionPlan;
import com.jorchi.business.service.NsProductionPlanService;
import com.jorchi.business.form.NsProductionPlanForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.util.Date;

/**
 * ns_production_plan-模块测试<br/>
 * 对应表名：ns_production_plan，表备注：生产计划
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON> at jorchi
 * @date: 2022-11-11 11:56:05
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsProductionPlanTester extends BaseApi {

    @Resource
    NsProductionPlanService nsProductionPlanService;

    @Resource
    NsProductionPlanApi nsProductionPlanApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（生产计划）<br>
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-11 11:56:05
     */
    @Test
    public void findByIdNsProductionPlanTest() {
        Long id;
        id = 1L;

        NsProductionPlan bean = nsProductionPlanService.findById(id);
        if (bean == null)
            throw ClientException.of("NsProductionPlan not found error!");

        log.info("findById NsProductionPlanTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（生产计划）<br>
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-11 11:56:05
     */
    @Test
    public void listPageNsProductionPlanTest() {
        NsProductionPlanForm nsProductionPlanVo = new NsProductionPlanForm();
        nsProductionPlanVo.setPageNum(1);
        nsProductionPlanVo.setPageSize(10);
        log.info("listPage NsProductionPlanTest result:");
        log.info(getDataTable(nsProductionPlanService.listPage(nsProductionPlanVo)).toString());
    }

    /**
     * 删除测试（生产计划）<br>
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-11 11:56:05
     */
    @Test
    public void deleteNsProductionPlanTest() {
        Long id = 0L;
        log.info("delete NsProductionPlanTest result:");
        log.info(AjaxResult.success(nsProductionPlanService.deleteNsProductionPlan(id)).toString());
    }

    /**
     * 新增或保存测试（生产计划）<br>
     * ID 为空即为新增，否则为更新
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-11 11:56:05
     */
    @Test
    public void saveNsProductionPlanTest () {
        NsProductionPlan nsProductionPlan = new NsProductionPlan();
        // 主键id
        // nsProductionPlan.setId(0L);
        // 种植区
        nsProductionPlan.setPlantName("种植区");
        // 大棚id
        nsProductionPlan.setPlantId(0L);
        // 大棚编号id
        nsProductionPlan.setHouseId(0L);
        // 大棚编号
        nsProductionPlan.setHouseName("大棚编号");
        // 作物名称id
        nsProductionPlan.setCropId(0L);
        // 作物名称
        nsProductionPlan.setCropName("作物名称");
        // 品种
        nsProductionPlan.setBreeds("品种");
        // 种植模式
        nsProductionPlan.setCroppingPattern("种植模式");
        // 种植标准id
        nsProductionPlan.setPlantingStandardId(0L);
        // 种植标准名称
        nsProductionPlan.setStandardName("种植标准名称");
        // 种植面积
        nsProductionPlan.setHouseArea("种植面积");
        // 积温需求
        nsProductionPlan.setTemperature("积温需求");
        // 种植日期
        nsProductionPlan.setPlantDate(new Date());
        // 种植天数
        nsProductionPlan.setPlantDays("种植天数");
        // 种子用量
        nsProductionPlan.setConsumption("种子用量");
        // 技术人员id
        nsProductionPlan.setArtisanId(0L);
        // 技术人员名字
        nsProductionPlan.setArtisanName("技术人员名字");
        // 采收日期
        nsProductionPlan.setHarvestDate(new Date());
        // 预计产量
        nsProductionPlan.setEstimatedOutput("预计产量");
        // 预计总产量
        nsProductionPlan.setEstimatedTotalOutput("预计总产量");
        // 实际产量
        nsProductionPlan.setActualOutput("实际产量");
        // 实际总产量
        nsProductionPlan.setActualTotalOutput("实际总产量");
        // 删除标志
        nsProductionPlan.setDeleted(false);
        // 创建人
        nsProductionPlan.setCreateBy(0L);
        // 创建时间
        nsProductionPlan.setCreateTime(new Date());
        // 更新人
        nsProductionPlan.setUpdateBy(0L);
        // 更新时间
        nsProductionPlan.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsProductionPlanApi.validatePo(nsProductionPlan);

        // 名称唯一性检查?
        // nsProductionPlanService.checkNameExisted(nsProductionPlan);

        NsProductionPlan result = nsProductionPlanService.saveNsProductionPlan(nsProductionPlan);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（生产计划）<br>
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-11 11:56:05
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("生产计划");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsProductionPlan");
        // 组件路径
        menu.setComponent("business/nsProductionPlan/nsProductionPlan");
        // 权限标识
        menu.setPerms("business:nsProductionPlan:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("ChenLiFeng");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("生产计划", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "生产计划-查询", 1, "business:nsProductionPlan:list");
            addOperationMenu(menu.getMenuId(), "生产计划-新增", 2, "business:nsProductionPlan:add");
            addOperationMenu(menu.getMenuId(), "生产计划-修改", 3, "business:nsProductionPlan:edit");
            addOperationMenu(menu.getMenuId(), "生产计划-删除", 4, "business:nsProductionPlan:remove");
//            addOperationMenu(menu.getMenuId(), "生产计划-导出", 5, "business:nsProductionPlan:export");
//            addOperationMenu(menu.getMenuId(), "生产计划-导入", 6, "business:nsProductionPlan:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsProductionPlan ok");
        }
    }

    /**
     * 创建操作按钮权限（生产计划）<br>
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-11 11:56:05
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（生产计划）<br>
     * @author: ChenLiFeng at jorchi
     * @date: 2022-11-11 11:56:05
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("生产计划", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsProductionPlan menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
