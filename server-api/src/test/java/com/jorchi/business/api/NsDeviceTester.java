package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsDevice;
import com.jorchi.business.service.NsDeviceService;
import com.jorchi.business.form.NsDeviceForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartHttpServletRequest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ns_device-模块测试<br/>
 * 对应表名：ns_device，表备注：农场设备表
 * @author: 周建宇 at jorchi
 * @date: 2025-01-22 16:19:22
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsDeviceTester extends BaseApi {

    @Resource
    NsDeviceService nsDeviceService;

    @Resource
    NsDeviceApi nsDeviceApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（农场设备表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-22 16:19:22
     */
    @Test
    public void findByIdNsDeviceTest() {
        Long deviceId;
        deviceId = 1L;

        NsDevice bean = nsDeviceService.findById(deviceId);
        if (bean == null)
            throw ClientException.of("NsDevice not found error!");

        log.info("findById NsDeviceTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（农场设备表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-22 16:19:22
     */
    @Test
    public void listPageNsDeviceTest() {
        NsDeviceForm nsDeviceVo = new NsDeviceForm();
        nsDeviceVo.setPageNum(1);
        nsDeviceVo.setPageSize(10);
        log.info("listPage NsDeviceTest result:");
        log.info(getDataTable(nsDeviceService.listPage(nsDeviceVo)).toString());
    }

    /**
     * 删除测试（农场设备表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-22 16:19:22
     */
    @Test
    public void deleteNsDeviceTest() {
        Long id = 0L;
        log.info("delete NsDeviceTest result:");
        log.info(AjaxResult.success(nsDeviceService.deleteNsDevice(id)).toString());
    }

    /**
     * 新增或保存测试（农场设备表）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2025-01-22 16:19:22
     */
    @Test
    public void saveNsDeviceTest () {
        NsDevice nsDevice = new NsDevice();
        // 设备id
        // nsDevice.setDeviceId(0L);
        // 农场ID
        // nsDevice.setDeptId(0L);
        // 附件
        nsDevice.setFile("附件");
        // 设备编号
        nsDevice.setDeviceCode("设备编号");
        // 删除标志
        nsDevice.setDeleted(0);
        // 创建人
        nsDevice.setCreateBy(0L);
        // 创建时间
        nsDevice.setCreateTime(new Date());
        // 更新人
        nsDevice.setUpdateBy(0L);
        // 更新时间
        nsDevice.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsDeviceApi.validatePo(nsDevice);

        // 名称唯一性检查?
        // nsDeviceService.checkNameExisted(nsDevice);

        NsDevice result = nsDeviceService.saveNsDevice(nsDevice);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（农场设备表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-22 16:19:22
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("农场设备表");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsDevice");
        // 组件路径
        menu.setComponent("business/nsDevice/nsDevice");
        // 权限标识
        menu.setPerms("business:nsDevice:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("周建宇");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("农场设备表", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "农场设备表-查询", 1, "business:nsDevice:list");
            addOperationMenu(menu.getMenuId(), "农场设备表-新增", 2, "business:nsDevice:add");
            addOperationMenu(menu.getMenuId(), "农场设备表-修改", 3, "business:nsDevice:edit");
            addOperationMenu(menu.getMenuId(), "农场设备表-删除", 4, "business:nsDevice:remove");
            addOperationMenu(menu.getMenuId(), "农场设备表-导出", 5, "business:nsDevice:export");
            addOperationMenu(menu.getMenuId(), "农场设备表-导入", 6, "business:nsDevice:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsDevice ok");
        }
    }

    /**
     * 创建操作按钮权限（农场设备表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-22 16:19:22
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（农场设备表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-22 16:19:22
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("农场设备表", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsDevice menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
