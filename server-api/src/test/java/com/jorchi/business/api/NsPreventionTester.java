package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsPrevention;
import com.jorchi.business.service.NsPreventionService;
import com.jorchi.business.form.NsPreventionForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartHttpServletRequest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.util.Date;

/**
 * ns_prevention-模块测试<br/>
 * 对应表名：ns_prevention，表备注：防治对象表
 * @author: GeSenQi at jorchi
 * @date: 2022-10-27 17:51:12
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsPreventionTester extends BaseApi {

    @Resource
    NsPreventionService nsPreventionService;

    @Resource
    NsPreventionApi nsPreventionApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（防治对象表）<br>
     * @author: GeSenQi at jorchi
     * @date: 2022-10-27 17:51:12
     */
    @Test
    public void findByIdNsPreventionTest() {
        Long preventionId;
        preventionId = 1L;

        NsPrevention bean = nsPreventionService.findById(preventionId);
        if (bean == null)
            throw ClientException.of("NsPrevention not found error!");

        log.info("findById NsPreventionTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（防治对象表）<br>
     * @author: GeSenQi at jorchi
     * @date: 2022-10-27 17:51:12
     */
    @Test
    public void listPageNsPreventionTest() {
        NsPreventionForm nsPreventionVo = new NsPreventionForm();
        nsPreventionVo.setPageNum(1);
        nsPreventionVo.setPageSize(10);
        log.info("listPage NsPreventionTest result:");
        log.info(getDataTable(nsPreventionService.listPage(nsPreventionVo)).toString());
    }

//    /**
//     * 删除测试（防治对象表）<br>
//     * @author: GeSenQi at jorchi
//     * @date: 2022-10-27 17:51:12
//     */
//    @Test
//    public void deleteNsPreventionTest() {
//        Long id = 0L;
//        log.info("delete NsPreventionTest result:");
//        log.info(AjaxResult.success(nsPreventionService.deleteNsPrevention(id)).toString());
//    }
//
//    /**
//     * 新增或保存测试（防治对象表）<br>
//     * ID 为空即为新增，否则为更新
//     * @author: GeSenQi at jorchi
//     * @date: 2022-10-27 17:51:12
//     */
//    @Test
//    public void saveNsPreventionTest () {
//        NsPrevention nsPrevention = new NsPrevention();
//        // 防治对象主键
//        // nsPrevention.setPreventionId(0L);
//        // 农资主键
//        // nsPrevention.setAgriculturalMaterialsId(0L);
//        // 防治对象
//        nsPrevention.setPreventionName("防治对象");
//        // 最小用药量
//        nsPrevention.setDosageMin(0);
//        // 最大用药量
//        nsPrevention.setDosageMax(0);
//        // 用药单位
//        nsPrevention.setDosageUnit("用药单位");
//        // 备注
//        nsPrevention.setRemark("备注");
//        // 删除标志（0代表存在2代表删除）
//        nsPrevention.setDeleted(0);
//        // 创建人
//        nsPrevention.setCreateBy(0L);
//        // 创建时间
//        nsPrevention.setCreateTime(new Date());
//        // 更新人
//        nsPrevention.setUpdateBy(0L);
//        // 更新时间
//        nsPrevention.setUpdateTime(new Date());
//
//        // 检查待保存数据的合法性
//        nsPreventionApi.validatePo(nsPrevention);
//
//        // 名称唯一性检查?
//        // nsPreventionService.checkNameExisted(nsPrevention);
//
//        NsPrevention result = nsPreventionService.saveNsPrevention(nsPrevention);
//        log.info("save result:");
//        log.info(result.toString());
//    }


    /**
     * 生成菜单（防治对象表）<br>
     * @author: GeSenQi at jorchi
     * @date: 2022-10-27 17:51:12
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("防治对象表");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsPrevention");
        // 组件路径
        menu.setComponent("business/nsPrevention/nsPrevention");
        // 权限标识
        menu.setPerms("business:nsPrevention:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("GeSenQi");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("防治对象表", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "防治对象表-查询", 1, "business:nsPrevention:list");
            addOperationMenu(menu.getMenuId(), "防治对象表-新增", 2, "business:nsPrevention:add");
            addOperationMenu(menu.getMenuId(), "防治对象表-修改", 3, "business:nsPrevention:edit");
            addOperationMenu(menu.getMenuId(), "防治对象表-删除", 4, "business:nsPrevention:remove");
            addOperationMenu(menu.getMenuId(), "防治对象表-导出", 5, "business:nsPrevention:export");
            addOperationMenu(menu.getMenuId(), "防治对象表-导入", 6, "business:nsPrevention:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsPrevention ok");
        }
    }

    /**
     * 创建操作按钮权限（防治对象表）<br>
     * @author: GeSenQi at jorchi
     * @date: 2022-10-27 17:51:12
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（防治对象表）<br>
     * @author: GeSenQi at jorchi
     * @date: 2022-10-27 17:51:12
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("防治对象表", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsPrevention menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
