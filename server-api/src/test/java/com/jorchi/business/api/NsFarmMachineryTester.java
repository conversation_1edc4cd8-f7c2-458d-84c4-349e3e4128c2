package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsFarmMachinery;
import com.jorchi.business.service.NsFarmMachineryService;
import com.jorchi.business.form.NsFarmMachineryForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartHttpServletRequest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ns_farm_machinery-模块测试<br/>
 * 对应表名：ns_farm_machinery，表备注：农机管理
 * @author: 周建宇 at jorchi
 * @date: 2024-12-14 20:13:40
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsFarmMachineryTester extends BaseApi {

    @Resource
    NsFarmMachineryService nsFarmMachineryService;

    @Resource
    NsFarmMachineryApi nsFarmMachineryApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（农机管理）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-14 20:13:40
     */
    @Test
    public void findByIdNsFarmMachineryTest() {
        Long machineryId;
        machineryId = 1L;

        NsFarmMachinery bean = nsFarmMachineryService.findById(machineryId);
        if (bean == null)
            throw ClientException.of("NsFarmMachinery not found error!");

        log.info("findById NsFarmMachineryTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（农机管理）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-14 20:13:40
     */
    @Test
    public void listPageNsFarmMachineryTest() {
        NsFarmMachineryForm nsFarmMachineryVo = new NsFarmMachineryForm();
        nsFarmMachineryVo.setPageNum(1);
        nsFarmMachineryVo.setPageSize(10);
        log.info("listPage NsFarmMachineryTest result:");
        log.info(getDataTable(nsFarmMachineryService.listPage(nsFarmMachineryVo)).toString());
    }

    /**
     * 删除测试（农机管理）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-14 20:13:40
     */
    @Test
    public void deleteNsFarmMachineryTest() {
        Long id = 0L;
        log.info("delete NsFarmMachineryTest result:");
        log.info(AjaxResult.success(nsFarmMachineryService.deleteNsFarmMachinery(id)).toString());
    }

    /**
     * 新增或保存测试（农机管理）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2024-12-14 20:13:40
     */
    @Test
    public void saveNsFarmMachineryTest () {
        NsFarmMachinery nsFarmMachinery = new NsFarmMachinery();
        // 农机主键
        // nsFarmMachinery.setMachineryId(0L);
        // 供应商
        nsFarmMachinery.setSupplierName("供应商");
        // 农机类型
        nsFarmMachinery.setMachineryType("农机类型");
        // 农机信息
        nsFarmMachinery.setDetails("农机信息");
        // 使用方法
        nsFarmMachinery.setUsageMethod("使用方法");
        // 农机单位
        nsFarmMachinery.setMachineryUnit("农机单位");
        // 备注
        nsFarmMachinery.setRemark("备注");
        // 删除标志（0代表存在2代表删除）
        nsFarmMachinery.setDeleted(0);
        // 创建人
        nsFarmMachinery.setCreateBy(0L);
        // 创建时间
        nsFarmMachinery.setCreateTime(new Date());
        // 更新人
        nsFarmMachinery.setUpdateBy(0L);
        // 更新时间
        nsFarmMachinery.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsFarmMachineryApi.validatePo(nsFarmMachinery);

        // 名称唯一性检查?
        // nsFarmMachineryService.checkNameExisted(nsFarmMachinery);

        NsFarmMachinery result = nsFarmMachineryService.saveNsFarmMachinery(nsFarmMachinery);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（农机管理）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-14 20:13:40
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("农机管理");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsFarmMachinery");
        // 组件路径
        menu.setComponent("business/nsFarmMachinery/nsFarmMachinery");
        // 权限标识
        menu.setPerms("business:nsFarmMachinery:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("周建宇");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("农机管理", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "农机管理-查询", 1, "business:nsFarmMachinery:list");
            addOperationMenu(menu.getMenuId(), "农机管理-新增", 2, "business:nsFarmMachinery:add");
            addOperationMenu(menu.getMenuId(), "农机管理-修改", 3, "business:nsFarmMachinery:edit");
            addOperationMenu(menu.getMenuId(), "农机管理-删除", 4, "business:nsFarmMachinery:remove");
            addOperationMenu(menu.getMenuId(), "农机管理-导出", 5, "business:nsFarmMachinery:export");
            addOperationMenu(menu.getMenuId(), "农机管理-导入", 6, "business:nsFarmMachinery:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsFarmMachinery ok");
        }
    }

    /**
     * 创建操作按钮权限（农机管理）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-14 20:13:40
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（农机管理）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-14 20:13:40
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("农机管理", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsFarmMachinery menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
