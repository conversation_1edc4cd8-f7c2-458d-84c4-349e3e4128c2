package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsAuditMaintain;
import com.jorchi.business.service.NsAuditMaintainService;
import com.jorchi.business.form.NsAuditMaintainForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.util.Date;

/**
 * ns_audit_maintain-模块测试<br/>
 * 对应表名：ns_audit_maintain，表备注：稽核维护
 * @author: xubinbin at jorchi
 * @date: 2022-11-11 09:47:46
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsAuditMaintainTester extends BaseApi {

    @Resource
    NsAuditMaintainService nsAuditMaintainService;

    @Resource
    NsAuditMaintainApi nsAuditMaintainApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（稽核维护）<br>
     * @author: xubinbin at jorchi
     * @date: 2022-11-11 09:47:46
     */
    @Test
    public void findByIdNsAuditMaintainTest() {
        Long auditId;
        auditId = 1L;

        NsAuditMaintain bean = nsAuditMaintainService.findById(auditId);
        if (bean == null)
            throw ClientException.of("NsAuditMaintain not found error!");

        log.info("findById NsAuditMaintainTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（稽核维护）<br>
     * @author: xubinbin at jorchi
     * @date: 2022-11-11 09:47:46
     */
    @Test
    public void listPageNsAuditMaintainTest() {
        NsAuditMaintainForm nsAuditMaintainVo = new NsAuditMaintainForm();
        nsAuditMaintainVo.setPageNum(1);
        nsAuditMaintainVo.setPageSize(10);
        log.info("listPage NsAuditMaintainTest result:");
        log.info(getDataTable(nsAuditMaintainService.listPage(nsAuditMaintainVo)).toString());
    }

    /**
     * 删除测试（稽核维护）<br>
     * @author: xubinbin at jorchi
     * @date: 2022-11-11 09:47:46
     */
    @Test
    public void deleteNsAuditMaintainTest() {
        Long id = 0L;
        log.info("delete NsAuditMaintainTest result:");
        log.info(AjaxResult.success(nsAuditMaintainService.deleteNsAuditMaintain(id)).toString());
    }

    /**
     * 新增或保存测试（稽核维护）<br>
     * ID 为空即为新增，否则为更新
     * @author: xubinbin at jorchi
     * @date: 2022-11-11 09:47:46
     */
    @Test
    public void saveNsAuditMaintainTest () {
        NsAuditMaintain nsAuditMaintain = new NsAuditMaintain();
        // 稽核ID
        // nsAuditMaintain.setAuditId(0L);
        // 稽核总项
        nsAuditMaintain.setAuditTotal("稽核总项");
        // 稽核事项
        nsAuditMaintain.setAuditName("稽核事项");
        // 描述信息
        nsAuditMaintain.setAuditDescription("描述信息");
        // 删除标志（0代表存在2代表删除）
        nsAuditMaintain.setDeleted(0);
        // 创建人
        nsAuditMaintain.setCreateBy(0L);
        // 创建时间
        nsAuditMaintain.setCreateTime(new Date());
        // 更新人
        nsAuditMaintain.setUpdateBy(0L);
        // 更新时间
        nsAuditMaintain.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsAuditMaintainApi.validatePo(nsAuditMaintain);

        // 名称唯一性检查?
        // nsAuditMaintainService.checkNameExisted(nsAuditMaintain);

/*        NsAuditMaintain result = nsAuditMaintainService.saveNsAuditMaintain(nsAuditMaintain);
        log.info("save result:");
        log.info(result.toString());*/
    }


    /**
     * 生成菜单（稽核维护）<br>
     * @author: xubinbin at jorchi
     * @date: 2022-11-11 09:47:46
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("稽核维护");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsAuditMaintain");
        // 组件路径
        menu.setComponent("business/nsAuditMaintain/nsAuditMaintain");
        // 权限标识
        menu.setPerms("business:nsAuditMaintain:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("xubinbin");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("稽核维护", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "稽核维护-查询", 1, "business:nsAuditMaintain:list");
            addOperationMenu(menu.getMenuId(), "稽核维护-新增", 2, "business:nsAuditMaintain:add");
            addOperationMenu(menu.getMenuId(), "稽核维护-修改", 3, "business:nsAuditMaintain:edit");
            addOperationMenu(menu.getMenuId(), "稽核维护-删除", 4, "business:nsAuditMaintain:remove");
//            addOperationMenu(menu.getMenuId(), "稽核维护-导出", 5, "business:nsAuditMaintain:export");
//            addOperationMenu(menu.getMenuId(), "稽核维护-导入", 6, "business:nsAuditMaintain:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsAuditMaintain ok");
        }
    }

    /**
     * 创建操作按钮权限（稽核维护）<br>
     * @author: xubinbin at jorchi
     * @date: 2022-11-11 09:47:46
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（稽核维护）<br>
     * @author: xubinbin at jorchi
     * @date: 2022-11-11 09:47:46
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("稽核维护", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsAuditMaintain menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
