package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.po.NsProductStockTaking;
import com.jorchi.business.service.NsProductStockTakingService;
import com.jorchi.business.form.NsProductStockTakingForm;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartHttpServletRequest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;

/**
 * ns_product_stock_taking-模块测试<br/>
 * 对应表名：ns_product_stock_taking，表备注：农产品库存盘点表
 * @author: 周建宇 at jorchi
 * @date: 2025-01-11 11:06:55
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsProductStockTakingTester extends BaseApi {

    @Resource
    NsProductStockTakingService nsProductStockTakingService;

    @Resource
    NsProductStockTakingApi nsProductStockTakingApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（农产品库存盘点表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-11 11:06:55
     */
    @Test
    public void findByIdNsProductStockTakingTest() {
        Long takingId;
        takingId = 1L;

        NsProductStockTaking bean = nsProductStockTakingService.findById(takingId);
        if (bean == null)
            throw ClientException.of("NsProductStockTaking not found error!");

        log.info("findById NsProductStockTakingTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（农产品库存盘点表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-11 11:06:55
     */
    @Test
    public void listPageNsProductStockTakingTest() {
        NsProductStockTakingForm nsProductStockTakingVo = new NsProductStockTakingForm();
        nsProductStockTakingVo.setPageNum(1);
        nsProductStockTakingVo.setPageSize(10);
        log.info("listPage NsProductStockTakingTest result:");
        log.info(getDataTable(nsProductStockTakingService.listPage(nsProductStockTakingVo)).toString());
    }

    /**
     * 删除测试（农产品库存盘点表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-11 11:06:55
     */
    @Test
    public void deleteNsProductStockTakingTest() {
        Long id = 0L;
        log.info("delete NsProductStockTakingTest result:");
        log.info(AjaxResult.success(nsProductStockTakingService.deleteNsProductStockTaking(id)).toString());
    }

    /**
     * 新增或保存测试（农产品库存盘点表）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2025-01-11 11:06:55
     */
    @Test
    public void saveNsProductStockTakingTest () {
        NsProductStockTaking nsProductStockTaking = new NsProductStockTaking();
        // 投入品库存盘点ID
        // nsProductStockTaking.setTakingId(0L);
        // 农产品ID
        // nsProductStockTaking.setProductStockId(0L);
        // 农产品类别
        nsProductStockTaking.setCropType("农产品类别");
        // 科别
        nsProductStockTaking.setCropCategory("科别");
        // 品种
        nsProductStockTaking.setBreeds("品种");
        // 农产品名称
        nsProductStockTaking.setCropName("农产品名称");
        // 库存量
        nsProductStockTaking.setStockQuantity(new BigDecimal(0));
        // 盘点数量
        nsProductStockTaking.setTakingStockQuantity(new BigDecimal(0));
        // 仓库ID
        nsProductStockTaking.setHouseId(0L);
        // 仓库名称
        nsProductStockTaking.setHouseName("仓库名称");
        // 盘点结果:0-正常,1-盘亏，2-盘盈
        nsProductStockTaking.setTakingResult(0);
        // 库存预警状态
        nsProductStockTaking.setWarningStatus("库存预警状态");
        // 盘点日期
        nsProductStockTaking.setTakingDate(new Date());
        // 盘点人
        nsProductStockTaking.setTakingOperator("盘点人");
        // 盘点人ID
        nsProductStockTaking.setTakingOperatorId(0L);
        // 删除标记
        nsProductStockTaking.setDeleted(0);
        // 创建人
        nsProductStockTaking.setCreateBy(0L);
        // 创建时间
        nsProductStockTaking.setCreateTime(new Date());
        // 更新人
        nsProductStockTaking.setUpdateBy(0L);
        // 更新时间
        nsProductStockTaking.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsProductStockTakingApi.validatePo(Arrays.asList(nsProductStockTaking));

        // 名称唯一性检查?
        // nsProductStockTakingService.checkNameExisted(nsProductStockTaking);

        NsProductStockTaking result = nsProductStockTakingService.saveNsProductStockTaking(nsProductStockTaking);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（农产品库存盘点表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-11 11:06:55
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("农产品库存盘点表");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsProductStockTaking");
        // 组件路径
        menu.setComponent("business/nsProductStockTaking/nsProductStockTaking");
        // 权限标识
        menu.setPerms("business:nsProductStockTaking:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("周建宇");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("农产品库存盘点表", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "农产品库存盘点表-查询", 1, "business:nsProductStockTaking:list");
            addOperationMenu(menu.getMenuId(), "农产品库存盘点表-新增", 2, "business:nsProductStockTaking:add");
            addOperationMenu(menu.getMenuId(), "农产品库存盘点表-修改", 3, "business:nsProductStockTaking:edit");
            addOperationMenu(menu.getMenuId(), "农产品库存盘点表-删除", 4, "business:nsProductStockTaking:remove");
            addOperationMenu(menu.getMenuId(), "农产品库存盘点表-导出", 5, "business:nsProductStockTaking:export");
            addOperationMenu(menu.getMenuId(), "农产品库存盘点表-导入", 6, "business:nsProductStockTaking:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsProductStockTaking ok");
        }
    }

    /**
     * 创建操作按钮权限（农产品库存盘点表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-11 11:06:55
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（农产品库存盘点表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2025-01-11 11:06:55
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("农产品库存盘点表", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsProductStockTaking menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
