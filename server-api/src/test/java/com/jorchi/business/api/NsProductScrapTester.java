package com.jorchi.business.api;

import com.jorchi.ApplicationFast;
import com.jorchi.business.form.NsProductScrapForm;
import com.jorchi.business.po.NsProductScrap;
import com.jorchi.business.service.NsProductScrapService;
import com.jorchi.framework.web.api.BaseApi;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysMenuDao;
import com.jorchi.project.system.domain.SysMenu;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.system.service.SysMenuServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * ns_product_scrap-模块测试<br/>
 * 对应表名：ns_product_scrap，表备注：报废表
 * @author: 周建宇 at jorchi
 * @date: 2024-12-11 10:17:42
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class NsProductScrapTester extends BaseApi {

    @Resource
    NsProductScrapService nsProductScrapService;

    @Resource
    NsProductScrapApi nsProductScrapApi;

    @Resource
    private SysMenuDao menuDao;

    @Resource
    private SysMenuServiceImpl menuService;

    @Resource
    private MaxIdServiceImpl maxIdService;

    /**
     * 按主键查询测试（报废表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-11 10:17:42
     */
    @Test
    public void findByIdNsProductScrapTest() {
        Long scrapId;
        scrapId = 1L;

        NsProductScrap bean = nsProductScrapService.findById(scrapId);
        if (bean == null)
            throw ClientException.of("NsProductScrap not found error!");

        log.info("findById NsProductScrapTest result:");
        log.info(bean.toString());
    }

    /**
     * 分页查询测试（报废表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-11 10:17:42
     */
    @Test
    public void listPageNsProductScrapTest() {
        NsProductScrapForm nsProductScrapVo = new NsProductScrapForm();
        nsProductScrapVo.setPageNum(1);
        nsProductScrapVo.setPageSize(10);
        log.info("listPage NsProductScrapTest result:");
        log.info(getDataTable(nsProductScrapService.listPage(nsProductScrapVo)).toString());
    }

    /**
     * 删除测试（报废表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-11 10:17:42
     */
    @Test
    public void deleteNsProductScrapTest() {
        Long id = 0L;
        log.info("delete NsProductScrapTest result:");
        log.info(AjaxResult.success(nsProductScrapService.deleteNsProductScrap(id)).toString());
    }

    /**
     * 新增或保存测试（报废表）<br>
     * ID 为空即为新增，否则为更新
     * @author: 周建宇 at jorchi
     * @date: 2024-12-11 10:17:42
     */
    @Test
    public void saveNsProductScrapTest () {
        NsProductScrap nsProductScrap = new NsProductScrap();
        // 报废id
        // nsProductScrap.setScrapId(0L);
        // 申请人
        // nsProductScrap.setApplicant(0L);
        // 申请人姓名
        nsProductScrap.setApplicantName("申请人姓名");
        // 业务类型，下拉选项框，选项值为：1.半成品入库2.半成品转成品入库3.销售退库
        nsProductScrap.setBusinessType(1);
        // 仓库id
        nsProductScrap.setWarehouseId(1L);
        // 仓库编号
        nsProductScrap.setWarehouseCode("仓库编号");
        // 销售订单号
        nsProductScrap.setOrderId("销售订单号");
        // 生产计划id
        nsProductScrap.setProductionPlanId(0L);
        // 半成品入库id
        nsProductScrap.setSemiProductId(0L);
        // 成品id
        nsProductScrap.setProductId(0L);
        // 农产品类别
        nsProductScrap.setCropType("农产品类别");
        // 农产品名称
        nsProductScrap.setCropName("农产品名称");
        // 报废数量
        nsProductScrap.setScrapQuantity(new BigDecimal(0));
        // 创建人
        nsProductScrap.setCreateBy(0L);
        // 创建时间
        nsProductScrap.setCreateTime(new Date());
        // 更新人
        nsProductScrap.setUpdateBy(0L);
        // 更新时间
        nsProductScrap.setUpdateTime(new Date());

        // 检查待保存数据的合法性
        nsProductScrapApi.validatePo(nsProductScrap);

        // 名称唯一性检查?
        // nsProductScrapService.checkNameExisted(nsProductScrap);

        NsProductScrap result = nsProductScrapService.saveNsProductScrap(nsProductScrap);
        log.info("save result:");
        log.info(result.toString());
    }


    /**
     * 生成菜单（报废表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-11 10:17:42
     */
    @Test
    public void saveMenu() {
        SysMenu menu = new SysMenu();
        // 菜单名称
        menu.setMenuName("报废表");
        // 上级菜单 1是系统管理；2是系统监控；3是系统工具
        menu.setParentId(1L);
        // 路由地址
        menu.setPath("nsProductScrap");
        // 组件路径
        menu.setComponent("business/nsProductScrap/nsProductScrap");
        // 权限标识
        menu.setPerms("business:nsProductScrap:list");
        // 菜单图标
        menu.setIcon("seedbed");
        // 创建者
        menu.setCreateBy("周建宇");

        menu.setIsAsc("asc");
        menu.setOrderNum(0);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("C");
        menu.setVisible("0");
        menu.setStatus("0");

        SysMenu preMenu = menuDao.findByNameAndParentId("报废表", null);
        if (preMenu == null) {
            // 设置新增菜单ID
            menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
            menuService.insertMenu(menu);

            // 菜单操作按钮权限
            addOperationMenu(menu.getMenuId(), "报废表-查询", 1, "business:nsProductScrap:list");
            addOperationMenu(menu.getMenuId(), "报废表-新增", 2, "business:nsProductScrap:add");
            addOperationMenu(menu.getMenuId(), "报废表-修改", 3, "business:nsProductScrap:edit");
            addOperationMenu(menu.getMenuId(), "报废表-删除", 4, "business:nsProductScrap:remove");
            addOperationMenu(menu.getMenuId(), "报废表-导出", 5, "business:nsProductScrap:export");
            addOperationMenu(menu.getMenuId(), "报废表-导入", 6, "business:nsProductScrap:import");
            log.info("insertMenu ReleaseVersion ok");
        }
        else {
            menu.setMenuId(preMenu.getMenuId());
            menuService.updateMenu(menu);
            log.info("updateMenu NsProductScrap ok");
        }
    }

    /**
     * 创建操作按钮权限（报废表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-11 10:17:42
     */
    public void addOperationMenu(Long parentMenuId, String menuName, int orderNum, String perms) {
        SysMenu menu = new SysMenu();
        // 设置菜单ID
        menu.setMenuId(maxIdService.getAndIncrement("sys_menu"));
        // 菜单名称
        menu.setMenuName(menuName);
        // 上级菜单 1->系统管理
        menu.setParentId(parentMenuId);
        menu.setPerms(perms);

        menu.setIsAsc("asc");
        menu.setOrderNum(orderNum);
        menu.setIsFrame(1);
        menu.setIsCache(0);
        menu.setMenuType("F");
        menu.setVisible("0");
        menu.setStatus("0");
        menu.setIcon("#");

        menu.setCreateBy("admin");
        menu.setCreateTime(new Date());

        menuService.insertMenu(menu);
        log.info("insertOpMenu ReleaseVersion ok");
    }

    /**
     * 删除菜单（报废表）<br>
     * @author: 周建宇 at jorchi
     * @date: 2024-12-11 10:17:42
     */
    @Test
    public void deleteMenu() {
        SysMenu preMenu = menuDao.findByNameAndParentId("报废表", null);
        if (preMenu == null) {
            log.info("deleteMenu but NsProductScrap menu not found!!!");
        }
        else {
            // 删除菜单
            menuDao.deleteMenuById(preMenu.getMenuId());
            // 删除该菜单的操作按钮
            menuDao.deleteMenuByParentId(preMenu.getMenuId());
            log.info("deleteMenu ReleaseVersion ok");
        }
    }
}
