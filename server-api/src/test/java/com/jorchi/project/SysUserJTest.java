//package com.jorchi.project;
//
//import com.jorchi.common.constant.CommonDef;
//import com.jorchi.project.system.domain.SysUser;
//import com.jorchi.project.system.service.SysUserServiceImpl;
//import org.junit.Test;
//
///**
// * 用户模块测试
// *
// * <AUTHOR> Sugar.Tan
// * @date : 2021-03-07 10:17
// */
//
//public class SysUserJTest {
//
//    SysUserServiceImpl sysUserServiceImpl = new SysUserServiceImpl();
//
//    @Test
//    public void appendRole() {
//        SysUser userZ = new SysUser();
//        sysUserServiceImpl.appendRole(userZ, CommonDef.ROLE_ID_REPAIRMAN);
//        sysUserServiceImpl.appendRole(userZ, CommonDef.ROLE_ID_REPAIRMAN);
//        System.out.println(userZ.getRoleIds());
//        sysUserServiceImpl.appendRole(userZ, CommonDef.ROLE_ID_SERVICE);
//        sysUserServiceImpl.appendRole(userZ, CommonDef.ROLE_ID_SERVICE);
//        System.out.println(userZ.getRoleIds());
//
//
//    }
//}
