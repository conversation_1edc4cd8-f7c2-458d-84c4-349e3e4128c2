package com.jorchi.project;

import com.jorchi.project.system.domain.SysDept;
import com.jorchi.project.system.dao.SysDeptDao;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 部门模块测试
 *
 * <AUTHOR> Sugar.Tan
 * @date : 2021-03-07 10:17
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class SysDeptTest {

    protected MockHttpServletRequest request;

    @Resource
    SysDeptDao sysDeptDao;

    @Before
    public void init (){
        request = new MockHttpServletRequest();
        request.setCharacterEncoding("UTF-8");
        /*request.addHeader(CommonDef.TOKEN, token);
        request.addHeader(CommonDef.HEADER_SHOP_ID, CommonDef.SUPPER_SHOP_ID);*/
    }

    @Test
    public void insertDept2() {
        long deptId = 109;
        SysDept dept = new SysDept();
        dept.setDeptId(deptId);
        dept.setDeptName("TEst deptName");
        dept.setParentId(null);
        dept.setStatus("0");
        dept.setCreateTime(new Date());
        dept.setPhone("150999999");
        SysDept pre = sysDeptDao.selectDeptById(deptId);
        if (pre != null) {
            //sysDeptDao.deleteByPrimaryKey(new BigDecimal(10999999L));
            sysDeptDao.deleteDeptById(deptId, 10999999L);
        }
        sysDeptDao.insertDept(dept);
        System.out.println("OK.");
    }


    //@RequestMapping("/test/insertDept")
    /*@Test
    public void insertDept() {
        SysDept dept = new SysDept();
        dept.setDeptId((10999999L));
        dept.setDeptName("TEst deptName");
        dept.setParentId(null);
        dept.setCreateTime(new Date());
        dept.setStatus("0");
        dept.setPhone("150999999");
        //sysDeptMapper.insertDept(dept);
        SysDept pre = sysDeptDao.selectByPrimaryKey((10999999L));
        if (pre != null) {
            sysDeptDao.deleteByPrimaryKey((10999999L));
        }
        sysDeptDao.insert(dept);
    }*/


    @Test
    public void insertDept3() {
        long deptId = 109;
        SysDept dept = new SysDept();
        dept.setDeptId(deptId);
        dept.setDeptName("TEst deptName");
        dept.setParentId(null);
        dept.setStatus("0");
        dept.setCreateTime(new Date());
        dept.setPhone("150999999");
        SysDept pre = sysDeptDao.selectDeptById(deptId);
        if (pre != null) {
            //sysDeptDao.deleteByPrimaryKey(new BigDecimal(10999999L));
            sysDeptDao.deleteDeptById(deptId, 10999999L);
        }
        sysDeptDao.insertDept(dept);
        System.out.println("OK.");
    }
}
