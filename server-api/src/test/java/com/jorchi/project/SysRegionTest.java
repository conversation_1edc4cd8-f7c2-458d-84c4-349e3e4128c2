package com.jorchi.project;

import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.api.SysRegionApi;
import com.jorchi.project.system.dao.SysDeptDao;
import com.jorchi.project.system.dao.SysRegionDao;
import com.jorchi.project.system.domain.SysDept;
import com.jorchi.project.system.po.SysRegion;
import com.jorchi.project.system.service.SysRegionServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 省市区模块测试
 *
 * <AUTHOR> Sugar.Tan
 * @date : 2021-10-09 10:17
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class SysRegionTest {

    protected MockHttpServletRequest request;

    @Resource
    SysRegionDao sysRegionDao;

    @Autowired
    SysRegionApi sysRegionApi;

    @Autowired
    private SysRegionServiceImpl regionService;

    @Before
    public void init (){
        request = new MockHttpServletRequest();
        request.setCharacterEncoding("UTF-8");
        /*request.addHeader(CommonDef.TOKEN, token);
        request.addHeader(CommonDef.HEADER_SHOP_ID, CommonDef.SUPPER_SHOP_ID);*/
    }

    @Test
    public void listDeptByParentId() {
        AjaxResult root = sysRegionApi.listDeptByParentId(null);
        log.info("root:" + root);

        // 省
        AjaxResult prov = sysRegionApi.listDeptByParentId(100L);
        log.info("prov:" + prov);

        // 市
        AjaxResult citys = sysRegionApi.listDeptByParentId(110000L);
        log.info("citys:" + citys);
    }

    @Test
    public void checkIsLeaf() {
        // 省列表
        List<SysRegion> regions = regionService.selectRegionByParentId(100L);
        for (SysRegion region : regions) {
            checkIsLeaf(region);
        }
    }

    /**
     * 如果没有子结点，设置为叶子结点
     */
    private void checkIsLeaf(SysRegion region) {
        List<SysRegion> children = regionService.selectRegionByParentId(region.getRegionId());
        if (children == null || children.isEmpty()) {
            region.setIsLeaf("1");
            sysRegionDao.updateSelectiveByPrimaryKey(region);
            return;
        }

        // 有子结点，设置为非叶子结点
        region.setIsLeaf("0");
        sysRegionDao.updateSelectiveByPrimaryKey(region);

        for (SysRegion child : children) {
            checkIsLeaf(child);
        }
    }
}
