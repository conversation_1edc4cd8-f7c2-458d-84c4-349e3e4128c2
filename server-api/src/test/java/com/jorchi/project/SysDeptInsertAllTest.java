package com.jorchi.project;

import com.jorchi.ApplicationFast;
import com.jorchi.common.utils.PinYinUtil;
import com.jorchi.common.utils.SecurityUtils;
import com.jorchi.project.system.dao.SysDeptDao;
import com.jorchi.project.system.domain.SysDept;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.ClientException;
import pdfc.claim.common.CommonUtil;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;

/**
 * 部门模块测试
 *
 * <AUTHOR> Sugar.Tan
 * @date : 2021-03-07 10:17
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
@Slf4j
public class SysDeptInsertAllTest {

    @Resource
    SysDeptDao sysDeptDao;

    /**
     * 全国组织机构代码初使化
     */
    @Test
    public void insertDeptAll() {
        ArrayList<String> allData = SysDeptAllData.getAllDeptSrcList();
        int count = 0;
        for (String data : allData) {
            SysDept dept = toDeptPo(data);
            // 429021	神农架林区
            if (dept == null) {
                log.error(CommonUtil.append("not save dept by Po is null:", data));
                continue;
            }
            insertDept(dept);
            log.info(CommonUtil.append(" saved count: ", (count++)));
        }
        System.out.println("insertDeptAll ok...");
    }

    /**
     * change line to Dept PO
     */
    private SysDept toDeptPo(String data) {
        try {
            String[] codeName = data.split(",");
            if (codeName.length != 2) {
                log.error("length check error!!! data:", data);
                throw ClientException.of("length check error, data:", data);
            }
            SysDept dept = new SysDept();
            dept.setDeptId(Long.valueOf(codeName[0]));
            dept.setDeptName(codeName[1].trim());
            dept.setCreateBy("admin");
            dept.setCreateTime(new Date());
            dept.setPinYin1(PinYinUtil.getPinYin(dept.getDeptName()));
            dept.setPinYin2(PinYinUtil.getPinYinAll(dept.getDeptName()));
            long parentId = -1;
            if (codeName[0].endsWith("0000")) // 省的上级是root
                parentId = 100;
            else if (codeName[1].endsWith("市") || codeName[0].endsWith("00")) // 市的上级是省
                parentId = Long.valueOf(CommonUtil.append(codeName[0].substring(0, 2), "0000"));
            else // 区的上级是市
                parentId = Long.valueOf(CommonUtil.append(codeName[0].substring(0, 4), "00"));

            if (parentId == -1)
                throw ClientException.of("parentId not set found error!!! deptCode:", codeName[0]);

            SysDept cityDept = getParentDeptByCache(Long.valueOf(CommonUtil.append(codeName[0].substring(0, 2), "0000")));
            if (cityDept != null && cityDept.getDeptName().equals("北京") ||
                    cityDept != null && cityDept.getDeptName().trim().endsWith("市")) { // 直辖市
                parentId = Long.valueOf(CommonUtil.append(codeName[0].substring(0, 2), "0000"));
            }

            dept.setParentId(parentId);
            SysDept parentDept = getParentDeptByCache(dept.getParentId());
            if (parentDept == null) {
                log.error(CommonUtil.append("parentDept not found error!!! parentID:", dept.getParentId(),
                        " deptCode:", dept.getDeptId()));
                throw ClientException.of(CommonUtil.append("parentDept not found error!!! parentID:", dept.getParentId(),
                        " deptCode:", dept.getDeptId()));
            }
            dept.setAncestors(CommonUtil.append(parentDept.getAncestors(), ",", dept.getParentId()));
            dept.setSno(String.valueOf(dept.getDeptId()));
            dept.setOrderNum(0);
            dept.setStatus("0");
            dept.setDelFlag("0");
            return dept;
        } catch (Throwable e) {
            log.error(CommonUtil.append("toDeptPo error!!!, data:", data));
            return null;
        }
    }

    private HashMap<Long, SysDept> DEPT_CACH = new HashMap<>(500);
    private SysDept getParentDeptByCache(Long parentId) {
        SysDept parentDept = DEPT_CACH.get(parentId);
        if (parentDept == null) {
            parentDept = sysDeptDao.selectDeptById(parentId);
            if (parentDept != null)
                DEPT_CACH.put(parentId, parentDept);
        }
        return parentDept;
    }


    public void insertDept(SysDept dept) {
        SysDept pre = sysDeptDao.selectDeptById(dept.getDeptId());
        if (pre != null) {
            //sysDeptDao.deleteByPrimaryKey(new BigDecimal(10999999L));
            // sysDeptDao.deleteDeptById(dept.getDeptId(), 10999999L);
            log.error("not save dept by 部门已经存在！！！ deptID:", dept.getDeptId(), " deptName:", dept.getDeptName());
            return;
        }
        sysDeptDao.insertDept(dept);
    }
}
