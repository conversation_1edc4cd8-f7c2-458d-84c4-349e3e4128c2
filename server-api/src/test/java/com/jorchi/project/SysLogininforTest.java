package com.jorchi.project;

import com.jorchi.ApplicationFast;
import com.jorchi.project.monitor.dao.SysLogininforDao;
import com.jorchi.project.monitor.domain.SysLogininfor;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 操作日志模块测试
 *
 * <AUTHOR> Sugar.Tan
 * @date : 2021-03-07 10:17
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
public class SysLogininforTest {

    @Resource
    SysLogininforDao logininforDao;
    @Resource
    MaxIdServiceImpl maxIdService;

    @Test
    public void select() {
        SysLogininfor logininfor = new SysLogininfor();
        logininfor.setBrowser("test");
        List<SysLogininfor> sysLogininfors = logininforDao.selectLogininforList(logininfor);
        System.out.println(sysLogininfors);

    }

    @Test
    public void insert() {
        SysLogininfor logininfor = new SysLogininfor();
        logininfor.setBrowser("test");
        logininfor.setStatus("1");
        //logininfor.setIpaddr("**********");
        logininfor.setLoginLocation("Localhost");
        logininfor.setMsg("test msg");
        logininfor.setOs("win");
        logininfor.setUserName("admin");
        logininfor.setLoginTime(new Date());
        logininfor.setCreateBy("admin-");
        logininfor.setCreateTime(new Date());
        logininfor.setRemark("re");
        logininfor.setSearchValue("--");
        logininfor.setInfoId(maxIdService.getAndIncrement("sysLoginInfo.id"));
        logininforDao.insertLogininfor(logininfor);

    }

    @Test
    public void delete() {
        logininforDao.deleteLogininforByIds(new Long[]{120L});

    }
}
