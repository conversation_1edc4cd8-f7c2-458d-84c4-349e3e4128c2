package com.jorchi.project;

import com.jorchi.project.monitor.domain.SysOperLog;
import com.jorchi.project.monitor.dao.SysOperLogDao;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 操作日志模块测试
 *
 * <AUTHOR> Sugar.Tan
 * @date : 2021-03-07 10:17
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class SysOperLogTest {

    protected MockHttpServletRequest request;

    @Resource
    SysOperLogDao sysOperLogMapper;

    @Test
    public void selectOperLog() {
        SysOperLog sysOperLog = sysOperLogMapper.selectOperLogById(180L);
        System.out.println(sysOperLog);
    }

    @Test
    public void insertOperLog() {
        SysOperLog sysOperLog = SysOperLog.builder()
                .deptName("name")
                .operTime(new Date())
                .build();
        sysOperLog.setOperId(-20L);
        sysOperLogMapper.insertOperlog(sysOperLog);
    }

    @Test
    public void deleteOperLog(){
        sysOperLogMapper.deleteOperLogByIds(new Long[]{180L});
    }
}
