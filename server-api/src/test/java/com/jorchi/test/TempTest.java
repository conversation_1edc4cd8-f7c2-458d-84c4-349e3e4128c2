package com.jorchi.test;


import org.junit.Test;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Base64;

/**
 * <AUTHOR> Sugar.Tan
 * @date : 2021-03-09 17:15
 */
public class TempTest {

    public static void main(String args[]) {
        new TempTest().showPwd();
    }

    public void showPwd() {
        /*String password = "123456";
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        String pwd1 = passwordEncoder.encode(password);
        try {Thread.sleep(1000); } catch (Exception e){}
        String pwd2 = passwordEncoder.encode(password);
        System.out.println(pwd1.equals(pwd2));*/

        //System.out.println(EncryptUtil.encryptByMD5("123456"));

        System.out.println(Long.MAX_VALUE - 1);
    }

    @Test
    public void base64Test() throws UnsupportedEncodingException {
        byte[] re = Base64.getEncoder().encode("https://192.168.2.80:8080/web/servletName231".getBytes());
        System.out.println(new String(re));

        System.out.println(URLEncoder.encode("https://192.168.2.80:8080/web/servletName", "UTF-8"));
    }
}
