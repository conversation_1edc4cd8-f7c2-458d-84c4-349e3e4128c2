package com.jorchi.system;

import com.jorchi.project.system.dao.SysRoleDao;
import com.jorchi.project.system.domain.SysRole;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 角色测试
 *
 * <AUTHOR> Sugar.Tan
 * @date : 2021-03-20 10:18
 */

@RunWith(SpringRunner.class)
@SpringBootTest
public class SysRoleTest {
    @Resource
    SysRoleDao sysRoleDao;

    @Test
    public void selectRoleList() {
        com.jorchi.project.system.domain.SysRole roleDto = new SysRole();
        Date endTime = new Date();
        Map<String, Object> params = new HashMap<>();
        params.put("endTime", endTime);
        roleDto.setParams(params);

        List<SysRole> reList = sysRoleDao.selectRoleList(roleDto);
        System.out.println(reList);
    }
}
