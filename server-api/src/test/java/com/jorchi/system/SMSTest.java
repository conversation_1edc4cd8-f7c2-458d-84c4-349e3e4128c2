package com.jorchi.system;

import com.jorchi.ApplicationFast;
import com.jorchi.sms.DayuApiClient;
import com.taobao.api.ApiException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import pdfc.claim.common.CommonUtil;

import java.net.InetAddress;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
public class SMSTest {

    /*@Autowired
    SmsSender smsSender;

    @Test
    public void sendTest() {
        try {
            smsSender.sendMessage("15057157389", "【监控服务程序运行异常】",
                    CommonUtil.append(" at:", InetAddress.getLocalHost().getHostAddress()));
        } catch (Throwable e2){
            e2.printStackTrace();
        }
    }*/

    @Test
    public void sendSMS() {
        String strJson = String.format("{code:'%s', product:'世合农园管理系统'}", "1234");

        System.out.println(strJson);
        // 测试电话写死
        //for (String mobile : mobiles)
        {
            try {
                // DayuApiClient.sendEnterPriseMsg("13060495859", strJson);
                String result = DayuApiClient.sendEnterPriseMsg("15057157389", strJson);
                System.out.println(result);
            } catch (ApiException e) {
                e.printStackTrace();
            }
        }
    }

    @Test
    public void doTest() {
        // 发送短信
        String strJson = String.format("{code:'%s', product:'世合农园管理系统'}", "1222");
        try {
            String result = DayuApiClient.sendEnterPriseMsg("15057157389", strJson);
        } catch (ApiException e) {
        }
    }
}
