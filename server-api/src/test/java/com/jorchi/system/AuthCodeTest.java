package com.jorchi.system;

import com.jorchi.ApplicationFast;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;


/**
 * 龙湖微服务测试
 *
 * <AUTHOR> Sugar.Tan
 * @date : 2021-03-22 14:33
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationFast.class)
public class AuthCodeTest {

    /*@Resource
    AuthCodeDao authCodeDao;*/


    /*@Test
    public void list() {
        AuthCode authCode = new AuthCode();
        authCode.setCode("123");
        authCode.setCreateTime(new Date());
        authCode.setUserCode("Sugar.Tan");
        authCodeDao.selectByPrimaryKey("123");
    }

    @Test
    public void saveDbIsOkTest() {
        AuthCode authCode = new AuthCode();
        authCode.setCode("123");
        authCode.setCreateTime(new Date());
        authCode.setUserCode("Sugar.Tan");

        AuthCode pre = authCodeDao.selectByPrimaryKey(authCode.getUserCode());
        if (pre == null)
            authCodeDao.insert(authCode);
        else
            authCodeDao.save(authCode);
        System.out.println("OK.");
    }*/
}
