package com.jorchi.system;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import com.jorchi.project.system.po.SysUser;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;


@Slf4j
public class JsonTest {

    /**
     * 字符串转 JSON
     */
    @Test
    public void string2JsonTest() {
        String str = "{\"name\":\"张三\",\"id\":\"100\"}";
        JsonObject json = new Gson().fromJson(str, JsonObject.class);
        log.info(json.toString());
    }

    /**
     * 字符串转 Bean
     */
    @Test
    public void string2BeanTest() {
        String str = "{\"userName\":\"张三\",\"userId\":\"100\",\"createTime\":\"2022-08-03 12:43:56\"}";
        SysUser usr = new Gson().fromJson(str, SysUser.class);
        log.info(usr.toString());
    }

    /**
     * Bean 转 JSON
     */
    @Test
    public void bean2JsonTest() {
        SysUser bean = new SysUser();
        bean.setUserName("张三");
        bean.setUserId(100L);
        bean.setCreateTime(new Date());

        String jsonObject = new Gson().toJson(bean);
        log.info("日期未格式化：");
        log.info(jsonObject); // 日期未格式化 （Aug 3, 2022 12:41:05 PM）

        // Gson gson = new Gson();
        Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
        jsonObject = gson.toJson(bean);
        log.info("日期未格式化后：");
        log.info(jsonObject);

    }


    /**
     * 字符串转 LinkedHashMap
     */
    @Test
    public void stringToMapTest() {
        String str = "{\"userName\":\"张三\",\"userId\":\"100\",\"createTime\":\"2022-08-03 12:43:56\"}";
        Map usr = new Gson().fromJson(str, Map.class);
        log.info(usr.toString());
    }

    /**
     * HashMap 转 JSON
     */
    @Test
    public void map2jsonTest() {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("userName", "张三");
        resultMap.put("userId", 100);
        resultMap.put("createTime", new Date());

        // Gson gson = new Gson();
        Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
        String jsonStr = gson.toJson(resultMap);
        System.out.println(jsonStr);
    }
}
