CREATE TABLE MAX_ID (
  ID_TYPE VARCHAR2(64) NOT NULL PRIMARY KEY,
  MAX_VALUE INT NOT NULL,
  EXPIRY_DATE DATE
);
COMMENT ON COLUMN MAX_ID.ID_TYPE IS '用户工号';
COMMENT ON COLUMN MAX_ID.MAX_VALUE IS '姓名';
COMMENT ON COLUMN MAX_ID.EXPIRY_DATE IS '密码';
COMMENT ON TABLE MAX_ID IS '主键自增辅助表，记录各表主键当前最大值';



CREATE TABLE POLICY_PRODUCT (
  ID INT NOT NULL PRIMARY KEY,
  PRODUCT_NAME VARCHAR2(255),
  PRODUCT_INSTRODUCT VARCHAR2(255),
  PRODUCT_STATUS VARCHAR2(2),
  CREDATE_TIME DATE,
  CREDATE_BY VARCHAR2(10),
  UPDATE_TIME DATE,
  UPDATE_BY VARCHAR2(10)
);
COMMENT ON COLUMN POLICY_PRODUCT.ID IS '主键';
COMMENT ON COLUMN POLICY_PRODUCT.PRODUCT_NAME IS '产品名称';
COMMENT ON COLUMN POLICY_PRODUCT.PRODUCT_INSTRODUCT IS '产品介绍';
COMMENT ON COLUMN POLICY_PRODUCT.PRODUCT_STATUS IS '产品状态';
COMMENT ON COLUMN POLICY_PRODUCT.CREDATE_TIME IS '创建时间';
COMMENT ON COLUMN POLICY_PRODUCT.CREDATE_BY IS '创建人';
COMMENT ON COLUMN POLICY_PRODUCT.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN POLICY_PRODUCT.UPDATE_BY IS '更新人';
COMMENT ON TABLE POLICY_PRODUCT IS '可投保产品表';


CREATE TABLE PRODUCT_VARIETY (
  ID INT NOT NULL PRIMARY KEY,
  VARIETIES VARCHAR2(255),
  PRODUCT VARCHAR2(255),
  PARENT_ID INT,
  PRODUCT_LEVEL VARCHAR2(2),
  STATUS VARCHAR2(2) DEFAULT '6',
  CREDATE_TIME DATE,
  CREDATE_BY VARCHAR2(10),
  UPDATE_BY VARCHAR2(10),
  UPDATE_TIME DATE,
  VARIETIES_TYPE VARCHAR2(255)
);
COMMENT ON COLUMN PRODUCT_VARIETY.ID IS '主键';
COMMENT ON COLUMN PRODUCT_VARIETY.VARIETIES IS '品种名称';
COMMENT ON COLUMN PRODUCT_VARIETY.PRODUCT IS '产品名称';
COMMENT ON COLUMN PRODUCT_VARIETY.PARENT_ID IS '父级ID';
COMMENT ON COLUMN PRODUCT_VARIETY.PRODUCT_LEVEL IS '级别 1 品种   2产品';
COMMENT ON COLUMN PRODUCT_VARIETY.STATUS IS '状态 1 启用  2禁用';
COMMENT ON COLUMN PRODUCT_VARIETY.CREDATE_TIME IS '创建时间';
COMMENT ON COLUMN PRODUCT_VARIETY.CREDATE_BY IS '创建人';
COMMENT ON COLUMN PRODUCT_VARIETY.UPDATE_BY IS '更新人';
COMMENT ON COLUMN PRODUCT_VARIETY.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN PRODUCT_VARIETY.VARIETIES_TYPE IS '品种类别';
COMMENT ON TABLE PRODUCT_VARIETY IS '产品品种表(基础数据)';

CREATE TABLE SYS_CONFIG (
  CONFIG_ID DECIMAL(20,0) NOT NULL PRIMARY KEY,
  CONFIG_NAME VARCHAR2(100) DEFAULT '',
  CONFIG_KEY VARCHAR2(100) DEFAULT '',
  CONFIG_VALUE VARCHAR2(100) DEFAULT '',
  CONFIG_TYPE VARCHAR2(1) DEFAULT 'N',
  CREATE_BY VARCHAR2(64) DEFAULT '',
  UPDATE_BY VARCHAR2(64),
  CREATE_TIME DATE  DEFAULT sysdate NOT NULL,
  UPDATE_TIME DATE  DEFAULT sysdate NOT NULL,
  REMARK VARCHAR2(500)
);
COMMENT ON COLUMN SYS_CONFIG.CONFIG_ID IS '参数主键';
COMMENT ON COLUMN SYS_CONFIG.CONFIG_NAME IS '参数名称';
COMMENT ON COLUMN SYS_CONFIG.CONFIG_KEY IS '参数键名';
COMMENT ON COLUMN SYS_CONFIG.CONFIG_VALUE IS '参数键值';
COMMENT ON COLUMN SYS_CONFIG.CONFIG_TYPE IS '系统内置（Y是 N否）';
COMMENT ON COLUMN SYS_CONFIG.CREATE_BY IS '创建者';
COMMENT ON COLUMN SYS_CONFIG.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN SYS_CONFIG.UPDATE_BY IS '更新者';
COMMENT ON COLUMN SYS_CONFIG.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN SYS_CONFIG.REMARK IS '备注';
COMMENT ON TABLE SYS_CONFIG IS '产品品种表(基础数据)';

CREATE TABLE SYS_DEPT (
  DEPT_ID DECIMAL(20,0) NOT NULL PRIMARY KEY,
  PARENT_ID DECIMAL(20,0) DEFAULT '0',
  ANCESTORS VARCHAR2(50) DEFAULT '' ,
  DEPT_NAME VARCHAR2(30) DEFAULT '' ,
  ORDER_NUM DECIMAL(4,0) DEFAULT '0' ,
  LEADER VARCHAR2(20) DEFAULT NULL ,
  PHONE VARCHAR2(11) DEFAULT NULL ,
  EMAIL VARCHAR2(50) DEFAULT NULL ,
  STATUS VARCHAR2(1) DEFAULT '0' ,
  DEL_FLAG VARCHAR2(1) DEFAULT '0' ,
  CREATE_BY VARCHAR2(64) DEFAULT '' ,
  CREATE_TIME DATE DEFAULT sysdate NOT NULL,
  UPDATE_BY VARCHAR2(64) DEFAULT '' ,
  UPDATE_TIME DATE DEFAULT sysdate NOT NULL,
  SNO VARCHAR2(64) DEFAULT NULL ,
  PIN_YIN_1 VARCHAR2(20) DEFAULT NULL ,
  PIN_YIN_2 VARCHAR2(64) DEFAULT NULL
  );

COMMENT ON COLUMN SYS_DEPT.DEPT_ID IS '部门主键';
COMMENT ON COLUMN SYS_DEPT.PARENT_ID IS '父部门ID';
COMMENT ON COLUMN SYS_DEPT.ANCESTORS IS '祖级列表';
COMMENT ON COLUMN SYS_DEPT.DEPT_NAME IS '部门名称';
COMMENT ON COLUMN SYS_DEPT.ORDER_NUM IS '显示顺序';
COMMENT ON COLUMN SYS_DEPT.LEADER IS '负责人';
COMMENT ON COLUMN SYS_DEPT.PHONE IS '联系电话';
COMMENT ON COLUMN SYS_DEPT.EMAIL IS '邮箱';
COMMENT ON COLUMN SYS_DEPT.STATUS IS '部门状态（0正常 1停用）';
COMMENT ON COLUMN SYS_DEPT.DEL_FLAG IS '删除标志（0代表存在 2代表删除）';
COMMENT ON COLUMN SYS_DEPT.CREATE_BY IS '创建者';
COMMENT ON COLUMN SYS_DEPT.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN SYS_DEPT.UPDATE_BY IS '更新者';
COMMENT ON COLUMN SYS_DEPT.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN SYS_DEPT.SNO IS '组织代码';
COMMENT ON COLUMN SYS_DEPT.PIN_YIN_1 IS '昵称拼音各字首字母';
COMMENT ON COLUMN SYS_DEPT.PIN_YIN_2 IS '昵称拼音';
COMMENT ON TABLE SYS_DEPT IS '部门信息表';

CREATE TABLE SYS_DICT_DATA (
  DICT_CODE DECIMAL(20,0) NOT NULL PRIMARY KEY,
  DICT_SORT DECIMAL(4,0) DEFAULT '0',
  DICT_LABEL VARCHAR2(100) DEFAULT '' ,
  DICT_VALUE VARCHAR2(100) DEFAULT '' ,
  DICT_TYPE VARCHAR2(100) DEFAULT '0' ,
  CSS_CLASS VARCHAR2(100) DEFAULT NULL ,
  LIST_CLASS VARCHAR2(100) DEFAULT NULL ,
  IS_DEFAULT VARCHAR2(1) DEFAULT 'N' ,
  STATUS VARCHAR2(1) DEFAULT '0' ,
  CREATE_BY VARCHAR2(64) DEFAULT '' ,
  CREATE_TIME DATE DEFAULT sysdate NOT NULL ,
  UPDATE_BY VARCHAR(500) DEFAULT NULL ,
  UPDATE_TIME DATE DEFAULT sysdate NOT NULL ,
  REMARK VARCHAR(500) DEFAULT NULL
  );

COMMENT ON COLUMN SYS_DICT_DATA.DICT_CODE IS '字典主键';
COMMENT ON COLUMN SYS_DICT_DATA.DICT_SORT IS '字典排序';
COMMENT ON COLUMN SYS_DICT_DATA.DICT_LABEL IS '字典标签';
COMMENT ON COLUMN SYS_DICT_DATA.DICT_VALUE IS '字典键值';
COMMENT ON COLUMN SYS_DICT_DATA.DICT_TYPE IS '字典类型';
COMMENT ON COLUMN SYS_DICT_DATA.CSS_CLASS IS '样式属性（其他样式扩展）';
COMMENT ON COLUMN SYS_DICT_DATA.LIST_CLASS IS '表格回显样式';
COMMENT ON COLUMN SYS_DICT_DATA.IS_DEFAULT IS '是否默认（Y是 N否）';
COMMENT ON COLUMN SYS_DICT_DATA.STATUS IS '部门状态（0正常 1停用）';
COMMENT ON COLUMN SYS_DICT_DATA.CREATE_BY IS '创建者';
COMMENT ON COLUMN SYS_DICT_DATA.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN SYS_DICT_DATA.UPDATE_BY IS '更新者';
COMMENT ON COLUMN SYS_DICT_DATA.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN SYS_DICT_DATA.REMARK IS '备注';
COMMENT ON TABLE SYS_DICT_DATA IS '字典数据表';

CREATE TABLE SYS_DICT_TYPE (
  DICT_ID DECIMAL(20,0) NOT NULL PRIMARY KEY,
  DICT_NAME VARCHAR2(100) DEFAULT '',
  DICT_TYPE VARCHAR2(100) DEFAULT '' ,
  STATUS VARCHAR2(1) DEFAULT '0' ,
  CREATE_BY VARCHAR2(64) DEFAULT '' ,
  CREATE_TIME DATE DEFAULT sysdate NOT NULL ,
  UPDATE_BY VARCHAR2(64) DEFAULT '' ,
  UPDATE_TIME DATE DEFAULT sysdate NOT NULL,
  REMARK VARCHAR2(500) DEFAULT NULL ,
  TYPE_KIND DECIMAL(8,0)  DEFAULT '1' NOT NULL
  );

COMMENT ON COLUMN SYS_DICT_TYPE.DICT_ID IS '字典主键';
COMMENT ON COLUMN SYS_DICT_TYPE.DICT_NAME IS '字典名称';
COMMENT ON COLUMN SYS_DICT_TYPE.DICT_TYPE IS '字典类型';
COMMENT ON COLUMN SYS_DICT_TYPE.STATUS IS '状态（0正常 1停用）';
COMMENT ON COLUMN SYS_DICT_TYPE.CREATE_BY IS '创建者';
COMMENT ON COLUMN SYS_DICT_TYPE.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN SYS_DICT_TYPE.UPDATE_BY IS '更新者';
COMMENT ON COLUMN SYS_DICT_TYPE.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN SYS_DICT_TYPE.REMARK IS '备注';
COMMENT ON COLUMN SYS_DICT_TYPE.TYPE_KIND IS '1是系统数据字典；2是应用数据字典';
COMMENT ON TABLE SYS_DICT_TYPE IS '字典类型表';


CREATE TABLE SYS_JOB (
  JOB_ID DECIMAL(20,0) NOT NULL PRIMARY KEY,
  JOB_NAME VARCHAR2(64) DEFAULT '' NOT NULL ,
  JOB_GROUP VARCHAR2(64) DEFAULT '' NOT NULL ,
  INVOKE_TARGET VARCHAR2(500) NOT NULL ,
  CRON_EXPRESSIONATE_BY VARCHAR2(255) DEFAULT '' ,
  MISFIRE_POLICY VARCHAR2(20) DEFAULT '3' ,
  CONCURRENT VARCHAR2(1) DEFAULT '1' ,
  STATUS VARCHAR2(1) DEFAULT '0',
  CREATE_BY VARCHAR2(64) DEFAULT '' ,
  CREATE_TIME DATE DEFAULT sysdate NOT NULL,
  UPDATE_BY VARCHAR2(64) DEFAULT '' ,
  UPDATE_TIME DATE DEFAULT sysdate NOT NULL,
  REMARK VARCHAR2(500)  DEFAULT ''
  );

COMMENT ON COLUMN SYS_JOB.JOB_ID IS '任务主键';
COMMENT ON COLUMN SYS_JOB.JOB_NAME IS '任务名称';
COMMENT ON COLUMN SYS_JOB.JOB_GROUP IS '任务组名';
COMMENT ON COLUMN SYS_JOB.INVOKE_TARGET IS '调用目标字符串';
COMMENT ON COLUMN SYS_JOB.CRON_EXPRESSIONATE_BY IS 'CRON执行表达式';
COMMENT ON COLUMN SYS_JOB.MISFIRE_POLICY IS '计划执行错误策略（1立即执行 2执行一次 3放弃执行）';
COMMENT ON COLUMN SYS_JOB.CRON_EXPRESSIONATE_BY IS '是否并发执行（0允许 1禁止）';
COMMENT ON COLUMN SYS_JOB.STATUS IS '状态（0正常 1暂停）';
COMMENT ON COLUMN SYS_JOB.CREATE_BY IS '创建者';
COMMENT ON COLUMN SYS_JOB.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN SYS_JOB.UPDATE_BY IS '更新者';
COMMENT ON COLUMN SYS_JOB.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN SYS_JOB.REMARK IS '备注信息';
COMMENT ON TABLE SYS_JOB IS '定时任务调度表';


CREATE TABLE SYS_JOB_LOG (
  JOB_LOG_ID DECIMAL(20,0) NOT NULL PRIMARY KEY,
  JOB_NAME VARCHAR2(64)  NOT NULL ,
  JOB_GROUP VARCHAR2(64)  NOT NULL ,
  INVOKE_TARGET VARCHAR2(500) NOT NULL ,
  JOB_MESSAGE VARCHAR2(500) DEFAULT NULL ,
  STATUS VARCHAR2(1) DEFAULT '0' ,
  EXCEPTION_INFO VARCHAR2(2000),
   CREATE_TIME DATE DEFAULT sysdate NOT NULL
  );

COMMENT ON COLUMN SYS_JOB_LOG.JOB_LOG_ID IS '日志主键';
COMMENT ON COLUMN SYS_JOB_LOG.JOB_NAME IS '任务名称';
COMMENT ON COLUMN SYS_JOB_LOG.JOB_GROUP IS '任务组名';
COMMENT ON COLUMN SYS_JOB_LOG.INVOKE_TARGET IS '调用目标字符串';
COMMENT ON COLUMN SYS_JOB_LOG.JOB_MESSAGE IS '日志信息';
COMMENT ON COLUMN SYS_JOB_LOG.STATUS IS '执行状态（0正常 1失败）';
COMMENT ON COLUMN SYS_JOB_LOG.EXCEPTION_INFO IS '异常信息';
COMMENT ON COLUMN SYS_JOB_LOG.CREATE_TIME IS '创建时间';
COMMENT ON TABLE SYS_JOB_LOG IS '定时任务调度日志表';

CREATE TABLE SYS_LOGININFOR (
  INFO_ID DECIMAL(20,0) NOT NULL PRIMARY KEY,
  USER_NAME VARCHAR2(50)   DEFAULT '' ,
  IPADDR VARCHAR2(128)   DEFAULT '' ,
  LOGIN_LOCATION VARCHAR2(255)  DEFAULT '' ,
  BROWSER VARCHAR2(250)  DEFAULT '' ,
  OS VARCHAR2(128)  DEFAULT '' ,
  STATUS VARCHAR2(1) DEFAULT '0',
  MSG VARCHAR2(255) DEFAULT '',
  LOGIN_TIME DATE DEFAULT sysdate NOT NULL
  );

COMMENT ON COLUMN SYS_LOGININFOR.INFO_ID IS '访问主键';
COMMENT ON COLUMN SYS_LOGININFOR.USER_NAME IS '登录账号';
COMMENT ON COLUMN SYS_LOGININFOR.IPADDR IS '登录IP地址';
COMMENT ON COLUMN SYS_LOGININFOR.LOGIN_LOCATION IS '登录地点';
COMMENT ON COLUMN SYS_LOGININFOR.BROWSER IS '浏览器类型';
COMMENT ON COLUMN SYS_LOGININFOR.OS IS '操作系统';
COMMENT ON COLUMN SYS_LOGININFOR.STATUS IS '登录状态（0成功 1失败）';
COMMENT ON COLUMN SYS_LOGININFOR.MSG IS '提示消息';
COMMENT ON COLUMN SYS_LOGININFOR.LOGIN_TIME IS '访问时间';
COMMENT ON TABLE SYS_LOGININFOR IS '系统访问记录';

CREATE TABLE SYS_MENU (
  MENU_ID DECIMAL(20,0) NOT NULL PRIMARY KEY ,
  MENU_NAME VARCHAR2(50) NOT NULL ,
  PARENT_ID DECIMAL(20,0) DEFAULT '0' ,
  ORDER_NUM DECIMAL(4,0) DEFAULT '0' ,
  PATH VARCHAR2(200) DEFAULT '' ,
  COMPONENT VARCHAR2(255) DEFAULT NULL ,
  IS_FRAME DECIMAL(1,0) DEFAULT '1' ,
  IS_CACHE DECIMAL(1,0) DEFAULT '0' ,
  MENU_TYPE VARCHAR2(1) DEFAULT '' ,
  VISIBLE VARCHAR2(1) DEFAULT '0' ,
  STATUS VARCHAR2(1) DEFAULT '0' ,
  PERMS VARCHAR2(100) DEFAULT NULL ,
  ICON VARCHAR2(100) DEFAULT '#' ,
  CREATE_BY VARCHAR2(64) DEFAULT '' ,
  CREATE_TIME DATE DEFAULT sysdate NOT NULL ,
  UPDATE_BY VARCHAR2(64) DEFAULT '',
  UPDATE_TIME DATE DEFAULT sysdate NOT NULL,
  REMARK VARCHAR2(500) DEFAULT ''
);

COMMENT ON COLUMN SYS_MENU.MENU_ID IS '菜单主键';
COMMENT ON COLUMN SYS_MENU.MENU_NAME IS '菜单名称';
COMMENT ON COLUMN SYS_MENU.PARENT_ID IS '父菜单ID';
COMMENT ON COLUMN SYS_MENU.ORDER_NUM IS '显示顺序';
COMMENT ON COLUMN SYS_MENU.PATH IS '请求地址';
COMMENT ON COLUMN SYS_MENU.COMPONENT IS '路由地址';
COMMENT ON COLUMN SYS_MENU.IS_FRAME IS '是否为外链（0是 1否）';
COMMENT ON COLUMN SYS_MENU.IS_CACHE IS '是否缓存（0缓存 1不缓存）';
COMMENT ON COLUMN SYS_MENU.MENU_TYPE IS '菜单类型（M目录 C菜单 F按钮）';
COMMENT ON COLUMN SYS_MENU.VISIBLE IS '菜单状态（0显示 1隐藏）';
COMMENT ON COLUMN SYS_MENU.STATUS IS '菜单状态（0正常 1停用）';
COMMENT ON COLUMN SYS_MENU.PERMS IS '权限标识';
COMMENT ON COLUMN SYS_MENU.ICON IS '菜单图标';
COMMENT ON COLUMN SYS_MENU.CREATE_BY IS '创建者';
COMMENT ON COLUMN SYS_MENU.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN SYS_MENU.UPDATE_BY IS '更新者';
COMMENT ON COLUMN SYS_MENU.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN SYS_MENU.REMARK IS '备注';
COMMENT ON TABLE SYS_MENU IS '菜单权限表';

CREATE TABLE SYS_OPER_LOG (
  OPER_ID DECIMAL(20,0) NOT NULL PRIMARY KEY ,
  TITLE VARCHAR2(50) DEFAULT '',
  BUSINESS_TYPE DECIMAL(2,0) DEFAULT '0' ,
  METHOD VARCHAR2(100) DEFAULT '',
  REQUEST_METHOD VARCHAR2(10) DEFAULT '' ,
  OPERATOR_TYPE DECIMAL(1,0) DEFAULT '0' ,
  OPER_NAME VARCHAR2(50) DEFAULT '',
  DEPT_NAME VARCHAR2(50) DEFAULT '' ,
  OPER_URL VARCHAR2(255) DEFAULT '' ,
  OPER_IP VARCHAR2(128) DEFAULT '' ,
  OPER_LOCATION VARCHAR2(255) DEFAULT '' ,
  OPER_PARAM VARCHAR2(2000) ,
  JSON_RESULT VARCHAR2(2000) ,
  STATUS DECIMAL(1,0) DEFAULT '0' ,
  ERROR_MSG VARCHAR2(2000) ,
  OPER_TIME DATE DEFAULT sysdate NOT NULL
);

COMMENT ON COLUMN SYS_OPER_LOG.OPER_ID IS '日志主键';
COMMENT ON COLUMN SYS_OPER_LOG.TITLE IS '模块标题';
COMMENT ON COLUMN SYS_OPER_LOG.BUSINESS_TYPE IS '业务类型（0其它 1新增 2修改 3删除）';
COMMENT ON COLUMN SYS_OPER_LOG.METHOD IS '方法名称';
COMMENT ON COLUMN SYS_OPER_LOG.REQUEST_METHOD IS '请求方式';
COMMENT ON COLUMN SYS_OPER_LOG.OPERATOR_TYPE IS '操作类别（0其它 1后台用户 2手机端用户）';
COMMENT ON COLUMN SYS_OPER_LOG.OPER_NAME IS '操作人员';
COMMENT ON COLUMN SYS_OPER_LOG.DEPT_NAME IS '部门名称';
COMMENT ON COLUMN SYS_OPER_LOG.OPER_URL IS '请求URL';
COMMENT ON COLUMN SYS_OPER_LOG.OPER_IP IS '主机地址';
COMMENT ON COLUMN SYS_OPER_LOG.OPER_LOCATION IS '操作地点';
COMMENT ON COLUMN SYS_OPER_LOG.OPER_PARAM IS '请求参数';
COMMENT ON COLUMN SYS_OPER_LOG.JSON_RESULT IS '返回参数';
COMMENT ON COLUMN SYS_OPER_LOG.STATUS IS '操作状态（0正常 1异常）建者';
COMMENT ON COLUMN SYS_OPER_LOG.ERROR_MSG IS '错误消息';
COMMENT ON COLUMN SYS_OPER_LOG.OPER_TIME IS '操作时间';
COMMENT ON TABLE SYS_OPER_LOG IS '操作日志记录';

CREATE TABLE SYS_POST (
  POST_ID DECIMAL(20,0) NOT NULL PRIMARY KEY ,
  POST_CODE VARCHAR2(64) NOT NULL ,
  POST_NAME VARCHAR2(50) NOT NULL ,
  POST_SORT DECIMAL(4,0) NOT NULL ,
  STATUS CHAR(1) NOT NULL ,
  CREATE_BY VARCHAR2(64) DEFAULT '' ,
  CREATE_TIME DATE DEFAULT sysdate NOT NULL ,
  UPDATE_BY VARCHAR(64) DEFAULT '' ,
  UPDATE_TIME DATE DEFAULT sysdate NOT NULL ,
  REMARK VARCHAR(500) DEFAULT NULL
);

COMMENT ON COLUMN SYS_POST.POST_ID IS '岗位主键';
COMMENT ON COLUMN SYS_POST.POST_CODE IS '岗位编码';
COMMENT ON COLUMN SYS_POST.POST_NAME IS '岗位名称';
COMMENT ON COLUMN SYS_POST.POST_SORT IS '显示顺序';
COMMENT ON COLUMN SYS_POST.STATUS IS '状态（0正常 1停用）';
COMMENT ON COLUMN SYS_POST.CREATE_BY IS '创建者';
COMMENT ON COLUMN SYS_POST.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN SYS_POST.UPDATE_BY IS '更新者';
COMMENT ON COLUMN SYS_POST.UPDATE_TIME IS '更新';
COMMENT ON COLUMN SYS_POST.REMARK IS '备注';
COMMENT ON TABLE SYS_POST IS '操作日志记录';

CREATE TABLE SYS_REGION (
  REGION_ID INT NOT NULL PRIMARY KEY ,
  PARENT_ID INT DEFAULT '0' ,
  IS_LEAF CHAR(1) DEFAULT NULL ,
  ANCESTORS VARCHAR2(50) DEFAULT NULL ,
  REGION_NAME VARCHAR2(30) DEFAULT NULL ,
  ORDER_NUM INT DEFAULT '0',
  LEADER VARCHAR2(20) DEFAULT NULL ,
  PHONE VARCHAR2(11) DEFAULT NULL ,
  EMAIL VARCHAR2(50) DEFAULT NULL ,
  STATUS CHAR(1) DEFAULT NULL ,
  DEL_FLAG CHAR(1) DEFAULT NULL ,
  CREATE_BY VARCHAR2(64) DEFAULT NULL ,
  CREATE_TIME DATE DEFAULT sysdate NOT NULL,
  UPDATE_BY VARCHAR2(64) DEFAULT NULL ,
  UPDATE_TIME DATE,
  SNO VARCHAR2(64) DEFAULT NULL ,
  PIN_YIN_1 VARCHAR2(20) DEFAULT NULL ,
  PIN_YIN_2 VARCHAR2(64) DEFAULT NULL
);

COMMENT ON COLUMN SYS_REGION.REGION_ID IS '主键';
COMMENT ON COLUMN SYS_REGION.PARENT_ID IS '父级区域ID';
COMMENT ON COLUMN SYS_REGION.IS_LEAF IS '状态（0非叶子结点, 1叶子结点）';
COMMENT ON COLUMN SYS_REGION.ANCESTORS IS '祖级列表';
COMMENT ON COLUMN SYS_REGION.REGION_NAME IS '区域名称';
COMMENT ON COLUMN SYS_REGION.ORDER_NUM IS '创建者';
COMMENT ON COLUMN SYS_REGION.LEADER IS '创建时间';
COMMENT ON COLUMN SYS_REGION.PHONE IS '更新者';
COMMENT ON COLUMN SYS_REGION.EMAIL IS '更新';
COMMENT ON COLUMN SYS_REGION.STATUS IS '备注';
COMMENT ON COLUMN SYS_REGION.DEL_FLAG IS '创建者';
COMMENT ON COLUMN SYS_REGION.CREATE_BY IS '创建时间';
COMMENT ON COLUMN SYS_REGION.CREATE_TIME IS '更新者';
COMMENT ON COLUMN SYS_REGION.UPDATE_BY IS '更新者';
COMMENT ON COLUMN SYS_REGION.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN SYS_REGION.SNO IS '组织代码';
COMMENT ON COLUMN SYS_REGION.PIN_YIN_1 IS '区域名称拼音各字首字母';
COMMENT ON COLUMN SYS_REGION.PIN_YIN_2 IS '区域名称拼音';
COMMENT ON TABLE SYS_REGION IS '省市区域信息表';


CREATE TABLE SYS_ROLE (
  ROLE_ID DECIMAL(20,0) NOT NULL PRIMARY KEY ,
  ROLE_NAME VARCHAR2(30) NOT NULL ,
  ROLE_KEY VARCHAR2(100) NOT NULL ,
  ROLE_SORT DECIMAL(4,0) NOT NULL ,
  DATA_SCOPE CHAR(1) DEFAULT '1' ,
  MENU_CHECK_STRICTLY DECIMAL(1,0) ,
  DEPT_CHECK_STRICTLY DECIMAL(1,0) ,
  STATUS CHAR(1) NOT NULL ,
  DEL_FLAG CHAR(1) DEFAULT '0' ,
  CREATE_BY VARCHAR2(64) DEFAULT '' ,
  CREATE_TIME DATE DEFAULT sysdate NOT NULL ,
  UPDATE_BY VARCHAR2(64) DEFAULT '' ,
  UPDATE_TIME DATE ,
  REMARK VARCHAR2(500) DEFAULT NULL ,
  PIN_YIN_1 VARCHAR2(20) DEFAULT NULL ,
  PIN_YIN_2 VARCHAR2(64) DEFAULT NULL
);

COMMENT ON COLUMN SYS_ROLE.ROLE_ID IS '角色主键';
COMMENT ON COLUMN SYS_ROLE.ROLE_NAME IS '角色名称';
COMMENT ON COLUMN SYS_ROLE.ROLE_KEY IS '角色权限字符串';
COMMENT ON COLUMN SYS_ROLE.ROLE_SORT IS '显示顺序';
COMMENT ON COLUMN SYS_ROLE.DATA_SCOPE IS '数据范围（1：全部数据权限 2：自定数据权限）';
COMMENT ON COLUMN SYS_ROLE.MENU_CHECK_STRICTLY IS '菜单树选择项是否关联显示';
COMMENT ON COLUMN SYS_ROLE.DEPT_CHECK_STRICTLY IS '部门树选择项是否关联显示';
COMMENT ON COLUMN SYS_ROLE.STATUS IS '角色状态（0正常 1停用）';
COMMENT ON COLUMN SYS_ROLE.DEL_FLAG IS '删除标志（0代表存在 2代表删除）';
COMMENT ON COLUMN SYS_ROLE.CREATE_BY IS '创建者';
COMMENT ON COLUMN SYS_ROLE.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN SYS_ROLE.UPDATE_BY IS '更新者';
COMMENT ON COLUMN SYS_ROLE.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN SYS_ROLE.REMARK IS '备注';
COMMENT ON COLUMN SYS_ROLE.PIN_YIN_1 IS '昵称拼音各字首字母';
COMMENT ON COLUMN SYS_ROLE.PIN_YIN_2 IS '昵称拼音';
COMMENT ON TABLE SYS_ROLE IS '角色信息表';


CREATE TABLE SYS_ROLE_DEPT (
  ROLE_ID DECIMAL(20,0) NOT NULL ,
  DEPT_ID DECIMAL(20,0) NOT NULL ,
  PRIMARY KEY (ROLE_ID, DEPT_ID)
);
COMMENT ON COLUMN SYS_ROLE_DEPT.ROLE_ID IS '角色ID';
COMMENT ON COLUMN SYS_ROLE_DEPT.ROLE_ID IS '部门ID';
COMMENT ON TABLE SYS_ROLE_DEPT IS '角色和部门关联表';

CREATE TABLE SYS_ROLE_MENU (
  ROLE_ID DECIMAL(20,0) NOT NULL ,
  MENU_ID DECIMAL(20,0) NOT NULL
);

COMMENT ON COLUMN SYS_ROLE_MENU.ROLE_ID IS '角色ID';
COMMENT ON COLUMN SYS_ROLE_MENU.MENU_ID IS '菜单ID';
COMMENT ON TABLE SYS_ROLE_MENU IS '角色和菜单关联表';


CREATE TABLE SYS_USER (
 USER_ID DECIMAL(20,0) NOT NULL PRIMARY KEY ,
  DEPT_ID DECIMAL(20,0) DEFAULT NULL ,
  USER_NAME VARCHAR2(30) NOT NULL ,
  NICK_NAME VARCHAR2(30) DEFAULT '',
  USER_TYPE VARCHAR2(2) DEFAULT '00' ,
  EMAIL VARCHAR2(50) DEFAULT '' ,
  PHONENUMBER VARCHAR2(11) DEFAULT '' ,
  SEX CHAR(1) DEFAULT '0' ,
  AVATAR VARCHAR2(100) DEFAULT '' ,
  PASSWORD VARCHAR2(100) DEFAULT '',
  STATUS CHAR(1) DEFAULT '0' ,
  DEL_FLAG CHAR(1) DEFAULT '0' ,
  LOGIN_IP VARCHAR2(128) DEFAULT '' ,
  LOGIN_DATE DATE DEFAULT sysdate NOT NULL ,
  CREATE_BY VARCHAR2(64) DEFAULT NULL ,
  CREATE_TIME DATE DEFAULT sysdate NOT NULL ,
  UPDATE_BY VARCHAR2(64) DEFAULT '' ,
  UPDATE_TIME DATE,
  REMARK VARCHAR2(500) DEFAULT '' ,
  FORCE_CHANGE_PWD VARCHAR2(1) DEFAULT '1' ,
  SALT VARCHAR2(16) DEFAULT NULL ,
  IDCARD VARCHAR2(20) DEFAULT NULL ,
  PIN_YIN_1 VARCHAR2(20) DEFAULT NULL ,
  PIN_YIN_2 VARCHAR2(64) DEFAULT NULL,
  TOKEN VARCHAR2(255) DEFAULT NULL ,
  OPEN_ID VARCHAR2(255) DEFAULT NULL
);

/*
  POINT DECIMAL(15,2) DEFAULT '0.00' NOT NULL  ,
  CONTACT_ADDRESS VARCHAR2(255) DEFAULT NULL ,
  PROVICE VARCHAR2(255) DEFAULT NULL ,
  AREA VARCHAR2(255) DEFAULT NULL ,
  COUNTY VARCHAR2(255) DEFAULT NULL ,
  IS_ENTERPRISE CHAR(2) DEFAULT NULL ,
  ENTERPRISE_NAME VARCHAR2(255) DEFAULT NULL ,
  CREDIT_CODE VARCHAR2(255) DEFAULT NULL ,
  BANK_NAME VARCHAR2(255) DEFAULT NULL ,
  BANK_CARD_NO VARCHAR2(255) DEFAULT NULL ,
  IDCARD_FACE VARCHAR2(255) DEFAULT NULL ,
  IDCARD_BACK VARCHAR2(255) DEFAULT NULL ,
  NAME_DEPOSIT_BANK VARCHAR2(255) DEFAULT NULL ,
 */
COMMENT ON COLUMN SYS_USER.USER_ID IS'用户主键';
COMMENT ON COLUMN SYS_USER.DEPT_ID IS'部门ID';
COMMENT ON COLUMN SYS_USER.USER_NAME IS'用户账号';
COMMENT ON COLUMN SYS_USER.NICK_NAME IS'用户昵称';
COMMENT ON COLUMN SYS_USER.USER_TYPE IS'用户类型（00系统用户 01注册用户）';
COMMENT ON COLUMN SYS_USER.EMAIL IS'用户邮箱';
COMMENT ON COLUMN SYS_USER.PHONENUMBER IS'手机号码';
COMMENT ON COLUMN SYS_USER.SEX IS'用户性别（0男 1女 2未知）';
COMMENT ON COLUMN SYS_USER.AVATAR IS'头像路径';
COMMENT ON COLUMN SYS_USER.PASSWORD IS '密码';
COMMENT ON COLUMN SYS_USER.STATUS IS'帐号状态（0正常 1停用）';
COMMENT ON COLUMN SYS_USER.DEL_FLAG IS'删除标志（0代表存在 2代表删除）';
COMMENT ON COLUMN SYS_USER.LOGIN_IP IS'最后登录IP';
COMMENT ON COLUMN SYS_USER.LOGIN_DATE IS'最后登录时间';
COMMENT ON COLUMN SYS_USER.CREATE_BY IS'创建者';
COMMENT ON COLUMN SYS_USER.CREATE_TIME IS'创建时间';
COMMENT ON COLUMN SYS_USER.UPDATE_BY IS'更新者';
COMMENT ON COLUMN SYS_USER.UPDATE_TIME IS'更新时间';
COMMENT ON COLUMN SYS_USER.REMARK IS'备注';
COMMENT ON COLUMN SYS_USER.FORCE_CHANGE_PWD IS'强制更改密码';
COMMENT ON COLUMN SYS_USER.SALT IS'密码加盐';
COMMENT ON COLUMN SYS_USER.IDCARD IS'身份证号';
COMMENT ON COLUMN SYS_USER.PIN_YIN_1 IS'昵称拼音各字首字母';
COMMENT ON COLUMN SYS_USER.PIN_YIN_2 IS'昵称拼音';
/*COMMENT ON COLUMN SYS_USER.POINT IS'当前积分数';
COMMENT ON COLUMN SYS_USER.CONTACT_ADDRESS IS'联系地址';
COMMENT ON COLUMN SYS_USER.PROVICE IS'省';
COMMENT ON COLUMN SYS_USER.AREA IS'市';
COMMENT ON COLUMN SYS_USER.COUNTY IS'县';
COMMENT ON COLUMN SYS_USER.IS_ENTERPRISE IS'是否是企业  1 企业  2非企业';
COMMENT ON COLUMN SYS_USER.ENTERPRISE_NAME IS'企业名称';
COMMENT ON COLUMN SYS_USER.CREDIT_CODE IS'统一社会信用代码';
COMMENT ON COLUMN SYS_USER.BANK_NAME IS'所属银行';
COMMENT ON COLUMN SYS_USER.BANK_CARD_NO IS'银行账号';
COMMENT ON COLUMN SYS_USER.IDCARD_FACE IS'身份证正面图片';
COMMENT ON COLUMN SYS_USER.IDCARD_BACK IS'身份证反面图片';
COMMENT ON COLUMN SYS_USER.NAME_DEPOSIT_BANK IS'开户行名称';*/
COMMENT ON COLUMN SYS_USER.TOKEN IS 'TOKEN';
COMMENT ON COLUMN SYS_USER.OPEN_ID IS 'OPENID';
COMMENT ON TABLE SYS_USER IS '用户信息表菜单关联表';

CREATE TABLE SYS_USER_ROLE (
 USER_ID DECIMAL(20,0) NOT NULL PRIMARY KEY ,
 ROLE_ID DECIMAL(20,0) NOT NULL
);

COMMENT ON COLUMN SYS_USER_ROLE.USER_ID IS'用户ID';
COMMENT ON COLUMN SYS_USER_ROLE.ROLE_ID IS'角色ID';
COMMENT ON TABLE SYS_USER_ROLE IS '用户和角色关联表';


CREATE TABLE SYSTEM_VAR (
  ID INT NOT NULL PRIMARY KEY,
  NAME VARCHAR2(255) DEFAULT NULL ,
  UPDATE_TIME DATE DEFAULT sysdate NOT NULL ,
  VALUE VARCHAR2(255) DEFAULT NULL ,
  VERSION INT DEFAULT NULL
);

COMMENT ON COLUMN SYSTEM_VAR.ID IS'主键';
COMMENT ON COLUMN SYSTEM_VAR.NAME IS'名称';
COMMENT ON COLUMN SYSTEM_VAR.UPDATE_TIME IS'最近更新时间';
COMMENT ON COLUMN SYSTEM_VAR.VALUE IS'当前值ID';
COMMENT ON COLUMN SYSTEM_VAR.VERSION IS'版本';
COMMENT ON TABLE  SYSTEM_VAR IS '应用系统变量表';

CREATE TABLE T_IMAGE (
  CODE  VARCHAR2(40) NOT NULL PRIMARY KEY,
  PATH  VARCHAR2(256) DEFAULT NULL ,
  CREATE_TIME DATE DEFAULT sysdate NOT NULL ,
  EXPIRED_TIME DATE
);

COMMENT ON COLUMN T_IMAGE.CODE IS'附件ID';
COMMENT ON COLUMN T_IMAGE.PATH IS'文件保存路径';
COMMENT ON COLUMN T_IMAGE.CREATE_TIME IS'创建时间';
COMMENT ON COLUMN T_IMAGE.EXPIRED_TIME IS'过期时间';
COMMENT ON TABLE T_IMAGE IS '附件正式表';

CREATE TABLE T_IMAGE_TMP (
  CODE  VARCHAR2(40) NOT NULL PRIMARY KEY,
  PATH  VARCHAR2(256) DEFAULT NULL ,
  USED_FLAG CHAR(1) DEFAULT '0' ,
  CREATE_TIME DATE DEFAULT sysdate NOT NULL ,
  EXPIRED_TIME DATE DEFAULT sysdate NOT NULL
);

COMMENT ON COLUMN T_IMAGE_TMP.CODE IS'附件ID';
COMMENT ON COLUMN T_IMAGE_TMP.PATH IS'文件保存路径';
COMMENT ON COLUMN T_IMAGE_TMP.USED_FLAG IS'0是未被使用；1是已被使用';
COMMENT ON COLUMN T_IMAGE_TMP.CREATE_TIME IS'创建时间';
COMMENT ON COLUMN T_IMAGE_TMP.EXPIRED_TIME IS'过期时间，如未被使用且过期后将会被物理删除即要删除文件';
COMMENT ON TABLE T_IMAGE_TMP IS '附件临时表';


CREATE TABLE T_POLICY_INSURANCE (
  ID INT NOT NULL PRIMARY KEY  ,
  NAME_INSURED VARCHAR2(255) DEFAULT NULL ,
  POLICY_HOLDER VARCHAR2(255) DEFAULT NULL ,
  POLICY_NUMBER VARCHAR2(255) DEFAULT NULL ,
  POLICY_NO VARCHAR2(255) DEFAULT NULL ,
  PREMIUM VARCHAR2(255) DEFAULT NULL ,
  INSURED_AMOUNT VARCHAR2(255) DEFAULT NULL ,
  PAYMENT_CODE VARCHAR2(255) DEFAULT NULL ,
  PAYMENT_STATUS VARCHAR2(255) DEFAULT '2' ,
  POLICY_STATUS VARCHAR2(255) DEFAULT NULL ,
  POLICY_START_TIME VARCHAR2(255) DEFAULT NULL ,
  POLICY_END_TIME VARCHAR2(255) DEFAULT NULL ,
  INSURANCE_ADDRESS VARCHAR2(255) DEFAULT NULL ,
  INSURED_PHONE VARCHAR2(255) DEFAULT NULL ,
  COMPANY_POLICY VARCHAR2(255) DEFAULT NULL ,
  RENEWAL_MARK VARCHAR2(255) DEFAULT NULL ,
  PICTURE VARCHAR2(1000) DEFAULT NULL ,
  NEXTNODE_USER_ID INT DEFAULT NULL,
  NEXTNODE_USER_NAME  VARCHAR2(200) DEFAULT NULL,
  NEXTNODE_NICK_NAME VARCHAR2(200) DEFAULT NULL,
  CREDATE_TIME DATE ,
  HOLDER_PHONE VARCHAR2(255) DEFAULT NULL,
  PRODUCT_ID INT DEFAULT NULL ,
  USER_ID INT DEFAULT NULL ,
  DEPT_ID INT DEFAULT NULL ,
  DEPT_NAME VARCHAR2(255) DEFAULT NULL ,
  SNO VARCHAR2(255) DEFAULT NULL ,
  POLICY_NO_BEFORE VARCHAR2(255) DEFAULT NULL ,
  PRODUCT_NAME VARCHAR2(255) DEFAULT NULL
);

COMMENT ON COLUMN T_POLICY_INSURANCE.ID IS'主键';
COMMENT ON COLUMN T_POLICY_INSURANCE.NAME_INSURED IS'被保险人名称';
COMMENT ON COLUMN T_POLICY_INSURANCE.POLICY_HOLDER IS'投保人';
COMMENT ON COLUMN T_POLICY_INSURANCE.POLICY_NUMBER IS'投保单号';
COMMENT ON COLUMN T_POLICY_INSURANCE.POLICY_NO IS'保单号';
COMMENT ON COLUMN T_POLICY_INSURANCE.PREMIUM IS'保费';
COMMENT ON COLUMN T_POLICY_INSURANCE.INSURED_AMOUNT IS'保额';
COMMENT ON COLUMN T_POLICY_INSURANCE.PAYMENT_CODE IS'缴费二维码';
COMMENT ON COLUMN T_POLICY_INSURANCE.PAYMENT_STATUS IS'缴费状态  1已缴费  2未交费';
COMMENT ON COLUMN T_POLICY_INSURANCE.POLICY_STATUS IS'保单状态(1:农户提交申请；2:农户申请待确认(专员);3:农户申请待处理(出单员);4:农户确认申请；5:待上传缴费二维码(出单员);6:待修改缴费状态(出单员);7:投保成功)';
COMMENT ON COLUMN T_POLICY_INSURANCE.POLICY_START_TIME IS'最新起保时间';
COMMENT ON COLUMN T_POLICY_INSURANCE.POLICY_END_TIME IS'最新终保时间';
COMMENT ON COLUMN T_POLICY_INSURANCE.INSURANCE_ADDRESS IS'保险地址';
COMMENT ON COLUMN T_POLICY_INSURANCE.INSURED_PHONE IS'联系电话(被保险人)';
COMMENT ON COLUMN T_POLICY_INSURANCE.COMPANY_POLICY IS'保单所属公司';
COMMENT ON COLUMN T_POLICY_INSURANCE.RENEWAL_MARK IS'续保标志 0 是  1否';
COMMENT ON COLUMN T_POLICY_INSURANCE.PICTURE IS'图片';
COMMENT ON COLUMN T_POLICY_INSURANCE.CREDATE_TIME IS'创建时间';
COMMENT ON COLUMN T_POLICY_INSURANCE.HOLDER_PHONE IS'联系电话(投保人)';
COMMENT ON COLUMN T_POLICY_INSURANCE.PRODUCT_ID IS'产品ID';
COMMENT ON COLUMN T_POLICY_INSURANCE.USER_ID IS'用户ID';
COMMENT ON COLUMN T_POLICY_INSURANCE.DEPT_ID IS'部门ID';
COMMENT ON COLUMN T_POLICY_INSURANCE.DEPT_NAME IS'部门名称';
COMMENT ON COLUMN T_POLICY_INSURANCE.SNO IS'组织代码';
COMMENT ON COLUMN T_POLICY_INSURANCE.POLICY_NO_BEFORE IS'上一年保单号时间';
COMMENT ON COLUMN T_POLICY_INSURANCE.PRODUCT_NAME IS'产品名称';
COMMENT ON COLUMN T_POLICY_INSURANCE.NEXTNODE_USER_ID IS'下一节点用户id';
COMMENT ON COLUMN T_POLICY_INSURANCE.NEXTNODE_USER_NAME IS'下一节点用户工号';
COMMENT ON COLUMN T_POLICY_INSURANCE.NEXTNODE_NICK_NAME IS'下一节点用户昵称';
COMMENT ON TABLE T_POLICY_INSURANCE IS '保单表';


CREATE TABLE T_POLICY_LOG (
 ID INT NOT NULL PRIMARY KEY ,
  POLICY_ID INT DEFAULT NULL ,
  NODE_NAME VARCHAR2(255) DEFAULT NULL ,
  HANDLER_NAME VARCHAR2(255) DEFAULT NULL ,
  HANDLER_ID INT DEFAULT NULL ,
  HANDLER_DEPT_ID INT DEFAULT NULL ,
  HANDLER_DEPT_NAME VARCHAR2(255) DEFAULT NULL ,
  HANDLER_TIME DATE DEFAULT sysdate NOT NULL,
  POLICY_STATUS VARCHAR2(255) DEFAULT NULL
);
COMMENT ON COLUMN T_POLICY_LOG.ID IS'主键';
COMMENT ON COLUMN T_POLICY_LOG.POLICY_ID IS'保单ID(T_POLICY_INSURANCE.ID)';
COMMENT ON COLUMN T_POLICY_LOG.NODE_NAME IS'节点名称';
COMMENT ON COLUMN T_POLICY_LOG.HANDLER_NAME IS'处理人名称';
COMMENT ON COLUMN T_POLICY_LOG.HANDLER_ID IS'处理人ID';
COMMENT ON COLUMN T_POLICY_LOG.HANDLER_DEPT_ID IS'处理人机构ID';
COMMENT ON COLUMN T_POLICY_LOG.HANDLER_DEPT_NAME IS'处理人机构名称';
COMMENT ON COLUMN T_POLICY_LOG.HANDLER_TIME IS'处理时间';
COMMENT ON COLUMN T_POLICY_LOG.POLICY_STATUS IS'节点状态';
COMMENT ON TABLE T_POLICY_LOG IS '保单流程流转记录';


--新增数据
-- 系统
INSERT INTO SYSTEM_VAR (ID,NAME,UPDATE_TIME,VALUE,VERSION)VALUES (-2, 'WebGuestVersion', to_date('2021-10-13 18:31:57','yyyy-mm-dd hh24:mi:ss'), '1.0.0', 0);
INSERT INTO SYSTEM_VAR (ID,NAME,UPDATE_TIME,VALUE,VERSION)VALUES (-1, 'WebAdminVersion',to_date('2021-10-13 18:31:57','yyyy-mm-dd hh24:mi:ss'), '1.0.0', 0);
INSERT INTO SYSTEM_VAR (ID,NAME,UPDATE_TIME,VALUE,VERSION)VALUES (2, 'DB Table Version', to_date('2021-10-13 18:31:57','yyyy-mm-dd hh24:mi:ss'), '1.0.4', 1);

-- 用户表
INSERT INTO SYS_USER (USER_ID,DEPT_ID,USER_NAME,NICK_NAME,USER_TYPE,EMAIL,PHONENUMBER,SEX,AVATAR,PASSWORD,STATUS,DEL_FLAG,LOGIN_IP,LOGIN_DATE,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK,FORCE_CHANGE_PWD,SALT,IDCARD,PIN_YIN_1,PIN_YIN_2,POINT,CONTACT_ADDRESS,PROVICE,AREA,COUNTY,IS_ENTERPRISE,ENTERPRISE_NAME,CREDIT_CODE,BANK_NAME,BANK_CARD_NO,IDCARD_FACE,IDCARD_BACK,NAME_DEPOSIT_BANK,TOKEN,OPEN_ID) VALUES (1, ********, 'admin', '互联网超管', '00', '<EMAIL>', '***********', '1', '', '$2a$10$pSS8NfmT5IgZuktS8Kz1q.DvzgOcdldbG4T8HDEusZA.uiUW1doZO', '0', '0', '127.0.0.1', to_date('2021-12-23 11:02:46','yyyy-mm-dd hh24:mi:ss'), 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'),'',to_date('2021-12-17 16:54:28','yyyy-mm-dd hh24:mi:ss'), '管理员', '1', NULL, '145252199702125623', '', '', 80.00, '浙江省绍兴市', '北京市', '东城区', '', '2', '1', '1', '工商银行', '*****************', '', '', '互联网超管', NULL, '');

-- 用户权限表
INSERT INTO SYS_USER_ROLE(USER_ID,ROLE_ID) VALUES (1, 1);

-- 角色菜单关联表
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID) VALUES (10, 1);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 2);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 3);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 5);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 6);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 100);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 101);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 102);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 103);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 105);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 106);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 107);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 108);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 109);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 110);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 111);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 112);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 113);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 114);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 115);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 116);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 500);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 501);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1001);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1002);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1003);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1004);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1005);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1006);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1007);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1008);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1009);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1010);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1011);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1012);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1013);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1014);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1015);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1016);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1017);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1018);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1019);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1020);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1026);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1027);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1028);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1029);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1030);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1031);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1032);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1033);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1034);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1035);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1036);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1037);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1038);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1039);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1040);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1041);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1042);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1043);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1044);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1045);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1046);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1047);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1048);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1049);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1050);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1051);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1052);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1053);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 1054);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 2080);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 2081);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 2091);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 2092);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 2093);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 2094);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 2095);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 2099);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 2101);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 2106);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 2107);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (10, 2108);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2048, 2052);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2048, 2053);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2048, 2054);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2048, 2071);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2048, 2072);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2048, 2073);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2048, 2074);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2048, 2075);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2048, 2076);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2048, 2077);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2048, 2078);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2048, 2079);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2048, 2087);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2048, 2088);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2048, 2089);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2048, 2090);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2049, 2048);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2049, 2049);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2049, 2050);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2049, 2055);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2049, 2056);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2049, 2057);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2049, 2058);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2049, 2059);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2049, 2060);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2049, 2061);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2049, 2062);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2049, 2063);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2049, 2065);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2049, 2070);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2049, 2082);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2049, 2085);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2050, 2048);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2050, 2049);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2050, 2050);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2050, 2055);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2050, 2056);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2050, 2057);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2050, 2058);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2050, 2059);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2050, 2060);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2050, 2061);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2050, 2062);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2050, 2063);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2050, 2064);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2050, 2065);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2050, 2066);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2050, 2067);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2050, 2068);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2050, 2069);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2050, 2070);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2050, 2082);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2050, 2083);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2050, 2084);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2050, 2085);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2051, 2048);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2051, 2049);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2051, 2050);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2051, 2055);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2051, 2056);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2051, 2057);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2051, 2058);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2051, 2059);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2051, 2060);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2051, 2061);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2051, 2062);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2051, 2063);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2051, 2065);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2051, 2070);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2051, 2082);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2051, 2085);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2052, 2048);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2052, 2049);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2052, 2050);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2052, 2055);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2052, 2056);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2052, 2057);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2052, 2058);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2052, 2059);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2052, 2060);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2052, 2061);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2052, 2062);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2052, 2063);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2052, 2065);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2052, 2070);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2052, 2082);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2052, 2085);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 2);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 3);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 5);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 6);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 100);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 101);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 102);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 103);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 105);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 106);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 107);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 108);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 109);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 110);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 111);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 112);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 113);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 114);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 115);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 116);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 500);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 501);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1001);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1002);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1003);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1004);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1005);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1006);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1007);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1008);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1009);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1010);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1011);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1012);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1013);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1014);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1015);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1016);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1017);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1018);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1019);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1020);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1026);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1027);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1028);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1029);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1030);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1031);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1032);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1033);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1034);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1035);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1036);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1037);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1038);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1039);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1040);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1041);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1042);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1043);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1044);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1045);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1046);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1047);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1048);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1049);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1050);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1051);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1052);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1053);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 1054);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 2080);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 2081);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 2091);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 2092);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 2093);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 2094);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 2095);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 2099);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 2101);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 2106);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 2107);
INSERT INTO SYS_ROLE_MENU (ROLE_ID,MENU_ID)VALUES (2055, 2108);


-- 角色菜单
INSERT INTO SYS_ROLE_DEPT(ROLE_ID,DEPT_ID) VALUES (2, 100);
INSERT INTO SYS_ROLE_DEPT (ROLE_ID,DEPT_ID)VALUES (2, 101);
INSERT INTO SYS_ROLE_DEPT (ROLE_ID,DEPT_ID)VALUES (2, 105);
INSERT INTO SYS_ROLE_DEPT (ROLE_ID,DEPT_ID)VALUES (2055, 100);
INSERT INTO SYS_ROLE_DEPT (ROLE_ID,DEPT_ID)VALUES (2055, 33060100);
INSERT INTO SYS_ROLE_DEPT (ROLE_ID,DEPT_ID)VALUES (2055, ********);
INSERT INTO SYS_ROLE_DEPT (ROLE_ID,DEPT_ID)VALUES (2055, 33060600);
INSERT INTO SYS_ROLE_DEPT (ROLE_ID,DEPT_ID)VALUES (2055, 33060700);
INSERT INTO SYS_ROLE_DEPT (ROLE_ID,DEPT_ID)VALUES (2055, 33060800);
INSERT INTO SYS_ROLE_DEPT (ROLE_ID,DEPT_ID)VALUES (2055, 33060900);


-- 角色表
INSERT INTO SYS_ROLE(ROLE_ID,ROLE_NAME,ROLE_KEY,ROLE_SORT,DATA_SCOPE,MENU_CHECK_STRICTLY,DEPT_CHECK_STRICTLY,STATUS,DEL_FLAG,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK,PIN_YIN_1,PIN_YIN_2) VALUES (1, '超级管理员', 'admin', 1, '1', 1, 1, '0', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '超级管理员', NULL, NULL);
INSERT INTO SYS_ROLE (ROLE_ID,ROLE_NAME,ROLE_KEY,ROLE_SORT,DATA_SCOPE,MENU_CHECK_STRICTLY,DEPT_CHECK_STRICTLY,STATUS,DEL_FLAG,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK,PIN_YIN_1,PIN_YIN_2)VALUES (2, '农户', 'farmer', 1, '1', 1, 1, '0', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', 'nh', 'nonghu');
INSERT INTO SYS_ROLE (ROLE_ID,ROLE_NAME,ROLE_KEY,ROLE_SORT,DATA_SCOPE,MENU_CHECK_STRICTLY,DEPT_CHECK_STRICTLY,STATUS,DEL_FLAG,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK,PIN_YIN_1,PIN_YIN_2)VALUES (10, '出单员', 'cwork', 0, '3', 1, 1, '0', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '专员', '', '');
INSERT INTO SYS_ROLE (ROLE_ID,ROLE_NAME,ROLE_KEY,ROLE_SORT,DATA_SCOPE,MENU_CHECK_STRICTLY,DEPT_CHECK_STRICTLY,STATUS,DEL_FLAG,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK,PIN_YIN_1,PIN_YIN_2)VALUES (2054, '政府人员', 'member', 0, '5', 1, 1, '0', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', 'zfry', 'zhengfurenyuan');
INSERT INTO SYS_ROLE (ROLE_ID,ROLE_NAME,ROLE_KEY,ROLE_SORT,DATA_SCOPE,MENU_CHECK_STRICTLY,DEPT_CHECK_STRICTLY,STATUS,DEL_FLAG,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK,PIN_YIN_1,PIN_YIN_2)VALUES (2055, '农险专员', 'manager', 0, '1', 1, 1, '0', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', 'nxzy', 'nongxianzhuanyuan');



-- 菜单表

INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1, '系统管理', 0, 2, '/system', '', 1, 0, 'M', '0', '0', '', 'system', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '系统管理目录');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (2, '系统监控', 0, 3, '/monitor', '', 1, 0, 'M', '1', '0', '', 'monitor', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '系统监控目录');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (3, '系统工具', 0, 4, '/tool', '', 1, 0, 'M', '1', '0', '', 'tool', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '系统工具目录');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (5, '案件管理', 0, 1, '/case', '', 1, 0, 'M', '0', '0', '', 'dashboard', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (6, '报案处理', 5, 1, 'index', 'case/index', 1, 0, 'C', '0', '0', NULL, 'dashboard', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (100, '用户管理', 1, 1, 'user', 'system/user/index', 1, 0, 'C', '0', '0', 'system:user:list', 'user', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '用户管理菜单');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (101, '角色管理', 1, 2, 'role', 'system/role/index', 1, 0, 'C', '0', '0', 'system:role:list', 'peoples', 'admin',to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '',to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '角色管理菜单');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (102, '菜单管理', 1, 3, 'menu', 'system/menu/index', 1, 0, 'C', '0', '0', 'system:menu:list', 'tree-table', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '菜单管理菜单');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (103, '机构管理', 1, 4, 'dept', 'system/dept/index', 1, 0, 'C', '0', '0', 'system:dept:list', 'tree', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '部门管理菜单');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (105, '字典管理', 1, 6, 'dict', 'system/dict/index', 1, 0, 'C', '0', '0', 'system:dict:list', 'dict', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '字典管理菜单');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (106, '参数设置', 1, 7, 'config', 'system/config/index', 1, 0, 'C', '0', '0', 'system:config:list', 'edit', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '参数设置菜单');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (107, '通知公告', 1, 8, 'notice', 'system/notice/index', 1, 0, 'C', '1', '0', 'system:notice:list', 'message', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '通知公告菜单');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (108, '日志管理', 1, 9, 'log', '', 1, 0, 'M', '0', '0', '', 'log', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '日志管理菜单');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (109, '在线用户', 2, 1, 'online', 'monitor/online/index', 1, 0, 'C', '0', '0', 'monitor:online:list', 'online', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '在线用户菜单');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (110, '定时任务', 2, 2, 'job', 'monitor/job/index', 1, 0, 'C', '0', '0', 'monitor:job:list', 'job', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '定时任务菜单');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (111, '数据监控', 2, 3, 'druid', 'monitor/druid/index', 1, 0, 'C', '0', '0', 'monitor:druid:list', 'druid', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '数据监控菜单');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (112, '服务监控', 2, 4, 'server', 'monitor/server/index', 1, 0, 'C', '0', '0', 'monitor:server:list', 'server', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '服务监控菜单');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (113, '缓存监控', 2, 5, 'cache', 'monitor/cache/index', 1, 0, 'C', '0', '0', 'monitor:cache:list', 'redis', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '缓存监控菜单');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (114, '表单构建', 3, 1, 'build', 'tool/build/index', 1, 0, 'C', '0', '0', 'tool:build:list', 'build', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '表单构建菜单');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (115, '代码生成', 3, 2, 'gen', 'tool/gen/index', 1, 0, 'C', '0', '0', 'tool:gen:list', 'code', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '代码生成菜单');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (116, '系统接口', 3, 3, 'swagger', 'tool/swagger/index', 1, 0, 'C', '0', '0', 'tool:swagger:list', 'swagger', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '系统接口菜单');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (500, '操作日志', 108, 1, 'operlog', 'monitor/operlog/index', 1, 0, 'C', '0', '0', 'monitor:operlog:list', 'form', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '操作日志菜单');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (501, '登录日志', 108, 2, 'logininfor', 'monitor/logininfor/index', 1, 0, 'C', '0', '0', 'monitor:logininfor:list', 'logininfor', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '',to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '登录日志菜单');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1001, '用户查询', 100, 1, '', '', 1, 0, 'F', '0', '0', 'system:user:query', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1002, '用户新增', 100, 2, '', '', 1, 0, 'F', '0', '0', 'system:user:add', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1003, '用户修改', 100, 3, '', '', 1, 0, 'F', '0', '0', 'system:user:edit', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1004, '用户删除', 100, 4, '', '', 1, 0, 'F', '0', '0', 'system:user:remove', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1005, '用户导出', 100, 5, '', '', 1, 0, 'F', '0', '0', 'system:user:export', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1006, '用户导入', 100, 6, '', '', 1, 0, 'F', '0', '0', 'system:user:import', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1007, '重置密码', 100, 7, '', '', 1, 0, 'F', '0', '0', 'system:user:resetPwd', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '',to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1008, '角色查询', 101, 1, '', '', 1, 0, 'F', '0', '0', 'system:role:query', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1009, '角色新增', 101, 2, '', '', 1, 0, 'F', '0', '0', 'system:role:add', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1010, '角色修改', 101, 3, '', '', 1, 0, 'F', '0', '0', 'system:role:edit', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1011, '角色删除', 101, 4, '', '', 1, 0, 'F', '0', '0', 'system:role:remove', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1012, '角色导出', 101, 5, '', '', 1, 0, 'F', '0', '0', 'system:role:export', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1013, '菜单查询', 102, 1, '', '', 1, 0, 'F', '0', '0', 'system:menu:query', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1014, '菜单新增', 102, 2, '', '', 1, 0, 'F', '0', '0', 'system:menu:add', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '',to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1015, '菜单修改', 102, 3, '', '', 1, 0, 'F', '0', '0', 'system:menu:edit', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1016, '菜单删除', 102, 4, '', '', 1, 0, 'F', '0', '0', 'system:menu:remove', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1017, '机构查询', 103, 1, '', '', 1, 0, 'F', '0', '0', 'system:dept:query', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1018, '机构新增', 103, 2, '', '', 1, 0, 'F', '0', '0', 'system:dept:add', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1019, '机构修改', 103, 3, '', '', 1, 0, 'F', '0', '0', 'system:dept:edit', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1020, '机构删除', 103, 4, '', '', 1, 0, 'F', '0', '0', 'system:dept:remove', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1026, '字典查询', 105, 1, '#', '', 1, 0, 'F', '0', '0', 'system:dict:query', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1027, '字典新增', 105, 2, '#', '', 1, 0, 'F', '0', '0', 'system:dict:add', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1028, '字典修改', 105, 3, '#', '', 1, 0, 'F', '0', '0', 'system:dict:edit', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1029, '字典删除', 105, 4, '#', '', 1, 0, 'F', '0', '0', 'system:dict:remove', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1030, '字典导出', 105, 5, '#', '', 1, 0, 'F', '0', '0', 'system:dict:export', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1031, '参数查询', 106, 1, '#', '', 1, 0, 'F', '0', '0', 'system:config:query', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1032, '参数新增', 106, 2, '#', '', 1, 0, 'F', '0', '0', 'system:config:add', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1033, '参数修改', 106, 3, '#', '', 1, 0, 'F', '0', '0', 'system:config:edit', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1034, '参数删除', 106, 4, '#', '', 1, 0, 'F', '0', '0', 'system:config:remove', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1035, '参数导出', 106, 5, '#', '', 1, 0, 'F', '0', '0', 'system:config:export', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1036, '公告查询', 107, 1, '#', '', 1, 0, 'F', '0', '0', 'system:notice:query', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1037, '公告新增', 107, 2, '#', '', 1, 0, 'F', '0', '0', 'system:notice:add', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1038, '公告修改', 107, 3, '#', '', 1, 0, 'F', '0', '0', 'system:notice:edit', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1039, '公告删除', 107, 4, '#', '', 1, 0, 'F', '0', '0', 'system:notice:remove', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1040, '操作查询', 500, 1, '#', '', 1, 0, 'F', '0', '0', 'monitor:operlog:query', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1041, '操作删除', 500, 2, '#', '', 1, 0, 'F', '0', '0', 'monitor:operlog:remove', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1042, '日志导出', 500, 4, '#', '', 1, 0, 'F', '0', '0', 'monitor:operlog:export', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1043, '登录查询', 501, 1, '#', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:query', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1044, '登录删除', 501, 2, '#', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:remove', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1045, '日志导出', 501, 3, '#', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:export', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1046, '在线查询', 109, 1, '#', '', 1, 0, 'F', '0', '0', 'monitor:online:query', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1047, '批量强退', 109, 2, '#', '', 1, 0, 'F', '0', '0', 'monitor:online:batchLogout', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1048, '单条强退', 109, 3, '#', '', 1, 0, 'F', '0', '0', 'monitor:online:forceLogout', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1049, '任务查询', 110, 1, '#', '', 1, 0, 'F', '0', '0', 'monitor:job:query', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1050, '任务新增', 110, 2, '#', '', 1, 0, 'F', '0', '0', 'monitor:job:add', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1051, '任务修改', 110, 3, '#', '', 1, 0, 'F', '0', '0', 'monitor:job:edit', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1052, '任务删除', 110, 4, '#', '', 1, 0, 'F', '0', '0', 'monitor:job:remove', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1053, '状态修改', 110, 5, '#', '', 1, 0, 'F', '0', '0', 'monitor:job:changeStatus', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (1054, '任务导出', 110, 7, '#', '', 1, 0, 'F', '0', '0', 'monitor:job:export', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (2080, '用户打卡', 100, 8, '', NULL, 1, 0, 'F', '0', '0', 'system:user:clockin', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (2081, '查看打卡', 100, 9, '', NULL, 1, 0, 'F', '0', '0', 'system:user:isClockin', '#', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (2091, '活动列表', 2092, 1, 'activity', 'activity/index', 1, 0, 'C', '0', '0', '', 'list', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (2092, '活动管理', 0, 1, '/actiity', '', 1, 0, 'M', '0', '0', '', 'cascader', 'admin',to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (2093, '商品管理', 2092, 2, 'commodity', 'commodity/index', 1, 0, 'C', '0', '0', NULL, 'list', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (2094, '兑换记录', 2092, 3, 'record', 'exchangeRecord/index', 1, 0, 'C', '0', '0', NULL, 'list', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '',to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (2095, '积分汇总', 2092, 4, 'summary', 'summary/index', 1, 0, 'C', '0', '0', NULL, 'excel', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (2099, '投保/续保管理', 0, 1, '/policy', '', 1, 0, 'M', '0', '0', '', 'dashboard', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (2101, '投保管理', 2099, 2, 'index', 'policy/index', 1, 0, 'C', '0', '0', '', 'button', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (2106, '公示政策', 0, 1, '/notice', '', 1, 0, 'M', '0', '0', '', 'dashboard', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (2107, '公示分布', 2106, 1, 'publish', 'notice/publish', 1, 0, 'C', '0', '0', '', 'tree', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');
INSERT INTO SYS_MENU (MENU_ID,MENU_NAME,PARENT_ID,ORDER_NUM,PATH,COMPONENT,IS_FRAME,IS_CACHE,MENU_TYPE,VISIBLE,STATUS,PERMS,ICON,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (2108, '政策咨询', 2106, 2, 'inquire', 'notice/inquire', 1, 0, 'C', '0', '0', NULL, 'tree', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '');


--字典
INSERT INTO SYS_DICT_TYPE(DICT_ID,DICT_NAME,DICT_TYPE,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK,TYPE_KIND) VALUES (1, '用户性别', 'sys_user_sex', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '用户性别列表', 1);
INSERT INTO SYS_DICT_TYPE (DICT_ID,DICT_NAME,DICT_TYPE,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK,TYPE_KIND)VALUES (2, '菜单状态', 'sys_show_hide', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '菜单状态列表', 1);
INSERT INTO SYS_DICT_TYPE (DICT_ID,DICT_NAME,DICT_TYPE,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK,TYPE_KIND)VALUES (3, '系统开关', 'sys_normal_disable', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '系统开关列表', 1);
INSERT INTO SYS_DICT_TYPE (DICT_ID,DICT_NAME,DICT_TYPE,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK,TYPE_KIND)VALUES (4, '任务状态', 'sys_job_status', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '任务状态列表', 1);
INSERT INTO SYS_DICT_TYPE (DICT_ID,DICT_NAME,DICT_TYPE,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK,TYPE_KIND)VALUES (5, '任务分组', 'sys_job_group', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '任务分组列表', 1);
INSERT INTO SYS_DICT_TYPE (DICT_ID,DICT_NAME,DICT_TYPE,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK,TYPE_KIND)VALUES (6, '系统是否', 'sys_yes_no', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '系统是否列表', 1);
INSERT INTO SYS_DICT_TYPE (DICT_ID,DICT_NAME,DICT_TYPE,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK,TYPE_KIND)VALUES (7, '通知类型', 'sys_notice_type', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '通知类型列表', 1);
INSERT INTO SYS_DICT_TYPE (DICT_ID,DICT_NAME,DICT_TYPE,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK,TYPE_KIND)VALUES (8, '通知状态', 'sys_notice_status', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '通知状态列表', 1);
INSERT INTO SYS_DICT_TYPE (DICT_ID,DICT_NAME,DICT_TYPE,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK,TYPE_KIND)VALUES (9, '操作类型', 'sys_oper_type', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '操作类型列表', 1);
INSERT INTO SYS_DICT_TYPE (DICT_ID,DICT_NAME,DICT_TYPE,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK,TYPE_KIND)VALUES (10, '系统状态', 'sys_common_status', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '登录状态列表', 1);

--字典
INSERT INTO SYS_DICT_DATA(DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK) VALUES (1, 1, '男', '0', 'sys_user_sex', '', '', 'Y', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '性别男');
INSERT INTO SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (2, 2, '女', '1', 'sys_user_sex', '', '', 'N', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '性别女');
INSERT INTO SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (3, 3, '未知', '2', 'sys_user_sex', '', '', 'N', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '性别未知');
INSERT INTO SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (4, 1, '显示', '0', 'sys_show_hide', '', 'primary', 'Y', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '显示菜单');
INSERT INTO SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (5, 2, '隐藏', '1', 'sys_show_hide', '', 'danger', 'N', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '隐藏菜单');
INSERT INTO SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (6, 1, '正常', '0', 'sys_normal_disable', '', 'primary', 'Y', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '正常状态');
INSERT INTO SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (7, 2, '停用', '1', 'sys_normal_disable', '', 'danger', 'N', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '停用状态');
INSERT INTO SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (8, 1, '正常', '0', 'sys_job_status', '', 'primary', 'Y', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '正常状态');
INSERT INTO SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (9, 2, '暂停', '1', 'sys_job_status', '', 'danger', 'N', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '停用状态');
INSERT INTO SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (10, 1, '默认', 'DEFAULT', 'sys_job_group', '', '', 'Y', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '默认分组');
INSERT INTO SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (11, 2, '系统', 'SYSTEM', 'sys_job_group', '', '', 'N', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '系统分组');
INSERT INTO SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (12, 1, '是', 'Y', 'sys_yes_no', '', 'primary', 'Y', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '系统默认是');
INSERT INTO SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (13, 2, '否', 'N', 'sys_yes_no', '', 'danger', 'N', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '系统默认否');
INSERT INTO SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (14, 1, '通知', '1', 'sys_notice_type', '', 'warning', 'Y', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '通知');
INSERT INTO SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (15, 2, '公告', '2', 'sys_notice_type', '', 'success', 'N', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '公告');
INSERT INTO SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (16, 1, '正常', '0', 'sys_notice_status', '', 'primary', 'Y', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '正常状态');
INSERT INTO SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (17, 2, '关闭', '1', 'sys_notice_status', '', 'danger', 'N', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '关闭状态');
INSERT INTO SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (18, 99, '其他', '0', 'sys_oper_type', '', 'info', 'N', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '其他操作');
INSERT INTO SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (19, 1, '新增', '1', 'sys_oper_type', '', 'info', 'N', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '新增操作');
INSERT INTO SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (20, 2, '修改', '2', 'sys_oper_type', '', 'info', 'N', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '修改操作');
INSERT INTO SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (21, 3, '删除', '3', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '删除操作');
INSERT INTO SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (22, 4, '授权', '4', 'sys_oper_type', '', 'primary', 'N', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '授权操作');
INSERT INTO SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (23, 5, '导出', '5', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '导出操作');
INSERT INTO SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (24, 6, '导入', '6', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '',to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '导入操作');
INSERT INTO SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (25, 7, '强退', '7', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '强退操作');
INSERT INTO SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (26, 8, '生成代码', '8', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '生成操作');
INSERT INTO SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (27, 9, '清空数据', '9', 'sys_oper_type', '', 'danger', 'N', '0', 'admin',to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '清空操作');
INSERT INTO SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (28, 1, '成功', '0', 'sys_common_status', '', 'primary', 'N', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '正常状态');
INSERT INTO SYS_DICT_DATA (DICT_CODE,DICT_SORT,DICT_LABEL,DICT_VALUE,DICT_TYPE,CSS_CLASS,LIST_CLASS,IS_DEFAULT,STATUS,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,REMARK)VALUES (29, 2, '失败', '1', 'sys_common_status', '', 'danger', 'N', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '停用状态');

--
INSERT INTO SYS_DEPT(DEPT_ID,PARENT_ID,ANCESTORS,DEPT_NAME,ORDER_NUM,LEADER,PHONE,EMAIL,STATUS,DEL_FLAG,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,SNO,PIN_YIN_1,PIN_YIN_2)VALUES (33060100, 100, '0,100', '柯桥区', 4, '', '', '', '0', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '13516826556', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '330104', 'zhb', 'zonghebu');
INSERT INTO SYS_DEPT(DEPT_ID,PARENT_ID,ANCESTORS,DEPT_NAME,ORDER_NUM,LEADER,PHONE,EMAIL,STATUS,DEL_FLAG,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,SNO,PIN_YIN_1,PIN_YIN_2)VALUES (********, 100, '0,100', '越城区', 1, '', '', '', '0', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '330101', 'zjls', 'zongjinglishi');
INSERT INTO SYS_DEPT(DEPT_ID,PARENT_ID,ANCESTORS,DEPT_NAME,ORDER_NUM,LEADER,PHONE,EMAIL,STATUS,DEL_FLAG,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,SNO,PIN_YIN_1,PIN_YIN_2)VALUES (33060600, 100, '0,100', '诸暨市', 3, NULL, NULL, NULL, '0', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '330203', 'sweb', 'shangwuerbu');
INSERT INTO SYS_DEPT(DEPT_ID,PARENT_ID,ANCESTORS,DEPT_NAME,ORDER_NUM,LEADER,PHONE,EMAIL,STATUS,DEL_FLAG,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,SNO,PIN_YIN_1,PIN_YIN_2)VALUES (33060700, 100, '0,100', '上虞区', 2, NULL, NULL, NULL, '0', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '',to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '330102', 'swyb', 'shangwuyibu');
INSERT INTO SYS_DEPT(DEPT_ID,PARENT_ID,ANCESTORS,DEPT_NAME,ORDER_NUM,LEADER,PHONE,EMAIL,STATUS,DEL_FLAG,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,SNO,PIN_YIN_1,PIN_YIN_2)VALUES (33060800, 100, '0,100', '嵊州市', 6, NULL, NULL, NULL, '0', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '330106', 'lpzx', 'lipeizhongxin');
INSERT INTO SYS_DEPT(DEPT_ID,PARENT_ID,ANCESTORS,DEPT_NAME,ORDER_NUM,LEADER,PHONE,EMAIL,STATUS,DEL_FLAG,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,SNO,PIN_YIN_1,PIN_YIN_2)VALUES (33060900, 100, '0,100', '新昌县', 5, NULL, NULL, NULL, '0', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '330105', 'kfyyb', 'kefuyunyingbu');
INSERT INTO SYS_DEPT(DEPT_ID,PARENT_ID,ANCESTORS,DEPT_NAME,ORDER_NUM,LEADER,PHONE,EMAIL,STATUS,DEL_FLAG,CREATE_BY,CREATE_TIME,UPDATE_BY,UPDATE_TIME,SNO,PIN_YIN_1,PIN_YIN_2)VALUES (100, 0, '0', '绍兴市分公司', 0, '蛟驰', '15888888888', '<EMAIL>', '0', '0', 'admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '13516826556', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '00', NULL, NULL);

--
INSERT INTO SYS_CONFIG (CONFIG_ID,CONFIG_NAME,CONFIG_KEY,CONFIG_VALUE,CONFIG_TYPE,CREATE_BY,UPDATE_BY,CREATE_TIME,UPDATE_TIME,REMARK) VALUES (1, '主框架页-默认皮肤样式名称', 'sys.index.skinName', 'skin-blue', 'Y', 'admin', '', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow');
INSERT INTO SYS_CONFIG (CONFIG_ID,CONFIG_NAME,CONFIG_KEY,CONFIG_VALUE,CONFIG_TYPE,CREATE_BY,UPDATE_BY,CREATE_TIME,UPDATE_TIME,REMARK)VALUES (2, '用户管理-账号初始密码', 'sys.user.initPassword', 'Superp1cc', 'Y', 'admin','admin', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'),  to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '初始化');
INSERT INTO SYS_CONFIG (CONFIG_ID,CONFIG_NAME,CONFIG_KEY,CONFIG_VALUE,CONFIG_TYPE,CREATE_BY,UPDATE_BY,CREATE_TIME,UPDATE_TIME,REMARK)VALUES (3, '主框架页-侧边栏主题', 'sys.index.sideTheme', 'theme-dark', 'Y', 'admin','', to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '深黑主题theme-dark，浅色主题theme-light，深蓝主题theme-blue');
INSERT INTO SYS_CONFIG (CONFIG_ID,CONFIG_NAME,CONFIG_KEY,CONFIG_VALUE,CONFIG_TYPE,CREATE_BY,UPDATE_BY,CREATE_TIME,UPDATE_TIME,REMARK)VALUES (4, '账号自助-是否开启用户注册功能', 'sys.account.registerUser', 'false', 'Y', 'admin','',  to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), to_date('2021-10-13 18:31:48','yyyy-mm-dd hh24:mi:ss'), '是否开启注册用户功能');

-- 12/24 （上面已经有了）
-- ALTER TABLE T_POLICY_INSURANCE ADD ("NEXTNODE_USER_ID" int DEFAULT NULL);
-- ALTER TABLE T_POLICY_INSURANCE ADD ("NEXTNODE_USER_NAME" VARCHAR2(200) DEFAULT NULL);
-- alter table T_IMAGE_TMP rename column  EXPIRED_TIMED_FLAG to EXPIRED_TIME;
-- alter table SYS_LOGININFOR modify (OS VARCHAR2(50));




