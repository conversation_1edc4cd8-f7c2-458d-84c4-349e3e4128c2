create table max_id (
 id_type varchar(64) not null primary key comment '用户工号',
 max_value int not null comment '姓名',
 expiry_date timestamp comment '密码'
) comment = '主键自增辅助表，记录各表主键当前最大值';

create table sys_config (
 config_id decimal(20,0) not null primary key comment '参数主键',
 config_name varchar(100) default '' comment '参数名称',
 config_key varchar(100) default '' comment '参数键名',
 config_value varchar(100) default '' comment '参数键值',
 config_type varchar(1) default 'n' comment '系统内置（y是 n否）',
 create_by varchar(64) default '' comment '创建者',
 update_by varchar(64) comment '更新者',
 create_time timestamp default now() not null comment '创建时间',
 update_time timestamp default now() not null comment '更新时间',
 remark varchar(500) comment '备注'
) comment = '参数配置表';

create table sys_dept (
 dept_id decimal(20,0) not null primary key comment '部门主键',
 parent_id decimal(20,0) default '0' comment '父部门id',
 ancestors varchar(50) default '' comment '祖级列表',
 dept_name varchar(30) default '' comment '部门名称',
 order_num decimal(4,0) default '0' comment '显示顺序',
 leader varchar(20) default null comment '负责人',
 phone varchar(11) default null comment '联系电话',
 email varchar(50) default null comment '邮箱',
 status varchar(1) default '0' comment '部门状态（0正常 1停用）',
 del_flag varchar(1) default '0' comment '删除标志（0代表存在 2代表删除）',
 create_by varchar(64) default '' comment '创建者',
 create_time timestamp default now() not null comment '创建时间',
 update_by varchar(64) default '' comment '更新者',
 update_time timestamp default now() not null comment '更新时间',
 sno varchar(64) default null comment '组织代码',
 pin_yin_1 varchar(20) default null comment '昵称拼音各字首字母',
 pin_yin_2 varchar(64) default null comment '昵称拼音'
 ) comment = '部门信息表';

create table sys_dict_data (
 dict_code decimal(20,0) not null primary key comment '字典主键',
 dict_sort decimal(4,0) default '0' comment '字典排序',
 dict_label varchar(100) default '' comment '字典标签',
 dict_value varchar(100) default '' comment '字典键值',
 dict_type varchar(100) default '0' comment '字典类型',
 css_class varchar(100) default null comment '样式属性（其他样式扩展）',
 list_class varchar(100) default null comment '表格回显样式',
 is_default varchar(1) default 'n' comment '是否默认（y是 n否）',
 status varchar(1) default '0' comment '部门状态（0正常 1停用）',
 create_by varchar(64) default '' comment '创建者',
 create_time timestamp default now() not null comment '创建时间',
 update_by varchar(500) default null comment '更新者',
 update_time timestamp default now() not null comment '更新时间',
 remark varchar(500) default null comment '备注'
 ) comment = '字典数据表';

create table sys_dict_type (
 dict_id decimal(20,0) not null primary key comment '字典主键',
 dict_name varchar(100) default '' comment '字典名称',
 dict_type varchar(100) default '' comment '字典类型',
 status varchar(1) default '0' comment '状态（0正常 1停用）',
 create_by varchar(64) default '' comment '创建者',
 create_time timestamp default now() not null comment '创建时间',
 update_by varchar(64) default '' comment '更新者',
 update_time timestamp default now() not null comment '更新时间',
 remark varchar(500) default null comment '备注',
 type_kind decimal(8,0) default '1' not null comment '1是系统数据字典；2是应用数据字典'
 ) comment = '字典类型表';


create table sys_job (
 job_id decimal(20,0) not null primary key comment '任务主键',
 job_name varchar(64) default '' not null comment '任务名称',
 job_group varchar(64) default '' not null comment '任务组名',
 invoke_target varchar(500) not null comment '调用目标字符串',
 cron_expressionate_by varchar(255) default '' comment 'cron执行表达式',
 misfire_policy varchar(20) default '3' comment '计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
 concurrent varchar(1) default '1' ,
 status varchar(1) default '0' comment '状态（0正常 1暂停）',
 create_by varchar(64) default '' comment '创建者',
 create_time timestamp default now() not null comment '创建时间',
 update_by varchar(64) default '' comment '更新者',
 update_time timestamp default now() not null comment '更新时间',
 remark varchar(500) default '' comment '备注信息'
 ) comment = '定时任务调度表';


create table sys_job_log (
 job_log_id decimal(20,0) not null primary key comment '日志主键',
 job_name varchar(64) not null comment '任务名称',
 job_group varchar(64) not null comment '任务组名',
 invoke_target varchar(500) not null comment '调用目标字符串',
 job_message varchar(500) default null comment '日志信息',
 status varchar(1) default '0' comment '执行状态（0正常 1失败）',
 exception_info varchar(2000) comment '异常信息',
 create_time timestamp default now() not null comment '创建时间'
 ) comment = '定时任务调度日志表';

create table sys_logininfor (
 info_id decimal(20,0) not null primary key comment '访问主键',
 user_name varchar(50) default '' comment '登录账号',
 ipaddr varchar(64) default '' comment '登录ip地址',
 login_location varchar(255) default '' comment '登录地点',
 browser varchar(250) default '' comment '浏览器类型',
 os varchar(128) default '' comment '操作系统',
 status varchar(1) default '0' comment '登录状态（0成功 1失败）',
 msg varchar(255) default '' comment '提示消息',
 login_time timestamp default now() not null comment '访问时间'
 ) comment = '系统访问记录';

create table sys_menu (
 menu_id decimal(20,0) not null primary key comment '菜单主键',
 menu_name varchar(50) not null comment '菜单名称',
 parent_id decimal(20,0) default '0' comment '父菜单id',
 order_num decimal(4,0) default '0' comment '显示顺序',
 path varchar(200) default '' comment '请求地址',
 component varchar(255) default null comment '路由地址',
 is_frame decimal(1,0) default '1' comment '是否为外链（0是 1否）',
 is_cache decimal(1,0) default '0' comment '是否缓存（0缓存 1不缓存）',
 menu_type varchar(1) default '' comment '菜单类型（m目录 c菜单 f按钮）',
 visible varchar(1) default '0' comment '菜单状态（0显示 1隐藏）',
 status varchar(1) default '0' comment '菜单状态（0正常 1停用）',
 perms varchar(100) default null comment '权限标识',
 icon varchar(100) default '#' comment '菜单图标',
 create_by varchar(64) default '' comment '创建者',
 create_time timestamp default now() not null comment '创建时间',
 update_by varchar(64) default '' comment '更新者',
 update_time timestamp default now() not null comment '更新时间',
 remark varchar(500) default '' comment '备注'
) comment = '菜单权限表';

drop table if exists sys_oper_log;
create table sys_oper_log (
  oper_id           numeric(20)    not null primary key comment '日志主键',
  title             varchar(50)    default '' comment '模块标题',
  business_type     numeric(2)     default 0 comment '业务类型（0其它 1新增 2修改 3删除）',
  method            varchar(100)   default '' comment '方法名称',
  request_method    varchar(10)    default '' comment '请求方式',
  operator_type     numeric(1)     default 0 comment '操作类别（0其它 1后台用户 2手机端用户）',
  oper_name         varchar(50)    default '' comment '操作人员',
  dept_name         varchar(50)    default '' comment '部门名称',
  oper_url          varchar(255) 	 default '' comment '请求URL',
  oper_ip           varchar(128)   default '' comment '主机地址',
  oper_location     varchar(255)   default '' comment '操作地点',
  oper_param        text  comment '请求参数',
  json_result       text  comment '返回参数',
  status            numeric(1)     default 0 comment '操作状态（0正常 1异常）',
  error_msg         text  comment '错误消息',
  oper_time         timestamp comment '操作时间'
) comment '操作日志记录';
alter table sys_oper_log add index (oper_time);

create table sys_post (
 post_id decimal(20,0) not null primary key comment '岗位主键',
 post_code varchar(64) not null comment '岗位编码',
 post_name varchar(50) not null comment '岗位名称',
 post_sort decimal(4,0) not null comment '显示顺序',
 status char(1) not null comment '状态（0正常 1停用）',
 create_by varchar(64) default '' comment '创建者',
 create_time timestamp default now() not null comment '创建时间',
 update_by varchar(64) default '' comment '更新者',
 update_time timestamp default now() not null comment '更新',
 remark varchar(500) default null comment '备注'
) comment = '操作日志记录';

create table sys_region (
 region_id int not null primary key comment '主键',
 parent_id int default '0' comment '父级区域id',
 is_leaf char(1) default null comment '状态（0非叶子结点, 1叶子结点）',
 ancestors varchar(50) default null comment '祖级列表',
 region_name varchar(30) default null comment '区域名称',
 order_num int default '0' comment '创建者',
 leader varchar(20) default null comment '创建时间',
 phone varchar(11) default null comment '更新者',
 email varchar(50) default null comment '更新',
 status char(1) default null comment '备注',
 del_flag char(1) default null comment '创建者',
 create_by varchar(64) default null comment '创建时间',
 create_time timestamp default now() not null comment '更新者',
 update_by varchar(64) default null comment '更新者',
 update_time timestamp default now() comment '更新时间',
 sno varchar(64) default null comment '组织代码',
 pin_yin_1 varchar(20) default null comment '区域名称拼音各字首字母',
 pin_yin_2 varchar(64) default null comment '区域名称拼音'
) comment = '省市区域信息表';


create table sys_role (
 role_id decimal(20,0) not null primary key comment '角色主键',
 role_name varchar(30) not null comment '角色名称',
 role_key varchar(100) not null comment '角色权限字符串',
 role_sort decimal(4,0) not null comment '显示顺序',
 data_scope char(1) default '1' comment '数据范围（1：全部数据权限 2：自定数据权限）',
 menu_check_strictly decimal(1,0) comment '菜单树选择项是否关联显示',
 dept_check_strictly decimal(1,0) comment '部门树选择项是否关联显示',
 status char(1) not null comment '角色状态（0正常 1停用）',
 del_flag char(1) default '0' comment '删除标志（0代表存在 2代表删除）',
 create_by varchar(64) default '' comment '创建者',
 create_time timestamp default now() not null comment '创建时间',
 update_by varchar(64) default '' comment '更新者',
 update_time timestamp default now() comment '更新时间',
 remark varchar(500) default null comment '备注',
 pin_yin_1 varchar(20) default null comment '昵称拼音各字首字母',
 pin_yin_2 varchar(64) default null comment '昵称拼音'
) comment = '角色信息表';


create table sys_role_dept (
 role_id decimal(20,0) not null comment '角色id',
 dept_id decimal(20,0) not null comment '部门id',
 primary key (role_id, dept_id)
) comment = '角色和部门关联表';

create table sys_role_menu (
 role_id decimal(20,0) not null comment '角色id',
 menu_id decimal(20,0) not null comment '菜单id'
) comment = '角色和菜单关联表';


drop table if exists sys_user;
create table sys_user (
  user_id           numeric(20)      not null primary key comment '用户主键',
  dept_id           numeric(20)      default null comment '部门id',
  user_name         varchar(30)    not null comment '用户账号',
  nick_name         varchar(30)    default '' comment '用户昵称',
  user_type         varchar(2)     default '00' comment '用户类型（00系统用户 01注册用户）',
  email             varchar(50)    default '' comment '用户邮箱',
  phonenumber       varchar(11)    default '' comment '手机号码',
  sex               char(1)         default '0' comment '用户性别（0男 1女 2未知）',
  avatar            varchar(100)   default '' comment '头像路径',
  password          varchar(100)   default '' comment '密码',
  status            char(1)         default '0' comment '帐号状态（0正常 1停用）',
  del_flag          char(1)         default '0' comment '删除标志（0代表存在 2代表删除）',
  login_ip          varchar(128)   default '' comment '最后登录ip',
  login_date        timestamp comment '最后登录时间',
  create_by         varchar(64) comment '创建者',
  create_time 	    timestamp default now() comment '创建时间',
  update_by         varchar(64)    default '' comment '更新者',
  update_time       timestamp default now() comment '更新时间',
  remark            varchar(500)   default '' comment '备注',
 force_change_pwd varchar(1) default '1' comment '强制更改密码',
 salt varchar(16) default null comment '密码加盐',
 idcard varchar(20) default null comment '身份证号',
 pin_yin_1 varchar(20) default null comment '昵称拼音各字首字母',
 pin_yin_2 varchar(64) default null comment '昵称拼音',
 token varchar(255) default null comment 'token',
 open_id varchar(255) default null comment 'openid'
) comment '用户信息表';

create table sys_user_role (
 user_id decimal(20,0) not null primary key comment '用户id',
 role_id decimal(20,0) not null comment '角色id'
) comment = '用户和角色关联表';


create table system_var (
 id int not null primary key comment '主键',
 name varchar(255) default null comment '名称',
 update_time timestamp default now() not null comment '最近更新时间',
 value varchar(255) default null comment '当前值id',
 version int default null comment '版本'
) comment = '应用系统变量表';

create table t_image (
 code varchar(40) not null primary key comment '附件id',
 path varchar(256) default null comment '文件保存路径',
 create_time timestamp default now() not null comment '创建时间',
 expired_time timestamp default now() comment '过期时间'
) comment = '附件正式表';

create table t_image_tmp (
 code varchar(40) not null primary key comment '附件id',
 path varchar(256) default null comment '文件保存路径',
 used_flag char(1) default '0' comment '0是未被使用；1是已被使用',
 create_time timestamp default now() not null comment '创建时间',
 expired_time timestamp default now() not null comment '过期时间，如未被使用且过期后将会被物理删除即要删除文件'
) comment = '附件临时表';


-- 新增数据
-- 系统
insert into system_var (id,name,update_time,value,version)values (-2, 'webguestversion', '2021-10-13 18:31:57', '1.0.0', 0);
insert into system_var (id,name,update_time,value,version)values (-1, 'webadminversion','2021-10-13 18:31:57', '1.0.0', 0);
insert into system_var (id,name,update_time,value,version)values (2, 'db table version', '2021-10-13 18:31:57', '1.0.1', 1);

-- 用户表
insert into sys_user(user_id,dept_id,user_name,nick_name,user_type,email,phonenumber,sex,avatar,password,status,del_flag,login_ip,login_date,create_by,create_time,update_by,update_time,remark,force_change_pwd,salt,idcard,pin_yin_1,pin_yin_2,token)
values ('1', '103', 'admin', '蛟驰', '00', '<EMAIL>', '15888888888', '1', '', '$2a$10$udmlabw6xagm.d0gqjzrqojkx5cnk5330zdsmqnkw25d45uubbhfc', '0', '0', '127.0.0.1', '2022-08-04 00:00:00', 'admin', '2022-08-04 00:00:00', '', '2022-08-04', '管理员', '1', null, null, null, null, null);

-- 用户权限表
insert into sys_user_role(user_id,role_id) values (1, 1);

-- 角色表（超管）
insert into sys_role(role_id,role_name,role_key,role_sort,data_scope,menu_check_strictly,dept_check_strictly,status,del_flag,create_by,create_time,update_by,update_time,remark,pin_yin_1,pin_yin_2) values (1, '超级管理员', 'admin', 1, '1', 1, 1, '0', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '超级管理员', null, null);
-- 角色表（平台相关）
insert into sys_role(role_id,role_name,role_key,role_sort,data_scope,menu_check_strictly,dept_check_strictly,status,del_flag,create_by,create_time,update_by,update_time,remark,pin_yin_1,pin_yin_2)
 values (2, '客户', 'client', 2, '2', 1, 1, '0', '0', 'admin', '2022-08-04 18:31:48', '', '2022-08-04 18:31:48', '客户角色', null, null);
insert into sys_role(role_id,role_name,role_key,role_sort,data_scope,menu_check_strictly,dept_check_strictly,status,del_flag,create_by,create_time,update_by,update_time,remark,pin_yin_1,pin_yin_2)
 values (3, '保司', 'client', 3, '3', 1, 1, '0', '0', 'admin', '2022-08-04 18:31:48', '', '2022-08-04 18:31:48', '客户角色', null, null);
insert into sys_role(role_id,role_name,role_key,role_sort,data_scope,menu_check_strictly,dept_check_strictly,status,del_flag,create_by,create_time,update_by,update_time,remark,pin_yin_1,pin_yin_2)
 values (4, '服务公司', 'client', 4, '4', 1, 1, '0', '0', 'admin', '2022-08-04 18:31:48', '', '2022-08-04 18:31:48', '客户角色', null, null);

-- 菜单表
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1, '系统管理', 0, 2, '/system', '', 1, 0, 'm', '0', '0', '', 'system', 'admin', '2021-10-13 18:31:48', 'admin', '2021-10-13 18:31:48', '系统管理目录');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (2, '系统监控', 0, 3, '/monitor', '', 1, 0, 'm', '1', '0', '', 'monitor', 'admin', '2021-10-13 18:31:48', 'admin', '2021-10-13 18:31:48', '系统监控目录');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (3, '系统工具', 0, 4, '/tool', '', 1, 0, 'm', '1', '0', '', 'tool', 'admin', '2021-10-13 18:31:48', 'admin', '2021-10-13 18:31:48', '系统工具目录');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (5, '案件管理', 0, 1, '/case', '', 1, 0, 'm', '0', '0', '', 'dashboard', 'admin', '2021-10-13 18:31:48', 'admin', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (6, '报案处理', 5, 1, 'index', 'case/index', 1, 0, 'c', '0', '0', NULL, 'dashboard', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (100, '用户管理', 1, 1, 'user', 'system/user/index', 1, 0, 'c', '0', '0', 'system:user:list', 'user', 'admin', '2021-10-13 18:31:48', 'admin', '2021-10-13 18:31:48', '用户管理菜单');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (101, '角色管理', 1, 2, 'role', 'system/role/index', 1, 0, 'c', '0', '0', 'system:role:list', 'peoples', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '角色管理菜单');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (102, '菜单管理', 1, 3, 'menu', 'system/menu/index', 1, 0, 'c', '0', '0', 'system:menu:list', 'tree-table', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '菜单管理菜单');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (103, '机构管理', 1, 4, 'dept', 'system/dept/index', 1, 0, 'c', '0', '0', 'system:dept:list', 'tree', 'admin', '2021-10-13 18:31:48', 'admin', '2021-10-13 18:31:48', '部门管理菜单');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (105, '字典管理', 1, 6, 'dict', 'system/dict/index', 1, 0, 'c', '0', '0', 'system:dict:list', 'dict', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '字典管理菜单');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (106, '参数设置', 1, 7, 'config', 'system/config/index', 1, 0, 'c', '0', '0', 'system:config:list', 'edit', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '参数设置菜单');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (107, '通知公告', 1, 8, 'notice', 'system/notice/index', 1, 0, 'c', '1', '0', 'system:notice:list', 'message', 'admin', '2021-10-13 18:31:48', 'admin', '2021-10-13 18:31:48', '通知公告菜单');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (108, '日志管理', 1, 9, 'log', '', 1, 0, 'm', '0', '0', '', 'log', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '日志管理菜单');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (109, '在线用户', 2, 1, 'online', 'monitor/online/index', 1, 0, 'c', '0', '0', 'monitor:online:list', 'online', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '在线用户菜单');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (110, '定时任务', 2, 2, 'job', 'monitor/job/index', 1, 0, 'c', '0', '0', 'monitor:job:list', 'job', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '定时任务菜单');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (111, '数据监控', 2, 3, 'druid', 'monitor/druid/index', 1, 0, 'c', '0', '0', 'monitor:druid:list', 'druid', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '数据监控菜单');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (112, '服务监控', 2, 4, 'server', 'monitor/server/index', 1, 0, 'c', '0', '0', 'monitor:server:list', 'server', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '服务监控菜单');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (113, '缓存监控', 2, 5, 'cache', 'monitor/cache/index', 1, 0, 'c', '0', '0', 'monitor:cache:list', 'redis', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '缓存监控菜单');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (114, '表单构建', 3, 1, 'build', 'tool/build/index', 1, 0, 'c', '0', '0', 'tool:build:list', 'build', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '表单构建菜单');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (115, '代码生成', 3, 2, 'gen', 'tool/gen/index', 1, 0, 'c', '0', '0', 'tool:gen:list', 'code', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '代码生成菜单');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (116, '系统接口', 3, 3, 'swagger', 'tool/swagger/index', 1, 0, 'c', '0', '0', 'tool:swagger:list', 'swagger', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '系统接口菜单');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (500, '操作日志', 108, 1, 'operlog', 'monitor/operlog/index', 1, 0, 'c', '0', '0', 'monitor:operlog:list', 'form', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '操作日志菜单');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (501, '登录日志', 108, 2, 'logininfor', 'monitor/logininfor/index', 1, 0, 'c', '0', '0', 'monitor:logininfor:list', 'logininfor', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '登录日志菜单');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1001, '用户查询', 100, 1, '', '', 1, 0, 'f', '0', '0', 'system:user:query', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1002, '用户新增', 100, 2, '', '', 1, 0, 'f', '0', '0', 'system:user:add', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1003, '用户修改', 100, 3, '', '', 1, 0, 'f', '0', '0', 'system:user:edit', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1004, '用户删除', 100, 4, '', '', 1, 0, 'f', '0', '0', 'system:user:remove', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1005, '用户导出', 100, 5, '', '', 1, 0, 'f', '0', '0', 'system:user:export', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1006, '用户导入', 100, 6, '', '', 1, 0, 'f', '0', '0', 'system:user:import', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1007, '重置密码', 100, 7, '', '', 1, 0, 'f', '0', '0', 'system:user:resetpwd', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1008, '角色查询', 101, 1, '', '', 1, 0, 'f', '0', '0', 'system:role:query', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1009, '角色新增', 101, 2, '', '', 1, 0, 'f', '0', '0', 'system:role:add', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1010, '角色修改', 101, 3, '', '', 1, 0, 'f', '0', '0', 'system:role:edit', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1011, '角色删除', 101, 4, '', '', 1, 0, 'f', '0', '0', 'system:role:remove', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1012, '角色导出', 101, 5, '', '', 1, 0, 'f', '0', '0', 'system:role:export', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1013, '菜单查询', 102, 1, '', '', 1, 0, 'f', '0', '0', 'system:menu:query', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1014, '菜单新增', 102, 2, '', '', 1, 0, 'f', '0', '0', 'system:menu:add', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1015, '菜单修改', 102, 3, '', '', 1, 0, 'f', '0', '0', 'system:menu:edit', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1016, '菜单删除', 102, 4, '', '', 1, 0, 'f', '0', '0', 'system:menu:remove', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1017, '机构查询', 103, 1, '', '', 1, 0, 'f', '0', '0', 'system:dept:query', '#', 'admin', '2021-10-13 18:31:48', 'admin', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1018, '机构新增', 103, 2, '', '', 1, 0, 'f', '0', '0', 'system:dept:add', '#', 'admin', '2021-10-13 18:31:48', 'admin', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1019, '机构修改', 103, 3, '', '', 1, 0, 'f', '0', '0', 'system:dept:edit', '#', 'admin', '2021-10-13 18:31:48', 'admin', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1020, '机构删除', 103, 4, '', '', 1, 0, 'f', '0', '0', 'system:dept:remove', '#', 'admin', '2021-10-13 18:31:48', 'admin', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1026, '字典查询', 105, 1, '#', '', 1, 0, 'f', '0', '0', 'system:dict:query', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1027, '字典新增', 105, 2, '#', '', 1, 0, 'f', '0', '0', 'system:dict:add', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1028, '字典修改', 105, 3, '#', '', 1, 0, 'f', '0', '0', 'system:dict:edit', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1029, '字典删除', 105, 4, '#', '', 1, 0, 'f', '0', '0', 'system:dict:remove', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1030, '字典导出', 105, 5, '#', '', 1, 0, 'f', '0', '0', 'system:dict:export', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1031, '参数查询', 106, 1, '#', '', 1, 0, 'f', '0', '0', 'system:config:query', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1032, '参数新增', 106, 2, '#', '', 1, 0, 'f', '0', '0', 'system:config:add', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1033, '参数修改', 106, 3, '#', '', 1, 0, 'f', '0', '0', 'system:config:edit', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1034, '参数删除', 106, 4, '#', '', 1, 0, 'f', '0', '0', 'system:config:remove', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1035, '参数导出', 106, 5, '#', '', 1, 0, 'f', '0', '0', 'system:config:export', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1036, '公告查询', 107, 1, '#', '', 1, 0, 'f', '0', '0', 'system:notice:query', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1037, '公告新增', 107, 2, '#', '', 1, 0, 'f', '0', '0', 'system:notice:add', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1038, '公告修改', 107, 3, '#', '', 1, 0, 'f', '0', '0', 'system:notice:edit', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1039, '公告删除', 107, 4, '#', '', 1, 0, 'f', '0', '0', 'system:notice:remove', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1040, '操作查询', 500, 1, '#', '', 1, 0, 'f', '0', '0', 'monitor:operlog:query', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1041, '操作删除', 500, 2, '#', '', 1, 0, 'f', '0', '0', 'monitor:operlog:remove', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1042, '日志导出', 500, 4, '#', '', 1, 0, 'f', '0', '0', 'monitor:operlog:export', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1043, '登录查询', 501, 1, '#', '', 1, 0, 'f', '0', '0', 'monitor:logininfor:query', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1044, '登录删除', 501, 2, '#', '', 1, 0, 'f', '0', '0', 'monitor:logininfor:remove', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1045, '日志导出', 501, 3, '#', '', 1, 0, 'f', '0', '0', 'monitor:logininfor:export', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1046, '在线查询', 109, 1, '#', '', 1, 0, 'f', '0', '0', 'monitor:online:query', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1047, '批量强退', 109, 2, '#', '', 1, 0, 'f', '0', '0', 'monitor:online:batchlogout', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1048, '单条强退', 109, 3, '#', '', 1, 0, 'f', '0', '0', 'monitor:online:forcelogout', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1049, '任务查询', 110, 1, '#', '', 1, 0, 'f', '0', '0', 'monitor:job:query', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1050, '任务新增', 110, 2, '#', '', 1, 0, 'f', '0', '0', 'monitor:job:add', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1051, '任务修改', 110, 3, '#', '', 1, 0, 'f', '0', '0', 'monitor:job:edit', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1052, '任务删除', 110, 4, '#', '', 1, 0, 'f', '0', '0', 'monitor:job:remove', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1053, '状态修改', 110, 5, '#', '', 1, 0, 'f', '0', '0', 'monitor:job:changestatus', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');
INSERT INTO sys_menu (menu_id, menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES (1054, '任务导出', 110, 7, '#', '', 1, 0, 'f', '0', '0', 'monitor:job:export', '#', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '');


-- 字典
insert into sys_dict_type(dict_id,dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark,type_kind) values (1, '用户性别', 'sys_user_sex', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '用户性别列表', 1);
insert into sys_dict_type (dict_id,dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark,type_kind)values (2, '菜单状态', 'sys_show_hide', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '菜单状态列表', 1);
insert into sys_dict_type (dict_id,dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark,type_kind)values (3, '系统开关', 'sys_normal_disable', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '系统开关列表', 1);
insert into sys_dict_type (dict_id,dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark,type_kind)values (4, '任务状态', 'sys_job_status', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '任务状态列表', 1);
insert into sys_dict_type (dict_id,dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark,type_kind)values (5, '任务分组', 'sys_job_group', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '任务分组列表', 1);
insert into sys_dict_type (dict_id,dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark,type_kind)values (6, '系统是否', 'sys_yes_no', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '系统是否列表', 1);
insert into sys_dict_type (dict_id,dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark,type_kind)values (7, '通知类型', 'sys_notice_type', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '通知类型列表', 1);
insert into sys_dict_type (dict_id,dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark,type_kind)values (8, '通知状态', 'sys_notice_status', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '通知状态列表', 1);
insert into sys_dict_type (dict_id,dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark,type_kind)values (9, '操作类型', 'sys_oper_type', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '操作类型列表', 1);
insert into sys_dict_type (dict_id,dict_name,dict_type,status,create_by,create_time,update_by,update_time,remark,type_kind)values (10, '系统状态', 'sys_common_status', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '登录状态列表', 1);

insert into sys_dict_data(dict_code,dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark) values (1, 1, '男', '0', 'sys_user_sex', '', '', 'y', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '性别男');
insert into sys_dict_data (dict_code,dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark)values (2, 2, '女', '1', 'sys_user_sex', '', '', 'n', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '性别女');
insert into sys_dict_data (dict_code,dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark)values (3, 3, '未知', '2', 'sys_user_sex', '', '', 'n', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '性别未知');
insert into sys_dict_data (dict_code,dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark)values (4, 1, '显示', '0', 'sys_show_hide', '', 'primary', 'y', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '显示菜单');
insert into sys_dict_data (dict_code,dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark)values (5, 2, '隐藏', '1', 'sys_show_hide', '', 'danger', 'n', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '隐藏菜单');
insert into sys_dict_data (dict_code,dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark)values (6, 1, '正常', '0', 'sys_normal_disable', '', 'primary', 'y', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '正常状态');
insert into sys_dict_data (dict_code,dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark)values (7, 2, '停用', '1', 'sys_normal_disable', '', 'danger', 'n', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '停用状态');
insert into sys_dict_data (dict_code,dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark)values (8, 1, '正常', '0', 'sys_job_status', '', 'primary', 'y', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '正常状态');
insert into sys_dict_data (dict_code,dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark)values (9, 2, '暂停', '1', 'sys_job_status', '', 'danger', 'n', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '停用状态');
insert into sys_dict_data (dict_code,dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark)values (10, 1, '默认', 'default', 'sys_job_group', '', '', 'y', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '默认分组');
insert into sys_dict_data (dict_code,dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark)values (11, 2, '系统', 'system', 'sys_job_group', '', '', 'n', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '系统分组');
insert into sys_dict_data (dict_code,dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark)values (12, 1, '是', 'y', 'sys_yes_no', '', 'primary', 'y', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '系统默认是');
insert into sys_dict_data (dict_code,dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark)values (13, 2, '否', 'n', 'sys_yes_no', '', 'danger', 'n', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '系统默认否');
insert into sys_dict_data (dict_code,dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark)values (14, 1, '通知', '1', 'sys_notice_type', '', 'warning', 'y', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '通知');
insert into sys_dict_data (dict_code,dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark)values (15, 2, '公告', '2', 'sys_notice_type', '', 'success', 'n', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '公告');
insert into sys_dict_data (dict_code,dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark)values (16, 1, '正常', '0', 'sys_notice_status', '', 'primary', 'y', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '正常状态');
insert into sys_dict_data (dict_code,dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark)values (17, 2, '关闭', '1', 'sys_notice_status', '', 'danger', 'n', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '关闭状态');
insert into sys_dict_data (dict_code,dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark)values (18, 99, '其他', '0', 'sys_oper_type', '', 'info', 'n', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '其他操作');
insert into sys_dict_data (dict_code,dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark)values (19, 1, '新增', '1', 'sys_oper_type', '', 'info', 'n', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '新增操作');
insert into sys_dict_data (dict_code,dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark)values (20, 2, '修改', '2', 'sys_oper_type', '', 'info', 'n', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '修改操作');
insert into sys_dict_data (dict_code,dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark)values (21, 3, '删除', '3', 'sys_oper_type', '', 'danger', 'n', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '删除操作');
insert into sys_dict_data (dict_code,dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark)values (22, 4, '授权', '4', 'sys_oper_type', '', 'primary', 'n', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '授权操作');
insert into sys_dict_data (dict_code,dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark)values (23, 5, '导出', '5', 'sys_oper_type', '', 'warning', 'n', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '导出操作');
insert into sys_dict_data (dict_code,dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark)values (24, 6, '导入', '6', 'sys_oper_type', '', 'warning', 'n', '0', 'admin', '2021-10-13 18:31:48', '','2021-10-13 18:31:48', '导入操作');
insert into sys_dict_data (dict_code,dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark)values (25, 7, '强退', '7', 'sys_oper_type', '', 'danger', 'n', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '强退操作');
insert into sys_dict_data (dict_code,dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark)values (26, 8, '生成代码', '8', 'sys_oper_type', '', 'warning', 'n', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '生成操作');
insert into sys_dict_data (dict_code,dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark)values (27, 9, '清空数据', '9', 'sys_oper_type', '', 'danger', 'n', '0', 'admin','2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '清空操作');
insert into sys_dict_data (dict_code,dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark)values (28, 1, '成功', '0', 'sys_common_status', '', 'primary', 'n', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '正常状态');
insert into sys_dict_data (dict_code,dict_sort,dict_label,dict_value,dict_type,css_class,list_class,is_default,status,create_by,create_time,update_by,update_time,remark)values (29, 2, '失败', '1', 'sys_common_status', '', 'danger', 'n', '0', 'admin', '2021-10-13 18:31:48', '', '2021-10-13 18:31:48', '停用状态');

insert into sys_dept(dept_id,parent_id,ancestors,dept_name,order_num,leader,phone,email,status,del_flag,create_by,create_time,update_by,update_time,sno,pin_yin_1,pin_yin_2)values (100, 0, '0', '全国', 0, '蛟驰', '15888888888', '<EMAIL>', '0', '0', 'admin', '2021-10-13 18:31:48', '13516826556', '2021-10-13 18:31:48', '00', null, null);

insert into sys_config (config_id,config_name,config_key,config_value,config_type,create_by,update_by,create_time,update_time,remark) values (1, '主框架页-默认皮肤样式名称', 'sys.index.skinname', 'skin-blue', 'y', 'admin', '', '2021-10-13 18:31:48', '2021-10-13 18:31:48', '蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow');
insert into sys_config (config_id,config_name,config_key,config_value,config_type,create_by,update_by,create_time,update_time,remark)values (2, '用户管理-账号初始密码', 'sys.user.initpassword', 'superp1cc', 'y', 'admin','admin', '2021-10-13 18:31:48', '2021-10-13 18:31:48', '初始化');
insert into sys_config (config_id,config_name,config_key,config_value,config_type,create_by,update_by,create_time,update_time,remark)values (3, '主框架页-侧边栏主题', 'sys.index.sidetheme', 'theme-dark', 'y', 'admin','', '2021-10-13 18:31:48', '2021-10-13 18:31:48', '深黑主题theme-dark，浅色主题theme-light，深蓝主题theme-blue');
insert into sys_config (config_id,config_name,config_key,config_value,config_type,create_by,update_by,create_time,update_time,remark)values (4, '账号自助-是否开启用户注册功能', 'sys.account.registeruser', 'false', 'y', 'admin','', '2021-10-13 18:31:48', '2021-10-13 18:31:48', '是否开启注册用户功能');

-- 分布式事物锁
drop table if exists t_message_consumer;
create table t_message_consumer (
 message_id varchar(64) not null primary key comment '消息id',
 message_type varchar(32) not null comment '消息类型',
 message_value varchar(64) not null comment '消息值',
 run_node_name varchar(32) null comment '可消费此消息的结点名称（相当于锁）',
 next_run_time timestamp(6) comment '下一次运行时间',
 retry_count int4 comment '累计运行次数',
 retry_diff_ms int8 comment '下次再消费此消息的间隔时间-每次翻倍（毫秒）',
 max_retry_count int4 comment '累计最大重复消费次数'
) comment =  '用于模拟简单的消息队列';
create unique index unk_t_message_value on t_message_consumer (message_type, message_value);


-- mail or xml import lock
insert into t_message_consumer ( message_id, message_type, message_value, retry_count, retry_diff_ms, max_retry_count )
values (1, 'import_cent_task', '0', 0, 60000, 3000);
insert into t_message_consumer ( message_id, message_type, message_value, retry_count, retry_diff_ms, max_retry_count )
values (2, 'day_end_task', '0', 0, 60000, 3000);
