# 投入品库存盘点接口测试用例

## 测试环境
- 接口地址：`POST /nsInputStockTaking/nsInputStockTakingSave`
- 权限要求：`business:nsInputStockTaking:edit`

## 测试用例1：正常盘点（更新现有库存）

### 请求参数
```json
{
  "inputStockId": 2074,
  "houseId": 2055,
  "takingOperator": "admin",
  "takingDate": "2025-09-21",
  "stockItems": [
    {
      "inputStockId": 2062,
      "businessId": 2074,
      "inputType": 2,
      "spec": "袋",
      "unit": "袋",
      "stockQuantity": 10,
      "takingStockQuantity": 10
    }
  ],
  "regionDeptId": "100"
}
```

### 预期结果
- 返回成功响应
- 库存数量保持不变（10）
- 生成盘点记录，盘点结果为0（正常）

## 测试用例2：盘盈情况

### 请求参数
```json
{
  "inputStockId": 2074,
  "houseId": 2055,
  "takingOperator": "admin",
  "takingDate": "2025-09-21",
  "stockItems": [
    {
      "inputStockId": 2062,
      "businessId": 2074,
      "inputType": 2,
      "spec": "袋",
      "unit": "袋",
      "stockQuantity": 10,
      "takingStockQuantity": 15
    }
  ],
  "regionDeptId": "100"
}
```

### 预期结果
- 返回成功响应
- 库存数量更新为15
- 生成盘点记录，盘点结果为2（盘盈）

## 测试用例3：盘亏情况

### 请求参数
```json
{
  "inputStockId": 2074,
  "houseId": 2055,
  "takingOperator": "admin",
  "takingDate": "2025-09-21",
  "stockItems": [
    {
      "inputStockId": 2062,
      "businessId": 2074,
      "inputType": 2,
      "spec": "袋",
      "unit": "袋",
      "stockQuantity": 10,
      "takingStockQuantity": 5
    }
  ],
  "regionDeptId": "100"
}
```

### 预期结果
- 返回成功响应
- 库存数量更新为5
- 生成盘点记录，盘点结果为1（盘亏）

## 测试用例4：新增库存项目

### 请求参数
```json
{
  "inputStockId": 2074,
  "houseId": 2055,
  "takingOperator": "admin",
  "takingDate": "2025-09-21",
  "stockItems": [
    {
      "inputStockId": null,
      "businessId": 2075,
      "inputType": 3,
      "spec": "瓶",
      "unit": "瓶",
      "stockQuantity": 0,
      "takingStockQuantity": 20
    }
  ],
  "regionDeptId": "100"
}
```

### 预期结果
- 返回成功响应
- 新增库存记录，数量为20
- 生成盘点记录，盘点结果为2（盘盈）

## 测试用例5：混合操作（更新+新增）

### 请求参数
```json
{
  "inputStockId": 2074,
  "houseId": 2055,
  "takingOperator": "admin",
  "takingDate": "2025-09-21",
  "stockItems": [
    {
      "inputStockId": 2062,
      "businessId": 2074,
      "inputType": 2,
      "spec": "袋",
      "unit": "袋",
      "stockQuantity": 10,
      "takingStockQuantity": 8
    },
    {
      "inputStockId": null,
      "businessId": 2075,
      "inputType": 3,
      "spec": "瓶",
      "unit": "瓶",
      "stockQuantity": 0,
      "takingStockQuantity": 15
    }
  ],
  "regionDeptId": "100"
}
```

### 预期结果
- 返回成功响应
- 更新第一个库存项目数量为8
- 新增第二个库存项目数量为15
- 生成2条盘点记录

## 测试用例6：指定删除库存项目

### 请求参数
```json
{
  "inputStockId": 2074,
  "houseId": 2055,
  "takingOperator": "admin",
  "takingDate": "2025-09-21",
  "stockItems": [
    {
      "inputStockId": 2062,
      "businessId": 2074,
      "inputType": 2,
      "spec": "袋",
      "unit": "袋",
      "stockQuantity": 10,
      "takingStockQuantity": 12
    }
  ],
  "deleteStockIds": [2063, 2064],
  "regionDeptId": "100"
}
```

### 预期结果
- 返回成功响应
- 更新库存项目2062的数量为12
- 删除库存项目2063和2064
- 生成3条盘点记录（1条更新，2条删除）

## 测试用例7：完全同步模式

### 请求参数
```json
{
  "inputStockId": 2074,
  "houseId": 2055,
  "takingOperator": "admin",
  "takingDate": "2025-09-21",
  "fullSync": true,
  "stockItems": [
    {
      "inputStockId": 2062,
      "businessId": 2074,
      "inputType": 2,
      "spec": "袋",
      "unit": "袋",
      "stockQuantity": 10,
      "takingStockQuantity": 12
    }
  ],
  "regionDeptId": "100"
}
```

### 预期结果
- 返回成功响应
- 更新库存项目2062的数量为12
- 删除数据库中存在但未在stockItems中提交的其他库存项目
- 为所有操作生成盘点记录

## 错误测试用例

### 测试用例8：缺少必填参数
```json
{
  "houseId": 2055,
  "takingOperator": "admin",
  "stockItems": []
}
```
预期：返回错误信息"盘点日期不能为空！"

### 测试用例9：负数盘点数量
```json
{
  "houseId": 2055,
  "takingOperator": "admin",
  "takingDate": "2025-09-21",
  "stockItems": [
    {
      "businessId": 2074,
      "inputType": 2,
      "takingStockQuantity": -5
    }
  ],
  "regionDeptId": "100"
}
```
预期：返回错误信息"盘点数量不能为负数！"

## 验证点

1. **数据库验证**：
   - 检查 `ns_input_stock` 表中库存数量是否正确更新
   - 检查 `ns_input_stock_taking` 表中是否生成了正确的盘点记录

2. **业务逻辑验证**：
   - 盘点结果计算是否正确
   - 预警状态更新是否正确
   - 新增库存项目的基本信息是否正确填充

3. **事务验证**：
   - 如果过程中出现异常，是否正确回滚
   - 多个库存项目的操作是否保持一致性
