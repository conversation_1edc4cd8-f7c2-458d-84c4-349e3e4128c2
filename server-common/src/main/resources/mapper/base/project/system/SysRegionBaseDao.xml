<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!-- ============================================================== -->
<!-- ===========通过 CodeGenerator 工具自动生成，请勿手工修改！！！====== -->
<!-- ============================================================== -->
<mapper namespace="com.jorchi.project.system.dao.SysRegionDao">
    <!-- 通用查询结果对象-->
    <resultMap id="BaseResultMap" type="com.jorchi.project.system.po.SysRegion">
        <id column="region_id" property="regionId" />
        <result column="parent_id" property="parentId" />
        <result column="is_leaf" property="isLeaf" />
        <result column="ancestors" property="ancestors" />
        <result column="region_name" property="regionName" />
        <result column="order_num" property="orderNum" />
        <result column="leader" property="leader" />
        <result column="phone" property="phone" />
        <result column="email" property="email" />
        <result column="status" property="status" />
        <result column="del_flag" property="delFlag" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="sno" property="sno" />
        <result column="pin_yin_1" property="pinYin1" />
        <result column="pin_yin_2" property="pinYin2" />
    </resultMap>

    <!-- 通用查询结果列-->
    <sql id="Base_Column_List">
        region_id,
        parent_id,
        is_leaf,
        ancestors,
        region_name,
        order_num,
        leader,
        phone,
        email,
        status,
        del_flag,
        create_by,
        create_time,
        update_by,
        update_time,
        sno,
        pin_yin_1,
        pin_yin_2
    </sql>

    <!-- 按对象查询记录的WHERE部分 -->
    <sql id="Base_Select_By_Entity_Where">
        <if test="regionId != null">
            and region_id = #{regionId}
        </if>
        <if test="parentId != null">
            and parent_id = #{parentId}
        </if>
        <if test="isLeaf != null and isLeaf != ''">
            and is_leaf = #{isLeaf}
        </if>
        <if test="ancestors != null and ancestors != ''">
            and ancestors = #{ancestors}
        </if>
        <if test="regionName != null and regionName != ''">
            and region_name = #{regionName}
        </if>
        <if test="orderNum != null">
            and order_num = #{orderNum}
        </if>
        <if test="leader != null and leader != ''">
            and leader = #{leader}
        </if>
        <if test="phone != null and phone != ''">
            and phone = #{phone}
        </if>
        <if test="email != null and email != ''">
            and email = #{email}
        </if>
        <if test="status != null and status != ''">
            and status = #{status}
        </if>
        <if test="delFlag != null and delFlag != ''">
            and del_flag = #{delFlag}
        </if>
        <if test="createBy != null and createBy != ''">
            and create_by = #{createBy}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime}
        </if>
        <if test="updateBy != null and updateBy != ''">
            and update_by = #{updateBy}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime}
        </if>
        <if test="sno != null and sno != ''">
            and sno = #{sno}
        </if>
        <if test="pinYin1 != null and pinYin1 != ''">
            and pin_yin_1 = #{pinYin1}
        </if>
        <if test="pinYin2 != null and pinYin2 != ''">
            and pin_yin_2 = #{pinYin2}
        </if>
    </sql>

    <!-- 按对象查询记录的SQL部分 -->
    <sql id="Base_Select_By_Entity">
        select
            <include refid="Base_Column_List" />
        from sys_region
        <where>
            <include refid="Base_Select_By_Entity_Where" />
        </where>
    </sql>

    <!-- 按主键查询一条记录 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from sys_region
         where region_id = #{param1}
    </select>

    <sql id="Base_Exists_By_PrimaryKey">
        select count(*)
          from sys_region
         where region_id = #{param1}
    </sql>

    <!-- 检查对象是否存在，实际按Key查询数据库是否有对应记录 -->
    <select id="exists" resultType="boolean">
        <include refid="Base_Exists_By_PrimaryKey" />
    </select>

    <!-- 检查对象是否存在，实际按Key查询数据库是否有对应记录 -->
    <select id="existsByEntity" resultType="boolean">
        <include refid="Base_Exists_By_PrimaryKey" />
    </select>

    <!-- 按主键List查询多条记录 -->
    <select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from sys_region
         where region_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 按entity对象List查询多条记录（实际根据对象的主键值查询） -->
    <select id="selectBatch" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from sys_region
         where region_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.regionId}
        </foreach>
    </select>

    <!-- 按对象查询一页记录（多条记录） -->
    <select id="selectPage" resultMap="BaseResultMap">
        <include refid="Base_Select_By_Entity" />
    </select>

    <!-- 按主键删除一条记录 -->
    <delete id="deleteByPrimaryKey">
        delete from sys_region
         where region_id = #{param1}
    </delete>

    <!-- 按主键List删除多条记录 -->
    <delete id="deleteBatchByPrimaryKeys">
        delete from sys_region
         where region_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- 按对象删除一条记录 -->
    <delete id="delete">
        delete from sys_region
         where region_id = #{regionId}
    </delete>

    <!-- 按对象List删除多条记录 -->
    <delete id="deleteBatch">
        delete from sys_region
         where region_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.regionId}
        </foreach>
    </delete>

    <!-- 完整插入一条记录-->
    <insert id="insert" >
        insert into sys_region
        <trim prefix="(" suffix=")" suffixOverrides="," >
            region_id,
            parent_id,
            is_leaf,
            ancestors,
            region_name,
            order_num,
            leader,
            phone,
            email,
            status,
            del_flag,
            create_by,
            create_time,
            update_by,
            update_time,
            sno,
            pin_yin_1,
            pin_yin_2
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            #{regionId},
            #{parentId},
            #{isLeaf},
            #{ancestors},
            #{regionName},
            #{orderNum},
            #{leader},
            #{phone},
            #{email},
            #{status},
            #{delFlag},
            #{createBy},
            #{createTime},
            #{updateBy},
            #{updateTime},
            #{sno},
            #{pinYin1},
            #{pinYin2}
        </trim>
    </insert>

    <!-- 插入一条记录(为空的字段不操作) -->
    <insert id="insertSelective" >
        insert into sys_region
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="regionId != null" >
                region_id,
            </if>
            <if test="parentId != null" >
                parent_id,
            </if>
            <if test="isLeaf != null" >
                is_leaf,
            </if>
            <if test="ancestors != null" >
                ancestors,
            </if>
            <if test="regionName != null" >
                region_name,
            </if>
            <if test="orderNum != null" >
                order_num,
            </if>
            <if test="leader != null" >
                leader,
            </if>
            <if test="phone != null" >
                phone,
            </if>
            <if test="email != null" >
                email,
            </if>
            <if test="status != null" >
                status,
            </if>
            <if test="delFlag != null" >
                del_flag,
            </if>
            <if test="createBy != null" >
                create_by,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateBy != null" >
                update_by,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
            <if test="sno != null" >
                sno,
            </if>
            <if test="pinYin1 != null" >
                pin_yin_1,
            </if>
            <if test="pinYin2 != null" >
                pin_yin_2
            </if>
        </trim>
        values <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="regionId != null" >
                #{regionId},
            </if>
            <if test="parentId != null" >
                #{parentId},
            </if>
            <if test="isLeaf != null" >
                #{isLeaf},
            </if>
            <if test="ancestors != null" >
                #{ancestors},
            </if>
            <if test="regionName != null" >
                #{regionName},
            </if>
            <if test="orderNum != null" >
                #{orderNum},
            </if>
            <if test="leader != null" >
                #{leader},
            </if>
            <if test="phone != null" >
                #{phone},
            </if>
            <if test="email != null" >
                #{email},
            </if>
            <if test="status != null" >
                #{status},
            </if>
            <if test="delFlag != null" >
                #{delFlag},
            </if>
            <if test="createBy != null" >
                #{createBy},
            </if>
            <if test="createTime != null" >
                #{createTime},
            </if>
            <if test="updateBy != null" >
                #{updateBy},
            </if>
            <if test="updateTime != null" >
                #{updateTime},
            </if>
            <if test="sno != null" >
                #{sno},
            </if>
            <if test="pinYin1 != null" >
                #{pinYin1},
            </if>
            <if test="pinYin2 != null" >
                #{pinYin2}
            </if>
        </trim>
    </insert>

    <!-- 完整插入多条记录-->
    <insert id="insertBatchBySQL" >
        insert into sys_region
        <trim prefix="(" suffix=")" suffixOverrides="," >
            region_id,
            parent_id,
            is_leaf,
            ancestors,
            region_name,
            order_num,
            leader,
            phone,
            email,
            status,
            del_flag,
            create_by,
            create_time,
            update_by,
            update_time,
            sno,
            pin_yin_1,
            pin_yin_2
        </trim>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides="," >
                #{item.regionId},
                #{item.parentId},
                #{item.isLeaf},
                #{item.ancestors},
                #{item.regionName},
                #{item.orderNum},
                #{item.leader},
                #{item.phone},
                #{item.email},
                #{item.status},
                #{item.delFlag},
                #{item.createBy},
                #{item.createTime},
                #{item.updateBy},
                #{item.updateTime},
                #{item.sno},
                #{item.pinYin1},
                #{item.pinYin2}
            </trim>
        </foreach>
    </insert>
    <sql id="Base_Update_Selective_By_PrimaryKey">
        update sys_region
          <set>
            <if test="parentId != null" >
                parent_id=#{parentId},
            </if>
            <if test="isLeaf != null" >
                is_leaf=#{isLeaf},
            </if>
            <if test="ancestors != null" >
                ancestors=#{ancestors},
            </if>
            <if test="regionName != null" >
                region_name=#{regionName},
            </if>
            <if test="orderNum != null" >
                order_num=#{orderNum},
            </if>
            <if test="leader != null" >
                leader=#{leader},
            </if>
            <if test="phone != null" >
                phone=#{phone},
            </if>
            <if test="email != null" >
                email=#{email},
            </if>
            <if test="status != null" >
                status=#{status},
            </if>
            <if test="delFlag != null" >
                del_flag=#{delFlag},
            </if>
            <if test="createBy != null" >
                create_by=#{createBy},
            </if>
            <if test="createTime != null" >
                create_time=#{createTime},
            </if>
            <if test="updateBy != null" >
                update_by=#{updateBy},
            </if>
            <if test="updateTime != null" >
                update_time=#{updateTime},
            </if>
            <if test="sno != null" >
                sno=#{sno},
            </if>
            <if test="pinYin1 != null" >
                pin_yin_1=#{pinYin1},
            </if>
            <if test="pinYin2 != null" >
                pin_yin_2=#{pinYin2},
            </if>
          </set>
         where region_id = #{regionId}
    </sql>

    <!-- 更新一条记录(为空的字段不操作) -->
    <update id="updateSelectiveByPrimaryKey" >
        <include refid="Base_Update_Selective_By_PrimaryKey" />
    </update>

    <!-- 更新一条记录(为空的字段不操作)WithVersion -->
    <update id="updateSelectiveByPrimaryKeyWithVersion" >
        <include refid="Base_Update_Selective_By_PrimaryKey" />
    </update>

    <sql id="Base_Update_By_PrimaryKey">
        update sys_region
          <set>
                parent_id=#{parentId},
                is_leaf=#{isLeaf},
                ancestors=#{ancestors},
                region_name=#{regionName},
                order_num=#{orderNum},
                leader=#{leader},
                phone=#{phone},
                email=#{email},
                status=#{status},
                del_flag=#{delFlag},
                create_by=#{createBy},
                create_time=#{createTime},
                update_by=#{updateBy},
                update_time=#{updateTime},
                sno=#{sno},
                pin_yin_1=#{pinYin1},
                pin_yin_2=#{pinYin2},
          </set>
         where region_id = #{regionId}
    </sql>

    <!-- 完整更新一条记录 -->
    <update id="updateByPrimaryKey" >
        <include refid="Base_Update_By_PrimaryKey" />
    </update>

    <!-- 完整更新一条记录WithVersion -->
    <update id="updateByPrimaryKeyWithVersion" >
        <include refid="Base_Update_By_PrimaryKey" />
    </update>
</mapper>
