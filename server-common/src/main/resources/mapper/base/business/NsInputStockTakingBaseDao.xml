<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
 基础 Mapper 表：ns_input_stock_taking (投入品库存盘点表)
 该 Mapper 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
-->
<mapper namespace="com.jorchi.business.dao.NsInputStockTakingDao">
    <!-- 通用查询结果对象-->
    <resultMap id="BaseResultMap" type="com.jorchi.business.po.NsInputStockTaking">
        <id column="taking_id" property="takingId" />
        <result column="business_id" property="businessId" />
        <result column="input_type" property="inputType" />
        <result column="input_category" property="inputCategory" />
        <result column="input_sub_category" property="inputSubCategory" />
        <result column="input_name" property="inputName" />
        <result column="stock_quantity" property="stockQuantity" />
        <result column="taking_stock_quantity" property="takingStockQuantity" />
        <result column="house_id" property="houseId" />
        <result column="house_name" property="houseName" />
        <result column="taking_result" property="takingResult" />
        <result column="warning_status" property="warningStatus" />
        <result column="taking_date" property="takingDate" />
        <result column="taking_operator" property="takingOperator" />
        <result column="taking_operator_id" property="takingOperatorId" />
        <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="region_dept_id" property="regionDeptId" />
    </resultMap>

    <!-- 通用查询结果列-->
    <sql id="Base_Column_List">
        taking_id,
        business_id,
        input_type,
        input_category,
        input_sub_category,
        input_name,
        stock_quantity,
        taking_stock_quantity,
        house_id,
        house_name,
        taking_result,
        warning_status,
        taking_date,
        taking_operator,
        taking_operator_id,
        deleted,
        create_by,
        create_time,
        update_by,
        update_time,
        region_dept_id
    </sql>

    <!-- 按对象查询记录的WHERE部分 -->
    <sql id="Base_Select_By_Entity_Where">
        <if test="takingId != null">
            and taking_id = #{takingId}
        </if>
        <if test="businessId != null">
            and business_id = #{businessId}
        </if>
        <if test="inputType != null">
            and input_type = #{inputType}
        </if>
        <if test="inputCategory != null and inputCategory != ''">
            and input_category = #{inputCategory}
        </if>
        <if test="inputSubCategory != null and inputSubCategory != ''">
            and input_sub_category = #{inputSubCategory}
        </if>
        <if test="inputName != null and inputName != ''">
            and input_name = #{inputName}
        </if>
        <if test="stockQuantity != null">
            and stock_quantity = #{stockQuantity}
        </if>
        <if test="takingStockQuantity != null">
            and taking_stock_quantity = #{takingStockQuantity}
        </if>
        <if test="houseId != null">
            and house_id = #{houseId}
        </if>
        <if test="houseName != null and houseName != ''">
            and house_name = #{houseName}
        </if>
        <if test="takingResult != null">
            and taking_result = #{takingResult}
        </if>
        <if test="warningStatus != null and warningStatus != ''">
            and warning_status = #{warningStatus}
        </if>
        <if test="takingDate != null">
            and taking_date = #{takingDate}
        </if>
        <if test="takingOperator != null and takingOperator != ''">
            and taking_operator = #{takingOperator}
        </if>
        <if test="takingOperatorId != null">
            and taking_operator_id = #{takingOperatorId}
        </if>
        <if test="deleted != null">
            and deleted = #{deleted}
        </if>
        <if test="createBy != null">
            and create_by = #{createBy}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime}
        </if>
        <if test="updateBy != null">
            and update_by = #{updateBy}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime}
        </if>
        <if test="regionDeptId != null">
            and region_dept_id = #{regionDeptId}
        </if>
    </sql>

    <!-- 按对象查询记录的SQL部分 -->
    <sql id="Base_Select_By_Entity">
        select
            <include refid="Base_Column_List" />
        from ns_input_stock_taking
        <where>
            <include refid="Base_Select_By_Entity_Where" />
        </where>
    </sql>

    <!-- 按主键查询一条记录 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_input_stock_taking
         where taking_id = #{param1}
    </select>

    <sql id="Base_Exists_By_PrimaryKey">
        select count(*)
          from ns_input_stock_taking
         where taking_id = #{param1}
    </sql>

    <!-- 检查对象是否存在，实际按Key查询数据库是否有对应记录 -->
    <select id="exists" resultType="boolean">
        <include refid="Base_Exists_By_PrimaryKey" />
    </select>

    <!-- 检查对象是否存在，实际按Key查询数据库是否有对应记录 -->
    <select id="existsByEntity" resultType="boolean">
        <include refid="Base_Exists_By_PrimaryKey" />
    </select>

    <!-- 按主键List查询多条记录 -->
    <select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_input_stock_taking
         where taking_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 按entity对象List查询多条记录（实际根据对象的主键值查询） -->
    <select id="selectBatch" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_input_stock_taking
         where taking_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.takingId}
        </foreach>
    </select>

    <!-- 按对象查询一页记录（多条记录） -->
    <select id="selectPage" resultMap="BaseResultMap">
        <include refid="Base_Select_By_Entity" />
    </select>

    <!-- 按主键删除一条记录 -->
    <delete id="deleteByPrimaryKey">
        delete from ns_input_stock_taking
         where taking_id = #{param1}
    </delete>

    <!-- 按主键List删除多条记录 -->
    <delete id="deleteBatchByPrimaryKeys">
        delete from ns_input_stock_taking
         where taking_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- 按对象删除一条记录 -->
    <delete id="delete">
        delete from ns_input_stock_taking
         where taking_id = #{takingId}
    </delete>

    <!-- 按对象List删除多条记录 -->
    <delete id="deleteBatch">
        delete from ns_input_stock_taking
         where taking_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.takingId}
        </foreach>
    </delete>

    <!-- 完整插入一条记录-->
    <insert id="insert" useGeneratedKeys="true" keyProperty="takingId">
        insert into ns_input_stock_taking
        <trim prefix="(" suffix=")" suffixOverrides="," >
            taking_id,
            business_id,
            input_type,
            input_category,
            input_sub_category,
            input_name,
            stock_quantity,
            taking_stock_quantity,
            house_id,
            house_name,
            taking_result,
            warning_status,
            taking_date,
            taking_operator,
            taking_operator_id,
            deleted,
            create_by,
            create_time,
            update_by,
            update_time,
            region_dept_id
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            #{takingId},
            #{businessId},
            #{inputType},
            #{inputCategory},
            #{inputSubCategory},
            #{inputName},
            #{stockQuantity},
            #{takingStockQuantity},
            #{houseId},
            #{houseName},
            #{takingResult},
            #{warningStatus},
            #{takingDate},
            #{takingOperator},
            #{takingOperatorId},
            #{deleted},
            #{createBy},
            #{createTime},
            #{updateBy},
            #{updateTime},
            #{regionDeptId}
        </trim>
    </insert>

    <!-- 插入一条记录(为空的字段不操作) -->
    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="takingId">
        insert into ns_input_stock_taking
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="takingId != null" >
                taking_id,
            </if>
            <if test="businessId != null" >
                business_id,
            </if>
            <if test="inputType != null" >
                input_type,
            </if>
            <if test="inputCategory != null" >
                input_category,
            </if>
            <if test="inputSubCategory != null" >
                input_sub_category,
            </if>
            <if test="inputName != null" >
                input_name,
            </if>
            <if test="stockQuantity != null" >
                stock_quantity,
            </if>
            <if test="takingStockQuantity != null" >
                taking_stock_quantity,
            </if>
            <if test="houseId != null" >
                house_id,
            </if>
            <if test="houseName != null" >
                house_name,
            </if>
            <if test="takingResult != null" >
                taking_result,
            </if>
            <if test="warningStatus != null" >
                warning_status,
            </if>
            <if test="takingDate != null" >
                taking_date,
            </if>
            <if test="takingOperator != null" >
                taking_operator,
            </if>
            <if test="takingOperatorId != null" >
                taking_operator_id,
            </if>
            <if test="deleted != null" >
                deleted,
            </if>
            <if test="createBy != null" >
                create_by,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateBy != null" >
                update_by,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
            <if test="regionDeptId != null" >
                region_dept_id
            </if>
        </trim>
        values <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="takingId != null" >
                #{takingId},
            </if>
            <if test="businessId != null" >
                #{businessId},
            </if>
            <if test="inputType != null" >
                #{inputType},
            </if>
            <if test="inputCategory != null" >
                #{inputCategory},
            </if>
            <if test="inputSubCategory != null" >
                #{inputSubCategory},
            </if>
            <if test="inputName != null" >
                #{inputName},
            </if>
            <if test="stockQuantity != null" >
                #{stockQuantity},
            </if>
            <if test="takingStockQuantity != null" >
                #{takingStockQuantity},
            </if>
            <if test="houseId != null" >
                #{houseId},
            </if>
            <if test="houseName != null" >
                #{houseName},
            </if>
            <if test="takingResult != null" >
                #{takingResult},
            </if>
            <if test="warningStatus != null" >
                #{warningStatus},
            </if>
            <if test="takingDate != null" >
                #{takingDate},
            </if>
            <if test="takingOperator != null" >
                #{takingOperator},
            </if>
            <if test="takingOperatorId != null" >
                #{takingOperatorId},
            </if>
            <if test="deleted != null" >
                #{deleted},
            </if>
            <if test="createBy != null" >
                #{createBy},
            </if>
            <if test="createTime != null" >
                #{createTime},
            </if>
            <if test="updateBy != null" >
                #{updateBy},
            </if>
            <if test="updateTime != null" >
                #{updateTime},
            </if>
            <if test="regionDeptId != null" >
                #{regionDeptId}
            </if>
        </trim>
    </insert>

    <!-- 完整插入多条记录-->
    <insert id="insertBatchBySQL" useGeneratedKeys="true" keyProperty="takingId">
        insert into ns_input_stock_taking
        <trim prefix="(" suffix=")" suffixOverrides="," >
            taking_id,
            business_id,
            input_type,
            input_category,
            input_sub_category,
            input_name,
            stock_quantity,
            taking_stock_quantity,
            house_id,
            house_name,
            taking_result,
            warning_status,
            taking_date,
            taking_operator,
            taking_operator_id,
            deleted,
            create_by,
            create_time,
            update_by,
            update_time,
            region_dept_id
        </trim>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides="," >
                #{item.takingId},
                #{item.businessId},
                #{item.inputType},
                #{item.inputCategory},
                #{item.inputSubCategory},
                #{item.inputName},
                #{item.stockQuantity},
                #{item.takingStockQuantity},
                #{item.houseId},
                #{item.houseName},
                #{item.takingResult},
                #{item.warningStatus},
                #{item.takingDate},
                #{item.takingOperator},
                #{item.takingOperatorId},
                #{item.deleted},
                #{item.createBy},
                #{item.createTime},
                #{item.updateBy},
                #{item.updateTime},
                #{item.regionDeptId}
            </trim>
        </foreach>
    </insert>
    <!-- 更新时，为空的字段不更新 -->
    <sql id="Base_Update_Selective_By_PrimaryKey">
        update ns_input_stock_taking
          <set>
            <if test="businessId != null" >
                business_id=#{businessId},
            </if>
            <if test="inputType != null" >
                input_type=#{inputType},
            </if>
            <if test="inputCategory != null" >
                input_category=#{inputCategory},
            </if>
            <if test="inputSubCategory != null" >
                input_sub_category=#{inputSubCategory},
            </if>
            <if test="inputName != null" >
                input_name=#{inputName},
            </if>
            <if test="stockQuantity != null" >
                stock_quantity=#{stockQuantity},
            </if>
            <if test="takingStockQuantity != null" >
                taking_stock_quantity=#{takingStockQuantity},
            </if>
            <if test="houseId != null" >
                house_id=#{houseId},
            </if>
            <if test="houseName != null" >
                house_name=#{houseName},
            </if>
            <if test="takingResult != null" >
                taking_result=#{takingResult},
            </if>
            <if test="warningStatus != null" >
                warning_status=#{warningStatus},
            </if>
            <if test="takingDate != null" >
                taking_date=#{takingDate},
            </if>
            <if test="takingOperator != null" >
                taking_operator=#{takingOperator},
            </if>
            <if test="takingOperatorId != null" >
                taking_operator_id=#{takingOperatorId},
            </if>
            <if test="deleted != null" >
                deleted=#{deleted},
            </if>
            <if test="createBy != null" >
                create_by=#{createBy},
            </if>
            <if test="createTime != null" >
                create_time=#{createTime},
            </if>
            <if test="updateBy != null" >
                update_by=#{updateBy},
            </if>
            <if test="updateTime != null" >
                update_time=#{updateTime},
            </if>
            <if test="regionDeptId != null" >
                region_dept_id=#{regionDeptId},
            </if>
          </set>
         where taking_id = #{takingId}
    </sql>

    <!-- 更新一条记录(为空的字段不操作) -->
    <update id="updateSelectiveByPrimaryKey" useGeneratedKeys="true" keyProperty="takingId">
        <include refid="Base_Update_Selective_By_PrimaryKey" />
    </update>

    <!-- 更新一条记录(为空的字段不操作)WithVersion -->
    <update id="updateSelectiveByPrimaryKeyWithVersion" useGeneratedKeys="true" keyProperty="takingId">
        <include refid="Base_Update_Selective_By_PrimaryKey" />
    </update>

    <!-- 完整更新一条记录 -->
    <sql id="Base_Update_By_PrimaryKey">
        update ns_input_stock_taking
          <set>
                business_id=#{businessId},
                input_type=#{inputType},
                input_category=#{inputCategory},
                input_sub_category=#{inputSubCategory},
                input_name=#{inputName},
                stock_quantity=#{stockQuantity},
                taking_stock_quantity=#{takingStockQuantity},
                house_id=#{houseId},
                house_name=#{houseName},
                taking_result=#{takingResult},
                warning_status=#{warningStatus},
                taking_date=#{takingDate},
                taking_operator=#{takingOperator},
                taking_operator_id=#{takingOperatorId},
                deleted=#{deleted},
                create_by=#{createBy},
                create_time=#{createTime},
                update_by=#{updateBy},
                update_time=#{updateTime},
                region_dept_id=#{regionDeptId},
          </set>
         where taking_id = #{takingId}
    </sql>

    <!-- 完整更新一条记录 -->
    <update id="updateByPrimaryKey" useGeneratedKeys="true" keyProperty="takingId">
        <include refid="Base_Update_By_PrimaryKey" />
    </update>

    <!-- 完整更新一条记录WithVersion -->
    <update id="updateByPrimaryKeyWithVersion" useGeneratedKeys="true" keyProperty="takingId">
        <include refid="Base_Update_By_PrimaryKey" />
    </update>
</mapper>
