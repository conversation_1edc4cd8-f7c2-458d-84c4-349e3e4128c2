<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
 基础 Mapper 表：ns_pesticide_residue_test_item (农残检测子项表)
 该 Mapper 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
-->
<mapper namespace="com.jorchi.business.dao.NsPesticideResidueTestItemDao">
    <!-- 通用查询结果对象-->
    <resultMap id="BaseResultMap" type="com.jorchi.business.po.NsPesticideResidueTestItem">
        <id column="item_id" property="itemId" />
        <result column="test_id" property="testId" />
        <result column="product_stock_id" property="productStockId" />
        <result column="breeds" property="breeds" />
        <result column="cropName" property="cropName" />
        <result column="residue" property="residue" />
        <result column="concentration" property="concentration" />
        <result column="result" property="result" />
        <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列-->
    <sql id="Base_Column_List">
        item_id,
        test_id,
        product_stock_id,
        breeds,
        cropName,
        residue,
        concentration,
        result,
        deleted,
        create_by,
        create_time,
        update_by,
        update_time
    </sql>

    <!-- 按对象查询记录的WHERE部分 -->
    <sql id="Base_Select_By_Entity_Where">
        <if test="itemId != null">
            and item_id = #{itemId}
        </if>
        <if test="testId != null">
            and test_id = #{testId}
        </if>
        <if test="productStockId != null">
            and product_stock_id = #{productStockId}
        </if>
        <if test="breeds != null and breeds != ''">
            and breeds = #{breeds}
        </if>
        <if test="cropName != null and cropName != ''">
            and cropName = #{cropName}
        </if>
        <if test="residue != null">
            and residue = #{residue}
        </if>
        <if test="concentration != null">
            and concentration = #{concentration}
        </if>
        <if test="result != null">
            and result = #{result}
        </if>
        <if test="deleted != null">
            and deleted = #{deleted}
        </if>
        <if test="createBy != null">
            and create_by = #{createBy}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime}
        </if>
        <if test="updateBy != null">
            and update_by = #{updateBy}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime}
        </if>
    </sql>

    <!-- 按对象查询记录的SQL部分 -->
    <sql id="Base_Select_By_Entity">
        select
            <include refid="Base_Column_List" />
        from ns_pesticide_residue_test_item
        <where>
            <include refid="Base_Select_By_Entity_Where" />
        </where>
    </sql>

    <!-- 按主键查询一条记录 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_pesticide_residue_test_item
         where item_id = #{param1}
    </select>

    <sql id="Base_Exists_By_PrimaryKey">
        select count(*)
          from ns_pesticide_residue_test_item
         where item_id = #{param1}
    </sql>

    <!-- 检查对象是否存在，实际按Key查询数据库是否有对应记录 -->
    <select id="exists" resultType="boolean">
        <include refid="Base_Exists_By_PrimaryKey" />
    </select>

    <!-- 检查对象是否存在，实际按Key查询数据库是否有对应记录 -->
    <select id="existsByEntity" resultType="boolean">
        <include refid="Base_Exists_By_PrimaryKey" />
    </select>

    <!-- 按主键List查询多条记录 -->
    <select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_pesticide_residue_test_item
         where item_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 按entity对象List查询多条记录（实际根据对象的主键值查询） -->
    <select id="selectBatch" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_pesticide_residue_test_item
         where item_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.itemId}
        </foreach>
    </select>

    <!-- 按对象查询一页记录（多条记录） -->
    <select id="selectPage" resultMap="BaseResultMap">
        <include refid="Base_Select_By_Entity" />
    </select>

    <!-- 按主键删除一条记录 -->
    <delete id="deleteByPrimaryKey">
        delete from ns_pesticide_residue_test_item
         where item_id = #{param1}
    </delete>

    <!-- 按主键List删除多条记录 -->
    <delete id="deleteBatchByPrimaryKeys">
        delete from ns_pesticide_residue_test_item
         where item_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- 按对象删除一条记录 -->
    <delete id="delete">
        delete from ns_pesticide_residue_test_item
         where item_id = #{itemId}
    </delete>

    <!-- 按对象List删除多条记录 -->
    <delete id="deleteBatch">
        delete from ns_pesticide_residue_test_item
         where item_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.itemId}
        </foreach>
    </delete>

    <!-- 完整插入一条记录-->
    <insert id="insert" useGeneratedKeys="true" keyProperty="itemId">
        insert into ns_pesticide_residue_test_item
        <trim prefix="(" suffix=")" suffixOverrides="," >
            item_id,
            test_id,
            product_stock_id,
            breeds,
            cropName,
            residue,
            concentration,
            result,
            deleted,
            create_by,
            create_time,
            update_by,
            update_time
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            #{itemId},
            #{testId},
            #{productStockId},
            #{breeds},
            #{cropName},
            #{residue},
            #{concentration},
            #{result},
            #{deleted},
            #{createBy},
            #{createTime},
            #{updateBy},
            #{updateTime}
        </trim>
    </insert>

    <!-- 插入一条记录(为空的字段不操作) -->
    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="itemId">
        insert into ns_pesticide_residue_test_item
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="itemId != null" >
                item_id,
            </if>
            <if test="testId != null" >
                test_id,
            </if>
            <if test="productStockId != null" >
                product_stock_id,
            </if>
            <if test="breeds != null" >
                breeds,
            </if>
            <if test="cropName != null" >
                cropName,
            </if>
            <if test="residue != null" >
                residue,
            </if>
            <if test="concentration != null" >
                concentration,
            </if>
            <if test="result != null" >
                result,
            </if>
            <if test="deleted != null" >
                deleted,
            </if>
            <if test="createBy != null" >
                create_by,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateBy != null" >
                update_by,
            </if>
            <if test="updateTime != null" >
                update_time
            </if>
        </trim>
        values <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="itemId != null" >
                #{itemId},
            </if>
            <if test="testId != null" >
                #{testId},
            </if>
            <if test="productStockId != null" >
                #{productStockId},
            </if>
            <if test="breeds != null" >
                #{breeds},
            </if>
            <if test="cropName != null" >
                #{cropName},
            </if>
            <if test="residue != null" >
                #{residue},
            </if>
            <if test="concentration != null" >
                #{concentration},
            </if>
            <if test="result != null" >
                #{result},
            </if>
            <if test="deleted != null" >
                #{deleted},
            </if>
            <if test="createBy != null" >
                #{createBy},
            </if>
            <if test="createTime != null" >
                #{createTime},
            </if>
            <if test="updateBy != null" >
                #{updateBy},
            </if>
            <if test="updateTime != null" >
                #{updateTime}
            </if>
        </trim>
    </insert>

    <!-- 完整插入多条记录-->
    <insert id="insertBatchBySQL" useGeneratedKeys="true" keyProperty="itemId">
        insert into ns_pesticide_residue_test_item
        <trim prefix="(" suffix=")" suffixOverrides="," >
            item_id,
            test_id,
            product_stock_id,
            breeds,
            cropName,
            residue,
            concentration,
            result,
            deleted,
            create_by,
            create_time,
            update_by,
            update_time
        </trim>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides="," >
                #{item.itemId},
                #{item.testId},
                #{item.productStockId},
                #{item.breeds},
                #{item.cropName},
                #{item.residue},
                #{item.concentration},
                #{item.result},
                #{item.deleted},
                #{item.createBy},
                #{item.createTime},
                #{item.updateBy},
                #{item.updateTime}
            </trim>
        </foreach>
    </insert>
    <!-- 更新时，为空的字段不更新 -->
    <sql id="Base_Update_Selective_By_PrimaryKey">
        update ns_pesticide_residue_test_item
          <set>
            <if test="testId != null" >
                test_id=#{testId},
            </if>
            <if test="productStockId != null" >
                product_stock_id=#{productStockId},
            </if>
            <if test="breeds != null" >
                breeds=#{breeds},
            </if>
            <if test="cropName != null" >
                cropName=#{cropName},
            </if>
            <if test="residue != null" >
                residue=#{residue},
            </if>
            <if test="concentration != null" >
                concentration=#{concentration},
            </if>
            <if test="result != null" >
                result=#{result},
            </if>
            <if test="deleted != null" >
                deleted=#{deleted},
            </if>
            <if test="createBy != null" >
                create_by=#{createBy},
            </if>
            <if test="createTime != null" >
                create_time=#{createTime},
            </if>
            <if test="updateBy != null" >
                update_by=#{updateBy},
            </if>
            <if test="updateTime != null" >
                update_time=#{updateTime},
            </if>
          </set>
         where item_id = #{itemId}
    </sql>

    <!-- 更新一条记录(为空的字段不操作) -->
    <update id="updateSelectiveByPrimaryKey" useGeneratedKeys="true" keyProperty="itemId">
        <include refid="Base_Update_Selective_By_PrimaryKey" />
    </update>

    <!-- 更新一条记录(为空的字段不操作)WithVersion -->
    <update id="updateSelectiveByPrimaryKeyWithVersion" useGeneratedKeys="true" keyProperty="itemId">
        <include refid="Base_Update_Selective_By_PrimaryKey" />
    </update>

    <!-- 完整更新一条记录 -->
    <sql id="Base_Update_By_PrimaryKey">
        update ns_pesticide_residue_test_item
          <set>
                test_id=#{testId},
                product_stock_id=#{productStockId},
                breeds=#{breeds},
                cropName=#{cropName},
                residue=#{residue},
                concentration=#{concentration},
                result=#{result},
                deleted=#{deleted},
                create_by=#{createBy},
                create_time=#{createTime},
                update_by=#{updateBy},
                update_time=#{updateTime},
          </set>
         where item_id = #{itemId}
    </sql>

    <!-- 完整更新一条记录 -->
    <update id="updateByPrimaryKey" useGeneratedKeys="true" keyProperty="itemId">
        <include refid="Base_Update_By_PrimaryKey" />
    </update>

    <!-- 完整更新一条记录WithVersion -->
    <update id="updateByPrimaryKeyWithVersion" useGeneratedKeys="true" keyProperty="itemId">
        <include refid="Base_Update_By_PrimaryKey" />
    </update>
</mapper>
