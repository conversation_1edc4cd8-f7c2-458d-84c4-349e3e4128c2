<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
 基础 Mapper 表：ns_house_soil_collect_day_data (大棚土壤传感器采集数据（日表）)
 该 Mapper 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
-->
<mapper namespace="com.jorchi.business.dao.NsHouseSoilCollectDayDataDao">
    <!-- 通用查询结果对象-->
    <resultMap id="BaseResultMap" type="com.jorchi.business.po.NsHouseSoilCollectDayData">
        <id column="soil_id" property="soilId" />
        <result column="house_id" property="houseId" />
        <result column="house_name" property="houseName" />
        <result column="n" property="n" />
        <result column="d" property="d" />
        <result column="t" property="t" />
        <result column="l" property="l" />
        <result column="q" property="q" />
        <result column="E" property="e" />
        <result column="A" property="a" />
        <result column="M" property="m" />
        <result column="ph" property="ph" />
        <result column="next_time" property="nextTime" />
        <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列-->
    <sql id="Base_Column_List">
        soil_id,
        house_id,
        house_name,
        n,
        d,
        t,
        l,
        q,
        E,
        A,
        M,
        ph,
        next_time,
        deleted,
        create_by,
        create_time,
        update_by,
        update_time
    </sql>

    <!-- 按对象查询记录的WHERE部分 -->
    <sql id="Base_Select_By_Entity_Where">
        <if test="soilId != null">
            and soil_id = #{soilId}
        </if>
        <if test="houseId != null">
            and house_id = #{houseId}
        </if>
        <if test="houseName != null and houseName != ''">
            and house_name = #{houseName}
        </if>
        <if test="n != null and n != ''">
            and n = #{n}
        </if>
        <if test="d != null and d != ''">
            and d = #{d}
        </if>
        <if test="t != null">
            and t = #{t}
        </if>
        <if test="l != null">
            and l = #{l}
        </if>
        <if test="q != null">
            and q = #{q}
        </if>
        <if test="e != null">
            and E = #{e}
        </if>
        <if test="a != null">
            and A = #{a}
        </if>
        <if test="m != null">
            and M = #{m}
        </if>
        <if test="ph != null">
            and ph = #{ph}
        </if>
        <if test="nextTime != null">
            and next_time = #{nextTime}
        </if>
        <if test="deleted != null">
            and deleted = #{deleted}
        </if>
        <if test="createBy != null">
            and create_by = #{createBy}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime}
        </if>
        <if test="updateBy != null">
            and update_by = #{updateBy}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime}
        </if>
    </sql>

    <!-- 按对象查询记录的SQL部分 -->
    <sql id="Base_Select_By_Entity">
        select
            <include refid="Base_Column_List" />
        from ns_house_soil_collect_day_data
        <where>
            <include refid="Base_Select_By_Entity_Where" />
        </where>
    </sql>

    <!-- 按主键查询一条记录 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_house_soil_collect_day_data
         where soil_id = #{param1}
    </select>

    <sql id="Base_Exists_By_PrimaryKey">
        select count(*)
          from ns_house_soil_collect_day_data
         where soil_id = #{param1}
    </sql>

    <!-- 检查对象是否存在，实际按Key查询数据库是否有对应记录 -->
    <select id="exists" resultType="boolean">
        <include refid="Base_Exists_By_PrimaryKey" />
    </select>

    <!-- 检查对象是否存在，实际按Key查询数据库是否有对应记录 -->
    <select id="existsByEntity" resultType="boolean">
        <include refid="Base_Exists_By_PrimaryKey" />
    </select>

    <!-- 按主键List查询多条记录 -->
    <select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_house_soil_collect_day_data
         where soil_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 按entity对象List查询多条记录（实际根据对象的主键值查询） -->
    <select id="selectBatch" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_house_soil_collect_day_data
         where soil_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.soilId}
        </foreach>
    </select>

    <!-- 按对象查询一页记录（多条记录） -->
    <select id="selectPage" resultMap="BaseResultMap">
        <include refid="Base_Select_By_Entity" />
    </select>

    <!-- 按主键删除一条记录 -->
    <delete id="deleteByPrimaryKey">
        delete from ns_house_soil_collect_day_data
         where soil_id = #{param1}
    </delete>

    <!-- 按主键List删除多条记录 -->
    <delete id="deleteBatchByPrimaryKeys">
        delete from ns_house_soil_collect_day_data
         where soil_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- 按对象删除一条记录 -->
    <delete id="delete">
        delete from ns_house_soil_collect_day_data
         where soil_id = #{soilId}
    </delete>

    <!-- 按对象List删除多条记录 -->
    <delete id="deleteBatch">
        delete from ns_house_soil_collect_day_data
         where soil_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.soilId}
        </foreach>
    </delete>

    <!-- 完整插入一条记录-->
    <insert id="insert" >
        insert into ns_house_soil_collect_day_data
        <trim prefix="(" suffix=")" suffixOverrides="," >
            soil_id,
            house_id,
            house_name,
            n,
            d,
            t,
            l,
            q,
            E,
            A,
            M,
            ph,
            next_time,
            deleted,
            create_by,
            create_time,
            update_by,
            update_time
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            #{soilId},
            #{houseId},
            #{houseName},
            #{n},
            #{d},
            #{t},
            #{l},
            #{q},
            #{e},
            #{a},
            #{m},
            #{ph},
            #{nextTime},
            #{deleted},
            #{createBy},
            #{createTime},
            #{updateBy},
            #{updateTime}
        </trim>
    </insert>

    <!-- 插入一条记录(为空的字段不操作) -->
    <insert id="insertSelective" >
        insert into ns_house_soil_collect_day_data
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="soilId != null" >
                soil_id,
            </if>
            <if test="houseId != null" >
                house_id,
            </if>
            <if test="houseName != null" >
                house_name,
            </if>
            <if test="n != null" >
                n,
            </if>
            <if test="d != null" >
                d,
            </if>
            <if test="t != null" >
                t,
            </if>
            <if test="l != null" >
                l,
            </if>
            <if test="q != null" >
                q,
            </if>
            <if test="e != null" >
                E,
            </if>
            <if test="a != null" >
                A,
            </if>
            <if test="m != null" >
                M,
            </if>
            <if test="ph != null" >
                ph,
            </if>
            <if test="nextTime != null" >
                next_time,
            </if>
            <if test="deleted != null" >
                deleted,
            </if>
            <if test="createBy != null" >
                create_by,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateBy != null" >
                update_by,
            </if>
            <if test="updateTime != null" >
                update_time
            </if>
        </trim>
        values <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="soilId != null" >
                #{soilId},
            </if>
            <if test="houseId != null" >
                #{houseId},
            </if>
            <if test="houseName != null" >
                #{houseName},
            </if>
            <if test="n != null" >
                #{n},
            </if>
            <if test="d != null" >
                #{d},
            </if>
            <if test="t != null" >
                #{t},
            </if>
            <if test="l != null" >
                #{l},
            </if>
            <if test="q != null" >
                #{q},
            </if>
            <if test="e != null" >
                #{e},
            </if>
            <if test="a != null" >
                #{a},
            </if>
            <if test="m != null" >
                #{m},
            </if>
            <if test="ph != null" >
                #{ph},
            </if>
            <if test="nextTime != null" >
                #{nextTime},
            </if>
            <if test="deleted != null" >
                #{deleted},
            </if>
            <if test="createBy != null" >
                #{createBy},
            </if>
            <if test="createTime != null" >
                #{createTime},
            </if>
            <if test="updateBy != null" >
                #{updateBy},
            </if>
            <if test="updateTime != null" >
                #{updateTime}
            </if>
        </trim>
    </insert>

    <!-- 完整插入多条记录-->
    <insert id="insertBatchBySQL" >
        insert into ns_house_soil_collect_day_data
        <trim prefix="(" suffix=")" suffixOverrides="," >
            soil_id,
            house_id,
            house_name,
            n,
            d,
            t,
            l,
            q,
            E,
            A,
            M,
            ph,
            next_time,
            deleted,
            create_by,
            create_time,
            update_by,
            update_time
        </trim>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides="," >
                #{item.soilId},
                #{item.houseId},
                #{item.houseName},
                #{item.n},
                #{item.d},
                #{item.t},
                #{item.l},
                #{item.q},
                #{item.e},
                #{item.a},
                #{item.m},
                #{item.ph},
                #{item.nextTime},
                #{item.deleted},
                #{item.createBy},
                #{item.createTime},
                #{item.updateBy},
                #{item.updateTime}
            </trim>
        </foreach>
    </insert>
    <!-- 更新时，为空的字段不更新 -->
    <sql id="Base_Update_Selective_By_PrimaryKey">
        update ns_house_soil_collect_day_data
          <set>
            <if test="houseId != null" >
                house_id=#{houseId},
            </if>
            <if test="houseName != null" >
                house_name=#{houseName},
            </if>
            <if test="n != null" >
                n=#{n},
            </if>
            <if test="d != null" >
                d=#{d},
            </if>
            <if test="t != null" >
                t=#{t},
            </if>
            <if test="l != null" >
                l=#{l},
            </if>
            <if test="q != null" >
                q=#{q},
            </if>
            <if test="e != null" >
                E=#{e},
            </if>
            <if test="a != null" >
                A=#{a},
            </if>
            <if test="m != null" >
                M=#{m},
            </if>
            <if test="ph != null" >
                ph=#{ph},
            </if>
            <if test="nextTime != null" >
                next_time=#{nextTime},
            </if>
            <if test="deleted != null" >
                deleted=#{deleted},
            </if>
            <if test="createBy != null" >
                create_by=#{createBy},
            </if>
            <if test="createTime != null" >
                create_time=#{createTime},
            </if>
            <if test="updateBy != null" >
                update_by=#{updateBy},
            </if>
            <if test="updateTime != null" >
                update_time=#{updateTime},
            </if>
          </set>
         where soil_id = #{soilId}
    </sql>

    <!-- 更新一条记录(为空的字段不操作) -->
    <update id="updateSelectiveByPrimaryKey" >
        <include refid="Base_Update_Selective_By_PrimaryKey" />
    </update>

    <!-- 更新一条记录(为空的字段不操作)WithVersion -->
    <update id="updateSelectiveByPrimaryKeyWithVersion" >
        <include refid="Base_Update_Selective_By_PrimaryKey" />
    </update>

    <!-- 完整更新一条记录 -->
    <sql id="Base_Update_By_PrimaryKey">
        update ns_house_soil_collect_day_data
          <set>
                house_id=#{houseId},
                house_name=#{houseName},
                n=#{n},
                d=#{d},
                t=#{t},
                l=#{l},
                q=#{q},
                E=#{e},
                A=#{a},
                M=#{m},
                ph=#{ph},
                next_time=#{nextTime},
                deleted=#{deleted},
                create_by=#{createBy},
                create_time=#{createTime},
                update_by=#{updateBy},
                update_time=#{updateTime},
          </set>
         where soil_id = #{soilId}
    </sql>

    <!-- 完整更新一条记录 -->
    <update id="updateByPrimaryKey" >
        <include refid="Base_Update_By_PrimaryKey" />
    </update>

    <!-- 完整更新一条记录WithVersion -->
    <update id="updateByPrimaryKeyWithVersion" >
        <include refid="Base_Update_By_PrimaryKey" />
    </update>
</mapper>
