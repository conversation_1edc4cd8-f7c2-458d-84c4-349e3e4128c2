<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
 基础 Mapper 表：ns_vacation_transfer (休假移交)
 该 Mapper 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
-->
<mapper namespace="com.jorchi.business.dao.NsVacationTransferDao">
    <!-- 通用查询结果对象-->
    <resultMap id="BaseResultMap" type="com.jorchi.business.po.NsVacationTransfer">
        <id column="vacation_transfer_id" property="vacationTransferId" />
        <result column="production_technicians" property="productionTechnicians" />
        <result column="artisan_id" property="artisanId" />
        <result column="application_time" property="applicationTime" />
        <result column="vacation_start_time" property="vacationStartTime" />
        <result column="vacation_end_time" property="vacationEndTime" />
        <result column="transfer_state" property="transferState" />
        <result column="number_tasks" property="numberTasks" />
        <result column="application_remarks" property="applicationRemarks" />
        <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="region_dept_id" property="regionDeptId" />
    </resultMap>

    <!-- 通用查询结果列-->
    <sql id="Base_Column_List">
        vacation_transfer_id,
        production_technicians,
        artisan_id,
        application_time,
        vacation_start_time,
        vacation_end_time,
        transfer_state,
        number_tasks,
        application_remarks,
        deleted,
        create_by,
        create_time,
        update_by,
        update_time,
        region_dept_id
    </sql>

    <!-- 按对象查询记录的WHERE部分 -->
    <sql id="Base_Select_By_Entity_Where">
        <if test="vacationTransferId != null">
            and vacation_transfer_id = #{vacationTransferId}
        </if>
        <if test="productionTechnicians != null and productionTechnicians != ''">
            and production_technicians = #{productionTechnicians}
        </if>
        <if test="artisanId != null">
            and artisan_id = #{artisanId}
        </if>
        <if test="applicationTime != null">
            and application_time = #{applicationTime}
        </if>
        <if test="vacationStartTime != null">
            and vacation_start_time = #{vacationStartTime}
        </if>
        <if test="vacationEndTime != null">
            and vacation_end_time = #{vacationEndTime}
        </if>
        <if test="transferState != null">
            and transfer_state = #{transferState}
        </if>
        <if test="numberTasks != null">
            and number_tasks = #{numberTasks}
        </if>
        <if test="applicationRemarks != null and applicationRemarks != ''">
            and application_remarks = #{applicationRemarks}
        </if>
        <if test="deleted != null">
            and deleted = #{deleted}
        </if>
        <if test="createBy != null">
            and create_by = #{createBy}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime}
        </if>
        <if test="updateBy != null">
            and update_by = #{updateBy}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime}
        </if>
        <if test="regionDeptId != null">
            and region_dept_id = #{regionDeptId}
        </if>
    </sql>

    <!-- 按对象查询记录的SQL部分 -->
    <sql id="Base_Select_By_Entity">
        select
            <include refid="Base_Column_List" />
        from ns_vacation_transfer
        <where>
            <include refid="Base_Select_By_Entity_Where" />
        </where>
    </sql>

    <!-- 按主键查询一条记录 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_vacation_transfer
         where vacation_transfer_id = #{param1}
    </select>

    <sql id="Base_Exists_By_PrimaryKey">
        select count(*)
          from ns_vacation_transfer
         where vacation_transfer_id = #{param1}
    </sql>

    <!-- 检查对象是否存在，实际按Key查询数据库是否有对应记录 -->
    <select id="exists" resultType="boolean">
        <include refid="Base_Exists_By_PrimaryKey" />
    </select>

    <!-- 检查对象是否存在，实际按Key查询数据库是否有对应记录 -->
    <select id="existsByEntity" resultType="boolean">
        <include refid="Base_Exists_By_PrimaryKey" />
    </select>

    <!-- 按主键List查询多条记录 -->
    <select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_vacation_transfer
         where vacation_transfer_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 按entity对象List查询多条记录（实际根据对象的主键值查询） -->
    <select id="selectBatch" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_vacation_transfer
         where vacation_transfer_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.vacationTransferId}
        </foreach>
    </select>

    <!-- 按对象查询一页记录（多条记录） -->
    <select id="selectPage" resultMap="BaseResultMap">
        <include refid="Base_Select_By_Entity" />
    </select>

    <!-- 按主键删除一条记录 -->
    <delete id="deleteByPrimaryKey">
        delete from ns_vacation_transfer
         where vacation_transfer_id = #{param1}
    </delete>

    <!-- 按主键List删除多条记录 -->
    <delete id="deleteBatchByPrimaryKeys">
        delete from ns_vacation_transfer
         where vacation_transfer_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- 按对象删除一条记录 -->
    <delete id="delete">
        delete from ns_vacation_transfer
         where vacation_transfer_id = #{vacationTransferId}
    </delete>

    <!-- 按对象List删除多条记录 -->
    <delete id="deleteBatch">
        delete from ns_vacation_transfer
         where vacation_transfer_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.vacationTransferId}
        </foreach>
    </delete>

    <!-- 完整插入一条记录-->
    <insert id="insert" >
        insert into ns_vacation_transfer
        <trim prefix="(" suffix=")" suffixOverrides="," >
            vacation_transfer_id,
            production_technicians,
            artisan_id,
            application_time,
            vacation_start_time,
            vacation_end_time,
            transfer_state,
            number_tasks,
            application_remarks,
            deleted,
            create_by,
            create_time,
            update_by,
            update_time,
            region_dept_id
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            #{vacationTransferId},
            #{productionTechnicians},
            #{artisanId},
            #{applicationTime},
            #{vacationStartTime},
            #{vacationEndTime},
            #{transferState},
            #{numberTasks},
            #{applicationRemarks},
            #{deleted},
            #{createBy},
            #{createTime},
            #{updateBy},
            #{updateTime},
            #{regionDeptId}
        </trim>
    </insert>

    <!-- 插入一条记录(为空的字段不操作) -->
    <insert id="insertSelective" >
        insert into ns_vacation_transfer
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="vacationTransferId != null" >
                vacation_transfer_id,
            </if>
            <if test="productionTechnicians != null" >
                production_technicians,
            </if>
            <if test="artisanId != null" >
                artisan_id,
            </if>
            <if test="applicationTime != null" >
                application_time,
            </if>
            <if test="vacationStartTime != null" >
                vacation_start_time,
            </if>
            <if test="vacationEndTime != null" >
                vacation_end_time,
            </if>
            <if test="transferState != null" >
                transfer_state,
            </if>
            <if test="numberTasks != null" >
                number_tasks,
            </if>
            <if test="applicationRemarks != null" >
                application_remarks,
            </if>
            <if test="deleted != null" >
                deleted,
            </if>
            <if test="createBy != null" >
                create_by,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateBy != null" >
                update_by,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
            <if test="regionDeptId != null" >
                region_dept_id
            </if>
        </trim>
        values <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="vacationTransferId != null" >
                #{vacationTransferId},
            </if>
            <if test="productionTechnicians != null" >
                #{productionTechnicians},
            </if>
            <if test="artisanId != null" >
                #{artisanId},
            </if>
            <if test="applicationTime != null" >
                #{applicationTime},
            </if>
            <if test="vacationStartTime != null" >
                #{vacationStartTime},
            </if>
            <if test="vacationEndTime != null" >
                #{vacationEndTime},
            </if>
            <if test="transferState != null" >
                #{transferState},
            </if>
            <if test="numberTasks != null" >
                #{numberTasks},
            </if>
            <if test="applicationRemarks != null" >
                #{applicationRemarks},
            </if>
            <if test="deleted != null" >
                #{deleted},
            </if>
            <if test="createBy != null" >
                #{createBy},
            </if>
            <if test="createTime != null" >
                #{createTime},
            </if>
            <if test="updateBy != null" >
                #{updateBy},
            </if>
            <if test="updateTime != null" >
                #{updateTime},
            </if>
            <if test="regionDeptId != null" >
                #{regionDeptId}
            </if>
        </trim>
    </insert>

    <!-- 完整插入多条记录-->
    <insert id="insertBatchBySQL" >
        insert into ns_vacation_transfer
        <trim prefix="(" suffix=")" suffixOverrides="," >
            vacation_transfer_id,
            production_technicians,
            artisan_id,
            application_time,
            vacation_start_time,
            vacation_end_time,
            transfer_state,
            number_tasks,
            application_remarks,
            deleted,
            create_by,
            create_time,
            update_by,
            update_time,
            region_dept_id
        </trim>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides="," >
                #{item.vacationTransferId},
                #{item.productionTechnicians},
                #{item.artisanId},
                #{item.applicationTime},
                #{item.vacationStartTime},
                #{item.vacationEndTime},
                #{item.transferState},
                #{item.numberTasks},
                #{item.applicationRemarks},
                #{item.deleted},
                #{item.createBy},
                #{item.createTime},
                #{item.updateBy},
                #{item.updateTime},
                #{item.regionDeptId}
            </trim>
        </foreach>
    </insert>
    <!-- 更新时，为空的字段不更新 -->
    <sql id="Base_Update_Selective_By_PrimaryKey">
        update ns_vacation_transfer
          <set>
            <if test="productionTechnicians != null" >
                production_technicians=#{productionTechnicians},
            </if>
            <if test="artisanId != null" >
                artisan_id=#{artisanId},
            </if>
            <if test="applicationTime != null" >
                application_time=#{applicationTime},
            </if>
            <if test="vacationStartTime != null" >
                vacation_start_time=#{vacationStartTime},
            </if>
            <if test="vacationEndTime != null" >
                vacation_end_time=#{vacationEndTime},
            </if>
            <if test="transferState != null" >
                transfer_state=#{transferState},
            </if>
            <if test="numberTasks != null" >
                number_tasks=#{numberTasks},
            </if>
            <if test="applicationRemarks != null" >
                application_remarks=#{applicationRemarks},
            </if>
            <if test="deleted != null" >
                deleted=#{deleted},
            </if>
            <if test="createBy != null" >
                create_by=#{createBy},
            </if>
            <if test="createTime != null" >
                create_time=#{createTime},
            </if>
            <if test="updateBy != null" >
                update_by=#{updateBy},
            </if>
            <if test="updateTime != null" >
                update_time=#{updateTime},
            </if>
            <if test="regionDeptId != null" >
                region_dept_id=#{regionDeptId},
            </if>
          </set>
         where vacation_transfer_id = #{vacationTransferId}
    </sql>

    <!-- 更新一条记录(为空的字段不操作) -->
    <update id="updateSelectiveByPrimaryKey" >
        <include refid="Base_Update_Selective_By_PrimaryKey" />
    </update>

    <!-- 更新一条记录(为空的字段不操作)WithVersion -->
    <update id="updateSelectiveByPrimaryKeyWithVersion" >
        <include refid="Base_Update_Selective_By_PrimaryKey" />
    </update>

    <!-- 完整更新一条记录 -->
    <sql id="Base_Update_By_PrimaryKey">
        update ns_vacation_transfer
          <set>
                production_technicians=#{productionTechnicians},
                artisan_id=#{artisanId},
                application_time=#{applicationTime},
                vacation_start_time=#{vacationStartTime},
                vacation_end_time=#{vacationEndTime},
                transfer_state=#{transferState},
                number_tasks=#{numberTasks},
                application_remarks=#{applicationRemarks},
                deleted=#{deleted},
                create_by=#{createBy},
                create_time=#{createTime},
                update_by=#{updateBy},
                update_time=#{updateTime},
                region_dept_id=#{regionDeptId},
          </set>
         where vacation_transfer_id = #{vacationTransferId}
    </sql>

    <!-- 完整更新一条记录 -->
    <update id="updateByPrimaryKey" >
        <include refid="Base_Update_By_PrimaryKey" />
    </update>

    <!-- 完整更新一条记录WithVersion -->
    <update id="updateByPrimaryKeyWithVersion" >
        <include refid="Base_Update_By_PrimaryKey" />
    </update>
</mapper>
