<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
 基础 Mapper 表：ns_sales_order_item (销售订单子项表)
 该 Mapper 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
-->
<mapper namespace="com.jorchi.business.dao.NsSalesOrderItemDao">
    <!-- 通用查询结果对象-->
    <resultMap id="BaseResultMap" type="com.jorchi.business.po.NsSalesOrderItem">
        <id column="item_id" property="itemId" />
        <result column="order_id" property="orderId" />
        <result column="product_stock_id" property="productStockId" />
        <result column="breeds" property="breeds" />
        <result column="cropName" property="cropName" />
        <result column="unit_price" property="unitPrice" />
        <result column="unit" property="unit" />
        <result column="quantity" property="quantity" />
        <result column="scrap_quantity" property="scrapQuantity" />
        <result column="pay_amount" property="payAmount" />
        <result column="subtotal" property="subtotal" />
        <result column="deleted" property="deleted" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="return_quantity" property="returnQuantity" />
    </resultMap>

    <!-- 通用查询结果列-->
    <sql id="Base_Column_List">
        item_id,
        order_id,
        product_stock_id,
        breeds,
        cropName,
        unit_price,
        unit,
        quantity,
        scrap_quantity,
        pay_amount,
        subtotal,
        deleted,
        create_by,
        create_time,
        update_by,
        update_time,
        return_quantity
    </sql>

    <!-- 按对象查询记录的WHERE部分 -->
    <sql id="Base_Select_By_Entity_Where">
        <if test="itemId != null">
            and item_id = #{itemId}
        </if>
        <if test="orderId != null">
            and order_id = #{orderId}
        </if>
        <if test="productStockId != null">
            and product_stock_id = #{productStockId}
        </if>
        <if test="breeds != null and breeds != ''">
            and breeds = #{breeds}
        </if>
        <if test="cropName != null and cropName != ''">
            and cropName = #{cropName}
        </if>
        <if test="unitPrice != null">
            and unit_price = #{unitPrice}
        </if>
        <if test="unit != null and unit != ''">
            and unit = #{unit}
        </if>
        <if test="quantity != null">
            and quantity = #{quantity}
        </if>
        <if test="scrapQuantity != null">
            and scrap_quantity = #{scrapQuantity}
        </if>
        <if test="payAmount != null">
            and pay_amount = #{payAmount}
        </if>
        <if test="subtotal != null">
            and subtotal = #{subtotal}
        </if>
        <if test="deleted != null">
            and deleted = #{deleted}
        </if>
        <if test="createBy != null">
            and create_by = #{createBy}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime}
        </if>
        <if test="updateBy != null">
            and update_by = #{updateBy}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime}
        </if>
        <if test="returnQuantity != null">
            and return_quantity = #{returnQuantity}
        </if>
    </sql>

    <!-- 按对象查询记录的SQL部分 -->
    <sql id="Base_Select_By_Entity">
        select
            <include refid="Base_Column_List" />
        from ns_sales_order_item
        <where>
            <include refid="Base_Select_By_Entity_Where" />
        </where>
    </sql>

    <!-- 按主键查询一条记录 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_sales_order_item
         where item_id = #{param1}
    </select>

    <sql id="Base_Exists_By_PrimaryKey">
        select count(*)
          from ns_sales_order_item
         where item_id = #{param1}
    </sql>

    <!-- 检查对象是否存在，实际按Key查询数据库是否有对应记录 -->
    <select id="exists" resultType="boolean">
        <include refid="Base_Exists_By_PrimaryKey" />
    </select>

    <!-- 检查对象是否存在，实际按Key查询数据库是否有对应记录 -->
    <select id="existsByEntity" resultType="boolean">
        <include refid="Base_Exists_By_PrimaryKey" />
    </select>

    <!-- 按主键List查询多条记录 -->
    <select id="selectBatchByPrimaryKeys" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_sales_order_item
         where item_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 按entity对象List查询多条记录（实际根据对象的主键值查询） -->
    <select id="selectBatch" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List" />
          from ns_sales_order_item
         where item_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.itemId}
        </foreach>
    </select>

    <!-- 按对象查询一页记录（多条记录） -->
    <select id="selectPage" resultMap="BaseResultMap">
        <include refid="Base_Select_By_Entity" />
    </select>

    <!-- 按主键删除一条记录 -->
    <delete id="deleteByPrimaryKey">
        delete from ns_sales_order_item
         where item_id = #{param1}
    </delete>

    <!-- 按主键List删除多条记录 -->
    <delete id="deleteBatchByPrimaryKeys">
        delete from ns_sales_order_item
         where item_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <!-- 按对象删除一条记录 -->
    <delete id="delete">
        delete from ns_sales_order_item
         where item_id = #{itemId}
    </delete>

    <!-- 按对象List删除多条记录 -->
    <delete id="deleteBatch">
        delete from ns_sales_order_item
         where item_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item.itemId}
        </foreach>
    </delete>

    <!-- 完整插入一条记录-->
    <insert id="insert" useGeneratedKeys="true" keyProperty="itemId">
        insert into ns_sales_order_item
        <trim prefix="(" suffix=")" suffixOverrides="," >
            item_id,
            order_id,
            product_stock_id,
            breeds,
            cropName,
            unit_price,
            unit,
            quantity,
            scrap_quantity,
            pay_amount,
            subtotal,
            deleted,
            create_by,
            create_time,
            update_by,
            update_time,
            return_quantity
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides="," >
            #{itemId},
            #{orderId},
            #{productStockId},
            #{breeds},
            #{cropName},
            #{unitPrice},
            #{unit},
            #{quantity},
            #{scrapQuantity},
            #{payAmount},
            #{subtotal},
            #{deleted},
            #{createBy},
            #{createTime},
            #{updateBy},
            #{updateTime},
            #{returnQuantity}
        </trim>
    </insert>

    <!-- 插入一条记录(为空的字段不操作) -->
    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="itemId">
        insert into ns_sales_order_item
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="itemId != null" >
                item_id,
            </if>
            <if test="orderId != null" >
                order_id,
            </if>
            <if test="productStockId != null" >
                product_stock_id,
            </if>
            <if test="breeds != null" >
                breeds,
            </if>
            <if test="cropName != null" >
                cropName,
            </if>
            <if test="unitPrice != null" >
                unit_price,
            </if>
            <if test="unit != null" >
                unit,
            </if>
            <if test="quantity != null" >
                quantity,
            </if>
            <if test="scrapQuantity != null" >
                scrap_quantity,
            </if>
            <if test="payAmount != null" >
                pay_amount,
            </if>
            <if test="subtotal != null" >
                subtotal,
            </if>
            <if test="deleted != null" >
                deleted,
            </if>
            <if test="createBy != null" >
                create_by,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="updateBy != null" >
                update_by,
            </if>
            <if test="updateTime != null" >
                update_time,
            </if>
            <if test="returnQuantity != null" >
                return_quantity
            </if>
        </trim>
        values <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="itemId != null" >
                #{itemId},
            </if>
            <if test="orderId != null" >
                #{orderId},
            </if>
            <if test="productStockId != null" >
                #{productStockId},
            </if>
            <if test="breeds != null" >
                #{breeds},
            </if>
            <if test="cropName != null" >
                #{cropName},
            </if>
            <if test="unitPrice != null" >
                #{unitPrice},
            </if>
            <if test="unit != null" >
                #{unit},
            </if>
            <if test="quantity != null" >
                #{quantity},
            </if>
            <if test="scrapQuantity != null" >
                #{scrapQuantity},
            </if>
            <if test="payAmount != null" >
                #{payAmount},
            </if>
            <if test="subtotal != null" >
                #{subtotal},
            </if>
            <if test="deleted != null" >
                #{deleted},
            </if>
            <if test="createBy != null" >
                #{createBy},
            </if>
            <if test="createTime != null" >
                #{createTime},
            </if>
            <if test="updateBy != null" >
                #{updateBy},
            </if>
            <if test="updateTime != null" >
                #{updateTime},
            </if>
            <if test="returnQuantity != null" >
                #{returnQuantity}
            </if>
        </trim>
    </insert>

    <!-- 完整插入多条记录-->
    <insert id="insertBatchBySQL" useGeneratedKeys="true" keyProperty="itemId">
        insert into ns_sales_order_item
        <trim prefix="(" suffix=")" suffixOverrides="," >
            item_id,
            order_id,
            product_stock_id,
            breeds,
            cropName,
            unit_price,
            unit,
            quantity,
            scrap_quantity,
            pay_amount,
            subtotal,
            deleted,
            create_by,
            create_time,
            update_by,
            update_time,
            return_quantity
        </trim>
        values
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides="," >
                #{item.itemId},
                #{item.orderId},
                #{item.productStockId},
                #{item.breeds},
                #{item.cropName},
                #{item.unitPrice},
                #{item.unit},
                #{item.quantity},
                #{item.scrapQuantity},
                #{item.payAmount},
                #{item.subtotal},
                #{item.deleted},
                #{item.createBy},
                #{item.createTime},
                #{item.updateBy},
                #{item.updateTime},
                #{item.returnQuantity}
            </trim>
        </foreach>
    </insert>
    <!-- 更新时，为空的字段不更新 -->
    <sql id="Base_Update_Selective_By_PrimaryKey">
        update ns_sales_order_item
          <set>
            <if test="orderId != null" >
                order_id=#{orderId},
            </if>
            <if test="productStockId != null" >
                product_stock_id=#{productStockId},
            </if>
            <if test="breeds != null" >
                breeds=#{breeds},
            </if>
            <if test="cropName != null" >
                cropName=#{cropName},
            </if>
            <if test="unitPrice != null" >
                unit_price=#{unitPrice},
            </if>
            <if test="unit != null" >
                unit=#{unit},
            </if>
            <if test="quantity != null" >
                quantity=#{quantity},
            </if>
            <if test="scrapQuantity != null" >
                scrap_quantity=#{scrapQuantity},
            </if>
            <if test="payAmount != null" >
                pay_amount=#{payAmount},
            </if>
            <if test="subtotal != null" >
                subtotal=#{subtotal},
            </if>
            <if test="deleted != null" >
                deleted=#{deleted},
            </if>
            <if test="createBy != null" >
                create_by=#{createBy},
            </if>
            <if test="createTime != null" >
                create_time=#{createTime},
            </if>
            <if test="updateBy != null" >
                update_by=#{updateBy},
            </if>
            <if test="updateTime != null" >
                update_time=#{updateTime},
            </if>
            <if test="returnQuantity != null" >
                return_quantity=#{returnQuantity},
            </if>
          </set>
         where item_id = #{itemId}
    </sql>

    <!-- 更新一条记录(为空的字段不操作) -->
    <update id="updateSelectiveByPrimaryKey" useGeneratedKeys="true" keyProperty="itemId">
        <include refid="Base_Update_Selective_By_PrimaryKey" />
    </update>

    <!-- 更新一条记录(为空的字段不操作)WithVersion -->
    <update id="updateSelectiveByPrimaryKeyWithVersion" useGeneratedKeys="true" keyProperty="itemId">
        <include refid="Base_Update_Selective_By_PrimaryKey" />
    </update>

    <!-- 完整更新一条记录 -->
    <sql id="Base_Update_By_PrimaryKey">
        update ns_sales_order_item
          <set>
                order_id=#{orderId},
                product_stock_id=#{productStockId},
                breeds=#{breeds},
                cropName=#{cropName},
                unit_price=#{unitPrice},
                unit=#{unit},
                quantity=#{quantity},
                scrap_quantity=#{scrapQuantity},
                pay_amount=#{payAmount},
                subtotal=#{subtotal},
                deleted=#{deleted},
                create_by=#{createBy},
                create_time=#{createTime},
                update_by=#{updateBy},
                update_time=#{updateTime},
                return_quantity=#{returnQuantity},
          </set>
         where item_id = #{itemId}
    </sql>

    <!-- 完整更新一条记录 -->
    <update id="updateByPrimaryKey" useGeneratedKeys="true" keyProperty="itemId">
        <include refid="Base_Update_By_PrimaryKey" />
    </update>

    <!-- 完整更新一条记录WithVersion -->
    <update id="updateByPrimaryKeyWithVersion" useGeneratedKeys="true" keyProperty="itemId">
        <include refid="Base_Update_By_PrimaryKey" />
    </update>
</mapper>
