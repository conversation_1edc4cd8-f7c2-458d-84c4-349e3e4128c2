<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<!--
 自定义配置 Mapper 表：ns_input_stock (投入品库存表)
 另基础 Mapper 请参考：base/business/NsInputStockBaseDao.xml
-->
<mapper namespace="com.jorchi.business.dao.NsInputStockDao">

    <!-- 按Form对象查询记录的WHERE部分 -->
    <sql id="Base_Select_By_Entity_Where_By_Form">
        <if test="inputId != null">
            and input_id = #{inputId}
        </if>
        <if test="inputType !=null ">
            and input_type = #{inputType}
        </if>
        <if test="inputCategory != null and inputCategory != ''">
            and input_category like concat('%', #{inputCategory}, '%')
        </if>
        <if test="inputSubCategory != null and inputSubCategory != ''">
            and input_sub_category like concat('%', #{inputSubCategory}, '%')
        </if>
        <if test="businessId!=null and businessId != ''">
            and business_id = #{businessId}
        </if>
        <if test="inputName != null and inputName != ''">
            and input_name like concat('%', #{inputName}, '%')
        </if>
        <if test="stockWarningLine != null">
            and stock_warning_line = #{stockWarningLine}
        </if>
        <if test="stockQuantity != null">
            and stock_quantity = #{stockQuantity}
        </if>
        <if test="warningStatus != null and warningStatus != ''">
            and warning_status like concat('%', #{warningStatus}, '%')
        </if>
        <if test="deleted != null">
            and deleted = #{deleted}
        </if>
        <if test="createBy != null">
            and create_by = #{createBy}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime}
        </if>
        <if test="createTimeFrom != null">
            and create_time >= #{createTimeFrom}
        </if>
        <if test="createTimeTo != null">
            <![CDATA[
            and create_time <= #{createTimeTo}
            ]]>
        </if>
        <if test="updateBy != null">
            and update_by = #{updateBy}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime}
        </if>
        <if test="updateTimeFrom != null">
            and update_time >= #{updateTimeFrom}
        </if>
        <if test="updateTimeTo != null">
            <![CDATA[
            and update_time <= #{updateTimeTo}
            ]]>
        </if>
                <if test="regionDeptId !=null">
            and region_dept_id = #{regionDeptId}
        </if>
        and stock_quantity != 0
        and deleted = 0

    </sql>

    <!-- 按Form对象查询一页记录（多条记录） -->
    <select id="selectPageByForm" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
          from ns_input_stock
        <where>
            <include refid="Base_Select_By_Entity_Where_By_Form" />
        </where>
    </select>

    <!-- 请在此处添加其它自定义配置-->
    <!-- 从 { type->[businessIds] } 中遍历查询投入品 -->
    <select id="selectByBusinessIds" resultType="com.jorchi.business.po.NsInputStock">
        select <include refid="Base_Column_List" />
            from ns_input_stock
        where deleted = 0
        <if test="dtos != null and dtos.size() > 0">
            and    <foreach collection="dtos" item="item" index="index" separator=" OR " >
            (
                input_type = #{item.type}
                and business_id in
                <foreach collection="item.businessIds" item="businessId" index="index" open="(" separator="," close=")">
                    #{businessId}
                </foreach>
            )
        </foreach>

        </if>
    </select>
    <select id="selectByBusinessIdsWithLock" resultType="com.jorchi.business.po.NsInputStock">
        select
        <include refid="Base_Column_List"/>
        from ns_input_stock
        where
        deleted = 0

        <if test="dtos != null and dtos.size() > 0">
        and
        <foreach collection="dtos" item="item" index="index" separator=" OR ">
            (
            input_type = #{item.type}
            and business_id in
            <foreach collection="item.businessIds" item="businessId" index="index" open="(" separator="," close=")">
                #{businessId}
            </foreach>
            )
        </foreach>
        </if>
        for update
    </select>
</mapper>
