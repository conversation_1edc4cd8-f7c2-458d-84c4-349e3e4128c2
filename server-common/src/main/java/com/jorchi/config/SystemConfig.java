package com.jorchi.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 配置文件
 * 在过滤器中使用 @Value 时为null，所以在这里使用的一个单例对象来存储所有的配置信息
 * @date: 2022/4/14
 */
@Component
public class SystemConfig {

    /** 获取当前单例对象 */
    public static SystemConfig INSTANCE;

    // 当前配置是生产还是测试 prod 生产环境 dev 测试环境
    @Value("${spring.profiles.active}")
    public String PROFILES_ACTIVE;



    @Value("${system.rsa.publicKey}")
    public String publicKey;
    @Value("${system.rsa.privateKey}")
    public String privateKey;


    /**
     * 加密密钥第1段
     * 每个项目请随机更改该值
     */
    /** 密码第1段，加密码传输 */
    @Value("${system.webRcHead}")
    public String webRcHead;

    /**
     * 加密密钥第2段
     * 每个项目请随机更改该值
     */
    @Value("${system.webRcPwd}")
    public String webRcPwd;


    // public static String FILE_DIR = "/usr/local/tomcat-8081/";

    @PostConstruct
    public void init() {
        INSTANCE = this;
        // FILE_DIR = fileDir;
    }

    /** 是否生产环境 */
    public boolean isProd() {
        return PROFILES_ACTIVE.equals("prod");
    }

    /** 是否开发环境 */
    public boolean isDev() {
        return PROFILES_ACTIVE.equals("dev");
    }
}
