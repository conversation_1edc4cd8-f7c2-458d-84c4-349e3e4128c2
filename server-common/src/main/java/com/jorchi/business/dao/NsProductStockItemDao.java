package com.jorchi.business.dao;

import com.github.pagehelper.Page;
import com.jorchi.business.form.NsProductStockItemForm;
import org.apache.ibatis.annotations.Mapper;

import com.jorchi.business.po.NsProductStockItem;
import com.jorchi.common.MybatisBaseDao;

/**
 * 表ns_product_stock_item对应的基于MyBatis实现的自定义Dao接口<br/>
 * @author: 周建宇 at jorchi
 * @date: 2025-06-14 16:04:47
 */
@Mapper
public interface NsProductStockItemDao extends MybatisBaseDao<NsProductStockItem, Long> {

    /** 根据 Vo 查询 */
    Page<NsProductStockItem> selectPage(NsProductStockItem entity);

    /** 根据 Form 查询 */
    Page<NsProductStockItem> selectPageByForm(NsProductStockItemForm entity);


    // 在此添加其它自定义方法 ...
}
