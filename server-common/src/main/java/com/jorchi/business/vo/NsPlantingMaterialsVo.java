package com.jorchi.business.vo;

import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import com.jorchi.framework.web.page.PageDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * NsPlantingMaterialsVo对象。对应实体描述：种植项物料表
 * 注意：@Excel中的 name 用于 excel 导入导出时与实体对象字段名相映射
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NsPlantingMaterialsVo extends PageDomain implements Serializable {
    private static final long serialVersionUID = 1L;

    @Excel(name = "planting_materials_id", description = "种植项物料ID")
    private Long plantingMaterialsId;

    @Excel(name = "planting_clause_id", description = "种植项ID")
    private Long plantingClauseId;

    @Excel(name = "brand_name", description = "品牌名称")
    private String brandName;

    @Excel(name = "active_ingredient", description = "投入品名称")
    private String activeIngredient;

    @Excel(name = "ratio", description = "占比")
    private BigDecimal ratio;

    @Excel(name = "use_method", description = "使用方法")
    private String useMethod;

    @Excel(name = "remark", description = "备注")
    private String remark;

    @Excel(name = "deleted", description = "删除标志（0代表存在2代表删除）")
    private Integer deleted;

    @Excel(name = "create_by", description = "创建人")
    private Long createBy;

    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;

    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;

    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;

}
