package com.jorchi.business.vo;

import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.io.Serializable;
import java.util.Date;
import com.jorchi.framework.web.page.PageDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * NsSpecVo对象。对应实体描述：规格表
 * 注意：@Excel中的 name 用于 excel 导入导出时与实体对象字段名相映射
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NsSpecVo extends PageDomain implements Serializable {
    private static final long serialVersionUID = 1L;

    @Excel(name = "spec_id", description = "规格ID")
    private Long specId;

    @Excel(name = "spec_type", description = "规格类型:1-投入品,2-农产品")
    private Integer specType;

    @Excel(name = "input_type", description = "投入品类型：2=肥料，3=植保剂，4=种子，5=农膜，6=其他")
    private Integer inputType;

    @Excel(name = "product_stock_id", description = "农产品ID")
    private Long productStockId;

    @Excel(name = "name", description = "农产品或者投入品名称")
    private String name;

    @Excel(name = "unit", description = "单位，例如kg")
    private String unit;

    @Excel(name = "package_unit", description = "打包单位,例如袋")
    private String packageUnit;

    @Excel(name = "mark", description = "规格说明")
    private String mark;

    @Excel(name = "deleted", description = "删除标志")
    private Integer deleted;

    @Excel(name = "create_by", description = "创建人")
    private Long createBy;

    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;

    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;

    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;

    @Excel(name = "region_dept_id", description = "农场id")
    private Long regionDeptId;

}
