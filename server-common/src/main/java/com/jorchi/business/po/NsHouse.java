package com.jorchi.business.po;

import java.io.Serializable;
import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表ns_house的PO对象<br/>
 * 对应表名：ns_house，备注：大棚
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "ns_house")
public class NsHouse implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 大棚ID */
    @Excel(name = "house_id", description = "大棚ID")
    private Long houseId;
    /** 大棚编号 */
    @Excel(name = "house_name", description = "大棚编号")
    private String houseName;
    /** 区块ID */
    @Excel(name = "house_region_id", description = "区块ID")
    private Long houseRegionId;
    /** 区块名称 */
    @Excel(name = "house_region_name", description = "区块名称")
    private String houseRegionName;
    /** 农场ID */
    @Excel(name = "region_dept_id", description = "农场ID")
    private Long regionDeptId;
    /** 农场名称 */
    @Excel(name = "region_dept_name", description = "农场名称")
    private String regionDeptName;
    /** 大棚面积 */
    @Excel(name = "house_area", description = "大棚面积")
    private String houseArea;
    /** 生产技术人员ID */
    @Excel(name = "technician_id", description = "生产技术人员ID")
    private Long technicianId;
    /** 生产技术人员姓名 */
    @Excel(name = "technician_name", description = "生产技术人员姓名")
    private String technicianName;
    /** 代理人ID */
    @Excel(name = "technician_agent_id", description = "代理人ID")
    private Long technicianAgentId;
    /** 代理人名称 */
    @Excel(name = "technician_agent_name", description = "代理人名称")
    private String technicianAgentName;
    /** 区域坐标点 */
    @Excel(name = "house_points", description = "区域坐标点")
    private String housePoints;
    /** 是否有种植物 */
    @Excel(name = "is_plant", description = "是否有种植物")
    private Integer isPlant;
    /** 流程阶段 */
    @Excel(name = "process_stage", description = "流程阶段")
    private Long processStage;
    /** 删除标志（0代表存在2代表删除） */
    @Excel(name = "deleted", description = "删除标志（0代表存在2代表删除）")
    private Integer deleted;
    /** 创建人 */
    @Excel(name = "create_by", description = "创建人")
    private Long createBy;
    /** 创建时间 */
    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;
    /** 更新人 */
    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;
    /** 更新时间 */
    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;

    // 上次种植
    private String lastTime;

    // 本次种植
    private String thisTime;

    // 本次采收时间

    private String plantDate;

    private String plantId;

    /** 二维码 */
    @Excel(name = "image_path", description = "二维码")
    private String imagePath;

    /** 当前大棚颜色值(手动添加)*/
    private String color;

    /** 当前大棚状态（手动添加）*/
    private String name;

    /**环境湿度 (手动添加)*/
    private String q;

    /** 环境温度（手动添加）*/
    private String t;

    /** 最近采集时间（手动添加）*/
    private String d;

}
