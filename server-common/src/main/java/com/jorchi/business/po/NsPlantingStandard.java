package com.jorchi.business.po;

import java.io.Serializable;
import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表ns_planting_standard的PO对象<br/>
 * 对应表名：ns_planting_standard，备注：种植标准
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "ns_planting_standard")
public class NsPlantingStandard implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 种植标准主键 */
    @Excel(name = "planting_standard_id", description = "种植标准主键")
    private Long plantingStandardId;
    /** 种植标准名称 */
    @Excel(name = "standard_name", description = "种植标准名称")
    private String standardName;
    /** 作物名称 */
    @Excel(name = "crop_name", description = "作物名称")
    private String cropName;
    /** 品种 */
    @Excel(name = "breeds", description = "品种")
    private String breeds;
    /** 种植模式 */
    @Excel(name = "cropping_pattern", description = "种植模式")
    private String croppingPattern;
    /** 删除标志 */
    @Excel(name = "deleted", description = "删除标志")
    private Integer deleted;
    /** 创建人 */
    @Excel(name = "create_by", description = "创建人")
    private Long createBy;
    /** 创建时间 */
    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;
    /** 更新人 */
    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;
    /** 更新时间 */
    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;
    /** 农场id */
    @Excel(name = "region_dept_id", description = "农场id")
    private Long regionDeptId;
}
