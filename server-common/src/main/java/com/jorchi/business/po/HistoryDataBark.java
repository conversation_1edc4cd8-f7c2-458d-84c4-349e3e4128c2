package com.jorchi.business.po;

import java.io.Serializable;
import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表history_data_bark的PO对象<br/>
 * 对应表名：history_data_bark，备注：设备数据历史表备份记录表
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "history_data_bark")
public class HistoryDataBark implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 备份表主键 */
    @Excel(name = "id", description = "备份表主键")
    private Long id;
    /** 备份表名称 */
    @Excel(name = "db_name", description = "备份表名称")
    private String dbName;
    /** 备份开始日期 */
    @Excel(name = "start_time", description = "备份开始日期")
    private Date startTime;
    /** 备份结束日期 */
    @Excel(name = "stop_time", description = "备份结束日期")
    private Date stopTime;
}
