package com.jorchi.business.po;

import java.io.Serializable;
import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表ns_factory_inspection的PO对象<br/>
 * 对应表名：ns_factory_inspection，备注：出厂检验表
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "ns_factory_inspection")
public class NsFactoryInspection implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 检验记录ID */
    @Excel(name = "inspection_id", description = "检验记录ID")
    private Long inspectionId;
    /** 检验人 */
    @Excel(name = "operator", description = "检验人")
    private String operator;
    /** 检验人id */
    @Excel(name = "operator_id", description = "检验人id")
    private Long operatorId;
    /** 关联生产计划 */
    @Excel(name = "production_plan_id", description = "关联生产计划")
    private Long productionPlanId;
    /** 生产计划编号 */
    @Excel(name = "production_plan_code", description = "生产计划编号")
    private String productionPlanCode;
    /** 检验日期 */
    @Excel(name = "inspection_date", description = "检验日期")
    private Date inspectionDate;
    /** 抽样数量 */
    @Excel(name = "sample_quantity", description = "抽样数量")
    private Integer sampleQuantity;
    /** 农产品id */
    @Excel(name = "product_stock_id", description = "农产品id")
    private Long productStockId;
    /** 品种 */
    @Excel(name = "breeds", description = "品种")
    private String breeds;
    /** 农产品名称 */
    @Excel(name = "crop_name", description = "农产品名称")
    private String cropName;
    /** 规格 */
    @Excel(name = "specification", description = "规格")
    private String specification;
    /** 记录编号 */
    @Excel(name = "record_number", description = "记录编号")
    private String recordNumber;
    /** 产品收货批号 */
    @Excel(name = "product_batch_number", description = "产品收货批号")
    private String productBatchNumber;
    /** 车辆运输编号 */
    @Excel(name = "vehicle_transport_number", description = "车辆运输编号")
    private String vehicleTransportNumber;
    /** 检验内容 */
    @Excel(name = "inspection_content", description = "检验内容")
    private String inspectionContent;
    /** 结果：1-及格，2-不及格 */
    @Excel(name = "result", description = "结果：1-及格，2-不及格")
    private Integer result;
    /** 删除标志（0代表存在2代表删除） */
    @Excel(name = "deleted", description = "删除标志（0代表存在2代表删除）")
    private Integer deleted;
    /** 创建人 */
    @Excel(name = "create_by", description = "创建人")
    private Long createBy;
    /** 创建时间 */
    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;
    /** 更新人 */
    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;
    /** 更新时间 */
    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;
    /** 农场id */
    @Excel(name = "region_dept_id", description = "农场id")
    private Long regionDeptId;
    /** 备注 */
    @Excel(name = "remark", description = "备注")
    private String remark;
}
