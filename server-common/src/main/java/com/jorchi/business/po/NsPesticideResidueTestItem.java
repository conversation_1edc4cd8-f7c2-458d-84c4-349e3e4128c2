package com.jorchi.business.po;

import java.io.Serializable;
import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.util.Date;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表ns_pesticide_residue_test_item的PO对象<br/>
 * 对应表名：ns_pesticide_residue_test_item，备注：农残检测子项表
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "ns_pesticide_residue_test_item")
public class NsPesticideResidueTestItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 农残检测子项id */
    @Excel(name = "item_id", description = "农残检测子项id")
    private Long itemId;
    /** 检测id，关联农残检测表的检测id */
    @Excel(name = "test_id", description = "检测id，关联农残检测表的检测id")
    private Long testId;
    /** 农产品ID */
    @Excel(name = "product_stock_id", description = "农产品ID")
    private Long productStockId;
    /** 品种 */
    @Excel(name = "breeds", description = "品种")
    private String breeds;
    /** 农产品名称 */
    @Excel(name = "cropName", description = "农产品名称")
    private String cropName;
    /** 农残抑制率 */
    @Excel(name = "residue", description = "农残抑制率")
    private BigDecimal residue;
    /** 农残浓度 */
    @Excel(name = "concentration", description = "农残浓度")
    private BigDecimal concentration;
    /** 检测结果,1-及格，2-不及格 */
    @Excel(name = "result", description = "检测结果,1-及格，2-不及格")
    private Integer result;
    /** 删除标志（0代表存在，2代表删除） */
    @Excel(name = "deleted", description = "删除标志（0代表存在，2代表删除）")
    private Integer deleted;
    /** 创建人 */
    @Excel(name = "create_by", description = "创建人")
    private Long createBy;
    /** 创建时间 */
    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;
    /** 更新人 */
    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;
    /** 更新时间 */
    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;
}
