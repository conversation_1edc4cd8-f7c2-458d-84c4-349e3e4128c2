package com.jorchi.business.po;

import java.io.Serializable;
import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.util.Date;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表ns_semi_product_inbound的PO对象<br/>
 * 对应表名：ns_semi_product_inbound，备注：半成品入库表
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "ns_semi_product_inbound")
public class NsSemiProductInbound implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 入库ID */
    @Excel(name = "inbound_id", description = "入库ID")
    private Long inboundId;
    /** 入库ID */
    @Excel(name = "warehouse_id", description = "入库ID")
    private Long warehouseId;
    /** 采摘人 */
    @Excel(name = "picker", description = "采摘人")
    private Long picker;
    /** 采摘人姓名 */
    @Excel(name = "picker_name", description = "采摘人姓名")
    private String pickerName;
    /** 采摘日期 */
    @Excel(name = "pick_date", description = "采摘日期")
    private Date pickDate;
    /** 来源大棚 */
    @Excel(name = "house_id", description = "来源大棚")
    private Long houseId;
    /** 生产计划 */
    @Excel(name = "production_plan_id", description = "生产计划")
    private Long productionPlanId;
    /** 生产计划编号 */
    @Excel(name = "production_plan_code", description = "生产计划编号")
    private String productionPlanCode;
    /** 实际入库量 */
    @Excel(name = "actual_quantity", description = "实际入库量")
    private BigDecimal actualQuantity;
    /** 是否已转换成品 */
    @Excel(name = "converted", description = "是否已转换成品")
    private Boolean converted;
    /** 删除标志 */
    @Excel(name = "deleted", description = "删除标志")
    private Integer deleted;
    /** 创建人 */
    @Excel(name = "create_by", description = "创建人")
    private Long createBy;
    /** 创建时间 */
    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;
    /** 更新人 */
    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;
    /** 更新时间 */
    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;
    /** 农产品类型 */
    @Excel(name = "crop_type", description = "农产品类型")
    private String cropType;
    /** 农产品名称 */
    @Excel(name = "crop_name", description = "农产品名称")
    private String cropName;
    /** 农场id */
    @Excel(name = "region_dept_id", description = "农场id")
    private Long regionDeptId;
    /** 称重内容 */
    @Excel(name = "items", description = "称重内容")
    private String items;
}
