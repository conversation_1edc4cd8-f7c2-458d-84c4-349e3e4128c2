package com.jorchi.business.po;

import java.io.Serializable;
import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表ns_pesticide_residue_test的PO对象<br/>
 * 对应表名：ns_pesticide_residue_test，备注：农残检测表
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "ns_pesticide_residue_test")
public class NsPesticideResidueTest implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 检测id */
    @Excel(name = "test_id", description = "检测id")
    private Long testId;
    /** 检查人 */
    @Excel(name = "inspector", description = "检查人")
    private String inspector;
    /** 检查人id */
    @Excel(name = "inspector_id", description = "检查人id")
    private Long inspectorId;
    /** 检测时间 */
    @Excel(name = "test_time", description = "检测时间")
    private Date testTime;
    /** 销售订单id，关联销售订单表的订单id */
    @Excel(name = "sales_order_id", description = "销售订单id，关联销售订单表的订单id")
    private Long salesOrderId;
    /** 销售订单号，关联销售订单表的订单号 */
    @Excel(name = "sales_order_code", description = "销售订单号，关联销售订单表的订单号")
    private String salesOrderCode;
    /** 删除标志（0代表存在，2代表删除） */
    @Excel(name = "deleted", description = "删除标志（0代表存在，2代表删除）")
    private Integer deleted;
    /** 创建人 */
    @Excel(name = "create_by", description = "创建人")
    private Long createBy;
    /** 创建时间 */
    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;
    /** 更新人 */
    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;
    /** 更新时间 */
    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;
    /** 农场id */
    @Excel(name = "region_dept_id", description = "农场id")
    private Long regionDeptId;
    /** 备注 */
    @Excel(name = "remark", description = "备注")
    private String remark;
    /** 生产计划id */
    @Excel(name = "production_plan_id", description = "生产计划id")
    private Long productionPlanId;
    /** 生产计划编号 */
    @Excel(name = "production_plan_code", description = "生产计划编号")
    private String productionPlanCode;
}
