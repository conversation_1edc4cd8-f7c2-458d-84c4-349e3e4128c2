package com.jorchi.business.po;

import java.io.Serializable;
import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表ns_device的PO对象<br/>
 * 对应表名：ns_device，备注：农场设备表
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "ns_device")
public class NsDevice implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 设备id */
    @Excel(name = "device_id", description = "设备id")
    private Long deviceId;
    /** 农场ID */
    @Excel(name = "dept_id", description = "农场ID")
    private Long deptId;
    /** 附件 */
    @Excel(name = "file", description = "附件")
    private String file;
    /** 设备编号 */
    @Excel(name = "device_code", description = "设备编号")
    private String deviceCode;
    /** 删除标志 */
    @Excel(name = "deleted", description = "删除标志")
    private Integer deleted;
    /** 创建人 */
    @Excel(name = "create_by", description = "创建人")
    private Long createBy;
    /** 创建时间 */
    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;
    /** 更新人 */
    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;
    /** 更新时间 */
    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;
}
