package com.jorchi.business.po;

import java.io.Serializable;
import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表ns_person_union的PO对象<br/>
 * 对应表名：ns_person_union，备注：技术人员、代理人员关联表
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "ns_person_union")
public class NsPersonUnion implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 联合表主键 */
    @Excel(name = "union_id", description = "联合表主键")
    private Long unionId;
    /** 技术人员id */
    @Excel(name = "technical_id", description = "技术人员id")
    private Long technicalId;
    /** 代理人员id */
    @Excel(name = "agent_id", description = "代理人员id")
    private Long agentId;
    /** 删除标志（0代表待审核1代表审核通过2代表删除） */
    @Excel(name = "deleted", description = "删除标志（0代表待审核1代表审核通过2代表删除）")
    private Integer deleted;
    /** 任务ID */
    @Excel(name = "task_id", description = "任务ID")
    private Long taskId;
    /** 请假开始时间 */
    @Excel(name = "start_time", description = "请假开始时间")
    private Date startTime;
    /** 请假结束时间 */
    @Excel(name = "stop_time", description = "请假结束时间")
    private Date stopTime;
    /** 休假移交表主键 */
    @Excel(name = "vacation_transfer_id", description = "休假移交表主键")
    private Long vacationTransferId;
    /** 创建人 */
    @Excel(name = "create_by", description = "创建人")
    private Long createBy;
    /** 创建时间 */
    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;
    /** 更新人 */
    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;
    /** 更新时间 */
    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;
}
