package com.jorchi.business.po;

import java.io.Serializable;
import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表ns_breeds_select的PO对象<br/>
 * 对应表名：ns_breeds_select，备注：种植项配置（选择种子）
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "ns_breeds_select")
public class NsBreedsSelect implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 选择种子表id */
    @Excel(name = "breeds_id", description = "选择种子表id")
    private Long breedsId;
    /** 种植项id */
    @Excel(name = "planting_clause_id", description = "种植项id")
    private Long plantingClauseId;
    /** 业务ID */
    @Excel(name = "business_id", description = "业务ID")
    private Long businessId;
    /** 作物名称 */
    @Excel(name = "cropName", description = "作物名称")
    private String cropName;
    /** 品种 */
    @Excel(name = "breeds", description = "品种")
    private String breeds;
    /** 使用量 */
    @Excel(name = "dosage", description = "使用量")
    private String dosage;
    /** 删除标志（0代表存在2代表删除） */
    @Excel(name = "deleted", description = "删除标志（0代表存在2代表删除）")
    private Integer deleted;
    /** 创建人 */
    @Excel(name = "create_by", description = "创建人")
    private Long createBy;
    /** 创建时间 */
    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;
    /** 更新人 */
    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;
    /** 更新时间 */
    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;
    /** 单位 */
    @Excel(name = "unit", description = "单位")
    private String unit;
}
