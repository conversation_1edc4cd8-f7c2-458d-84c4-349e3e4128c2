package com.jorchi.business.po;

import java.io.Serializable;
import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表ns_warehouse的PO对象<br/>
 * 对应表名：ns_warehouse，备注：仓库表
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "ns_warehouse")
public class NsWarehouse implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 仓库ID */
    @Excel(name = "warehouse_id", description = "仓库ID")
    private Long warehouseId;
    /** 农场ID */
    @Excel(name = "region_dept_id", description = "农场ID")
    private Long regionDeptId;
    /** 仓库分类 */
    @Excel(name = "category", description = "仓库分类")
    private String category;
    /** 仓库编号 */
    @Excel(name = "warehouse_code", description = "仓库编号")
    private String warehouseCode;
    /** 仓库名称 */
    @Excel(name = "warehouse_name", description = "仓库名称")
    private String warehouseName;
    /** 位置 */
    @Excel(name = "location", description = "位置")
    private String location;
    /** 存量 */
    @Excel(name = "stock_quantity", description = "存量")
    private Integer stockQuantity;
    /** 环境要求 */
    @Excel(name = "environmental_req", description = "环境要求")
    private String environmentalReq;
    /** 设备设施 */
    @Excel(name = "facilities", description = "设备设施")
    private String facilities;
    /** 温度控制 */
    @Excel(name = "temperature_ctrl", description = "温度控制")
    private String temperatureCtrl;
    /** 删除标志 */
    @Excel(name = "deleted", description = "删除标志")
    private Integer deleted;
    /** 创建人 */
    @Excel(name = "create_by", description = "创建人")
    private Long createBy;
    /** 创建时间 */
    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;
    /** 更新人 */
    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;
    /** 更新时间 */
    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;
}
