package com.jorchi.business.po;

import lombok.Data;

import java.util.Date;

/**
 * @Author: x<PERSON>n<PERSON>
 * @Date: 2023/12/5 17:00
 * @Describe:
 */

@Data
public class Weather {

    /** 预报日期*/
    private String fxDate;

    /** 日出时间*/
    private String sunrise;

    /** 日落时间*/
    private String sunset;

    /** 月升时间*/
    private String moonrise;

    /** 月落时间*/
    private String moonset;

    /** 夜间天气状况的图标代码*/
    private int iconNight ;

    /** 夜间天气状况描述 */
    private String textNight;

    /** 最高气温 */
    private int tempMax;

    /** 最低气温 */
    private int tempMin;

    /** 白天天气图标 */
    private int iconDay;

    /** 白天天气描述 */
    private String textDay;

}
