package com.jorchi.business.po;

import java.io.Serializable;
import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表ns_farming_process的PO对象<br/>
 * 对应表名：ns_farming_process，备注：农事流程
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "ns_farming_process")
public class NsFarmingProcess implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 农事流程id */
    @Excel(name = "farming_process_id", description = "农事流程id")
    private Long farmingProcessId;
    /** 农事流程名称 */
    @Excel(name = "process_name", description = "农事流程名称")
    private String processName;
    /** 投入品需求1-无需物料2-投入肥料3-投入物料4-投入种子 */
    @Excel(name = "input_type", description = "投入品需求1-无需物料2-投入肥料3-投入物料4-投入种子")
    private Integer inputType;
    /** 作业时间 */
    @Excel(name = "input_time", description = "作业时间")
    private String inputTime;
    /** 备注 */
    @Excel(name = "remark", description = "备注")
    private String remark;
    /** 流程阶段 */
    @Excel(name = "process_stage", description = "流程阶段")
    private Long processStage;
    /** 创建人id */
    @Excel(name = "create_id", description = "创建人id")
    private Long createId;
    /** 创建时间 */
    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;
    /** 修改人id */
    @Excel(name = "update_id", description = "修改人id")
    private Long updateId;
    /** 修改时间 */
    @Excel(name = "update_time", description = "修改时间")
    private Date updateTime;
    /** 是否删除0-未删除2-已删除 */
    @Excel(name = "deleted", description = "是否删除0-未删除2-已删除")
    private Integer deleted;
    /** 农场id */
    @Excel(name = "region_dept_id", description = "农场id")
    private Long regionDeptId;
}
