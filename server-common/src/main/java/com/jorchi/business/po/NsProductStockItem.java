package com.jorchi.business.po;

import java.io.Serializable;
import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.util.Date;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表ns_product_stock_item的PO对象<br/>
 * 对应表名：ns_product_stock_item，备注：农产品库存项目表
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "ns_product_stock_item")
public class NsProductStockItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 农产品库存项目id */
    @Excel(name = "item_id", description = "农产品库存项目id")
    private Long itemId;
    /** 农产品类别 */
    @Excel(name = "crop_type", description = "农产品类别")
    private String cropType;
    /** 农产品id */
    @Excel(name = "product_stock_id", description = "农产品id")
    private Long productStockId;
    /** 科别 */
    @Excel(name = "crop_category", description = "科别")
    private String cropCategory;
    /** 品种 */
    @Excel(name = "breeds", description = "品种")
    private String breeds;
    /** 农产品名称 */
    @Excel(name = "crop_name", description = "农产品名称")
    private String cropName;
    /** 规格 */
    @Excel(name = "spec", description = "规格")
    private String spec;
    /** 数量 */
    @Excel(name = "num", description = "数量")
    private BigDecimal num;
    /** 总重量 */
    @Excel(name = "weight", description = "总重量")
    private BigDecimal weight;
    /** 删除标记 */
    @Excel(name = "deleted", description = "删除标记")
    private Integer deleted;
    /** 创建人 */
    @Excel(name = "create_by", description = "创建人")
    private Long createBy;
    /** 创建时间 */
    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;
    /** 更新人 */
    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;
    /** 更新时间 */
    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;
    /** 农场id */
    @Excel(name = "region_dept_id", description = "农场id")
    private Long regionDeptId;
}
