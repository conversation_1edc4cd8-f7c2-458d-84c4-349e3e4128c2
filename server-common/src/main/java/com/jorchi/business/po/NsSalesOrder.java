package com.jorchi.business.po;

import java.io.Serializable;
import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表ns_sales_order的PO对象<br/>
 * 对应表名：ns_sales_order，备注：销售订单表
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "ns_sales_order")
public class NsSalesOrder implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 订单id */
    @Excel(name = "order_id", description = "订单id")
    private Long orderId;
    /** 销售单号 */
    @Excel(name = "sales_order_code", description = "销售单号")
    private String salesOrderCode;
    /** 申请人 */
    @Excel(name = "applicant", description = "申请人")
    private String applicant;
    /** 申请日期 */
    @Excel(name = "application_date", description = "申请日期")
    private Date applicationDate;
    /** 申请人手机号 */
    @Excel(name = "applicant_mobile", description = "申请人手机号")
    private String applicantMobile;
    /** 申请人ID */
    @Excel(name = "applicant_id", description = "申请人ID")
    private Long applicantId;
    /** 客户ID */
    @Excel(name = "customer_id", description = "客户ID")
    private Long customerId;
    /** 客户名称 */
    @Excel(name = "customer_name", description = "客户名称")
    private String customerName;
    /** 申请事由 */
    @Excel(name = "application_reason", description = "申请事由")
    private String applicationReason;
    /** 申请状态:0-未发起，1-待审批，2-已驳回，3-已完结 */
    @Excel(name = "application_status", description = "申请状态:0-未发起，1-待审批，2-已驳回，3-已完结")
    private Integer applicationStatus;
    /** 审批人 */
    @Excel(name = "approved_by", description = "审批人")
    private Long approvedBy;
    /** 审批时间 */
    @Excel(name = "approved_time", description = "审批时间")
    private Date approvedTime;
    /** 删除标志（0代表存在，2代表删除） */
    @Excel(name = "deleted", description = "删除标志（0代表存在，2代表删除）")
    private Integer deleted;
    /** 创建人 */
    @Excel(name = "create_by", description = "创建人")
    private Long createBy;
    /** 创建时间 */
    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;
    /** 更新人 */
    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;
    /** 更新时间 */
    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;
    /** 农场id */
    @Excel(name = "region_dept_id", description = "农场id")
    private Long regionDeptId;
}
