package com.jorchi.business.po;

import java.io.Serializable;
import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.util.Date;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表ns_fertilizer的PO对象<br/>
 * 对应表名：ns_fertilizer，备注：肥料管理
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "ns_fertilizer")
public class NsFertilizer implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 肥料主键 */
    @Excel(name = "fertilizer_id", description = "肥料主键")
    private Long fertilizerId;
    /** 供应商 */
    @Excel(name = "supplier_name", description = "供应商")
    private String supplierName;
    /** 肥料类型 */
    @Excel(name = "fertilizer_type", description = "肥料类型")
    private String fertilizerType;
    /** 肥料信息 */
    @Excel(name = "details", description = "肥料信息")
    private String details;
    /** 使用量与方法 */
    @Excel(name = "usage_method", description = "使用量与方法")
    private String usageMethod;
    /** 肥料单位 */
    @Excel(name = "fertilizer_unit", description = "肥料单位")
    private String fertilizerUnit;
    /** 备注 */
    @Excel(name = "remark", description = "备注")
    private String remark;
    /** 删除标志（0代表存在2代表删除） */
    @Excel(name = "deleted", description = "删除标志（0代表存在2代表删除）")
    private Integer deleted;
    /** 创建人 */
    @Excel(name = "create_by", description = "创建人")
    private Long createBy;
    /** 创建时间 */
    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;
    /** 更新人 */
    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;
    /** 更新时间 */
    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;
    /** 库存预警线 */
    @Excel(name = "stock_warning_line", description = "库存预警线")
    private BigDecimal stockWarningLine;
    /** 农场id */
    @Excel(name = "region_dept_id", description = "农场id")
    private Long regionDeptId;
}
