package com.jorchi.business.po;

import java.io.Serializable;
import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表ns_planting_clause_plan的PO对象<br/>
 * 对应表名：ns_planting_clause_plan，备注：种植项生产计划
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "ns_planting_clause_plan")
public class NsPlantingClausePlan implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 种植项ID */
    @Excel(name = "planting_clause_id", description = "种植项ID")
    private Long plantingClauseId;
    /** 种植标准ID */
    @Excel(name = "planting_standard_id", description = "种植标准ID")
    private Long plantingStandardId;
    /** 农事流程ID */
    @Excel(name = "farming_process_id", description = "农事流程ID")
    private Long farmingProcessId;
    /** 农事流程名称 */
    @Excel(name = "farming_process_name", description = "农事流程名称")
    private String farmingProcessName;
    /** 作业时间 */
    @Excel(name = "input_time", description = "作业时间")
    private String inputTime;
    /** 是否重复执行 */
    @Excel(name = "is_repeat", description = "是否重复执行")
    private Integer isRepeat;
    /** 周期开始 */
    @Excel(name = "cycle_start", description = "周期开始")
    private Integer cycleStart;
    /** 周期结束 */
    @Excel(name = "cycle_end", description = "周期结束")
    private Integer cycleEnd;
    /** 是否选择物料 */
    @Excel(name = "is_select", description = "是否选择物料")
    private Integer isSelect;
    /** 内容详情 */
    @Excel(name = "context", description = "内容详情")
    private String context;
    /** 删除标志 */
    @Excel(name = "deleted", description = "删除标志")
    private Integer deleted;
    /** 生产计划id */
    @Excel(name = "plan_id", description = "生产计划id")
    private Long planId;
    /** 创建人 */
    @Excel(name = "create_by", description = "创建人")
    private Long createBy;
    /** 创建时间 */
    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;
    /** 更新人 */
    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;
    /** 更新时间 */
    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;
}
