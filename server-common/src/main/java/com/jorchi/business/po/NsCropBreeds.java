package com.jorchi.business.po;

import java.io.Serializable;
import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.util.Date;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表ns_crop_breeds的PO对象<br/>
 * 对应表名：ns_crop_breeds，备注：作物品种
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "ns_crop_breeds")
public class NsCropBreeds implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 作物主键 */
    @Excel(name = "crop_id", description = "作物主键")
    private Long cropId;
    /** 农产品分类ID */
    @Excel(name = "product_stock_id", description = "农产品分类ID")
    private Long productStockId;
    /** 作物名称 */
    @Excel(name = "crop_name", description = "作物名称")
    private String cropName;
    /** 科别 */
    @Excel(name = "crop_category", description = "科别")
    private String cropCategory;
    /** 品种 */
    @Excel(name = "breeds", description = "品种")
    private String breeds;
    /** 作物类型 */
    @Excel(name = "crop_type", description = "作物类型")
    private String cropType;
    /** 供应商名称 */
    @Excel(name = "supplier_name", description = "供应商名称")
    private String supplierName;
    /** 种植模式 */
    @Excel(name = "cropping_pattern", description = "种植模式")
    private String croppingPattern;
    /** 备注 */
    @Excel(name = "remark", description = "备注")
    private String remark;
    /** 删除标志 */
    @Excel(name = "deleted", description = "删除标志")
    private Integer deleted;
    /** 创建人 */
    @Excel(name = "create_by", description = "创建人")
    private Long createBy;
    /** 创建时间 */
    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;
    /** 更新人 */
    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;
    /** 更新时间 */
    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;
    /** 库存预警线 */
    @Excel(name = "stock_warning_line", description = "库存预警线")
    private BigDecimal stockWarningLine;
    /** 农场id */
    @Excel(name = "region_dept_id", description = "农场id")
    private Long regionDeptId;
}
