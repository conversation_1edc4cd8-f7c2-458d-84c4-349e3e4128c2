package com.jorchi.business.po;

import java.io.Serializable;
import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表ns_region的PO对象<br/>
 * 对应表名：ns_region，备注：区块
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "ns_region")
public class NsRegion implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 区块ID
     */
    @Excel(name = "region_id", description = "区块ID")
    private Long regionId;
    /**
     * 区块名称
     */
    @Excel(name = "region_name", description = "区块名称")
    private String regionName;
    /**
     * 农场ID
     */
    @Excel(name = "region_dept_id", description = "农场ID")
    private Long regionDeptId;
    /**
     * 农场名称
     */
    @Excel(name = "region_dept_name", description = "农场名称")
    private String regionDeptName;
    /**
     * 区块面积
     */
    @Excel(name = "region_area", description = "区块面积")
    private String regionArea;
    /**
     * 区域负责人ID
     */
    @Excel(name = "region_leader_id", description = "区域负责人ID")
    private Long regionLeaderId;
    /**
     * 区域负责人姓名
     */
    @Excel(name = "region_leader_name", description = "区域负责人姓名")
    private String regionLeaderName;
    /**
     * 区域坐标点
     */
    @Excel(name = "region_points", description = "区域坐标点")
    private String regionPoints;
    /**
     * 删除标志（0代表存在2代表删除）
     */
    @Excel(name = "deleted", description = "删除标志（0代表存在2代表删除）")
    private Integer deleted;
    /**
     * 创建人
     */
    @Excel(name = "create_by", description = "创建人")
    private Long createBy;
    /**
     * 创建时间
     */
    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;
    /**
     * 更新人
     */
    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;
    /**
     * 更新时间
     */
    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;
}
