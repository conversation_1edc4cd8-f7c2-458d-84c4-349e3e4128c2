package com.jorchi.business.po;

import java.io.Serializable;
import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表ns_sales_order_return的PO对象<br/>
 * 对应表名：ns_sales_order_return，备注：销售退库表
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "ns_sales_order_return")
public class NsSalesOrderReturn implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 退货id */
    @Excel(name = "return_id", description = "退货id")
    private Long returnId;
    /** 申请人 */
    @Excel(name = "applicant", description = "申请人")
    private String applicant;
    /** 退货日期 */
    @Excel(name = "return_date", description = "退货日期")
    private Date returnDate;
    /** 申请人手机号 */
    @Excel(name = "applicant_mobile", description = "申请人手机号")
    private String applicantMobile;
    /** 申请人ID */
    @Excel(name = "applicant_id", description = "申请人ID")
    private Long applicantId;
    /** 退货事由 */
    @Excel(name = "return_reason", description = "退货事由")
    private String returnReason;
    /** 退货状态:0-未发起，1-待审批，2-已驳回，3-已完结 */
    @Excel(name = "return_status", description = "退货状态:0-未发起，1-待审批，2-已驳回，3-已完结")
    private Integer returnStatus;
    /** 审批人 */
    @Excel(name = "approved_by", description = "审批人")
    private Long approvedBy;
    /** 审批时间 */
    @Excel(name = "approved_time", description = "审批时间")
    private Date approvedTime;
    /** 删除标志（0代表存在，2代表删除） */
    @Excel(name = "deleted", description = "删除标志（0代表存在，2代表删除）")
    private Integer deleted;
    /** 创建人 */
    @Excel(name = "create_by", description = "创建人")
    private Long createBy;
    /** 创建时间 */
    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;
    /** 更新人 */
    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;
    /** 更新时间 */
    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;
    /** 销售订单id */
    @Excel(name = "order_id", description = "销售订单id")
    private Long orderId;
    /** 销售单号 */
    @Excel(name = "sales_order_code", description = "销售单号")
    private String salesOrderCode;
    /** 农场id */
    @Excel(name = "region_dept_id", description = "农场id")
    private Long regionDeptId;
}
