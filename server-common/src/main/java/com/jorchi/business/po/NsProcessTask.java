package com.jorchi.business.po;

import java.io.Serializable;
import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表ns_process_task的PO对象<br/>
 * 对应表名：ns_process_task，备注：流程任务表
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "ns_process_task")
public class NsProcessTask implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 流程任务ID */
    @Excel(name = "task_id", description = "流程任务ID")
    private Long taskId;
    /** 流程ID */
    @Excel(name = "process_id", description = "流程ID")
    private Long processId;
    /** 审批人 */
    @Excel(name = "approver", description = "审批人")
    private String approver;
    /** 审批人ID */
    @Excel(name = "approved_by", description = "审批人ID")
    private Long approvedBy;
    /** 审批状态:0-未发起，1-待审批，2-已驳回，3-已完结 */
    @Excel(name = "approval_status", description = "审批状态:0-未发起，1-待审批，2-已驳回，3-已完结")
    private Integer approvalStatus;
    /** 审批备注 */
    @Excel(name = "approval_remark", description = "审批备注")
    private String approvalRemark;
    /** 任务顺序 */
    @Excel(name = "task_sequence", description = "任务顺序")
    private Integer taskSequence;
    /** 实际完成时间 */
    @Excel(name = "actual_completion", description = "实际完成时间")
    private Date actualCompletion;
    /** 是否紧急（0：否，1：是） */
    @Excel(name = "is_urgent", description = "是否紧急（0：否，1：是）")
    private Integer isUrgent;
    /** 删除标志（0代表存在，1代表删除） */
    @Excel(name = "deleted", description = "删除标志（0代表存在，1代表删除）")
    private Integer deleted;
    /** 创建人 */
    @Excel(name = "create_by", description = "创建人")
    private Long createBy;
    /** 创建时间 */
    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;
    /** 更新人 */
    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;
    /** 更新时间 */
    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;
}
