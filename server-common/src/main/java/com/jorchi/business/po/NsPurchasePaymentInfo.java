package com.jorchi.business.po;

import java.io.Serializable;
import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.util.Date;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表ns_purchase_payment_info的PO对象<br/>
 * 对应表名：ns_purchase_payment_info，备注：采购支付信息表
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "ns_purchase_payment_info")
public class NsPurchasePaymentInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 支付信息ID */
    @Excel(name = "payment_info_id", description = "支付信息ID")
    private Long paymentInfoId;
    /** 采购记录ID */
    @Excel(name = "purchase_record_id", description = "采购记录ID")
    private Long purchaseRecordId;
    /** 供应商ID */
    @Excel(name = "supplier_id", description = "供应商ID")
    private Long supplierId;
    /** 供应商名称 */
    @Excel(name = "supplier_name", description = "供应商名称")
    private String supplierName;
    /** 供应商开户行 */
    @Excel(name = "supplier_bank", description = "供应商开户行")
    private String supplierBank;
    /** 供应商银行账号 */
    @Excel(name = "supplier_account", description = "供应商银行账号")
    private String supplierAccount;
    /** 支付金额 */
    @Excel(name = "payment_amount", description = "支付金额")
    private BigDecimal paymentAmount;
    /** 附件路径 */
    @Excel(name = "attachment", description = "附件路径")
    private String attachment;
    /** 删除标记 */
    @Excel(name = "deleted", description = "删除标记")
    private Integer deleted;
    /** 创建人 */
    @Excel(name = "create_by", description = "创建人")
    private Long createBy;
    /** 创建时间 */
    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;
    /** 更新人 */
    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;
    /** 更新时间 */
    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;
}
