package com.jorchi.business.po;

import java.io.Serializable;
import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.util.Date;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表ns_house_soil_collect_data_history的PO对象<br/>
 * 对应表名：ns_house_soil_collect_data_history，备注：大棚土壤传感器采集数据(历史数据)
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "ns_house_soil_collect_data_history")
public class NsHouseSoilCollectDataHistory implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 数据主键 */
    @Excel(name = "soil_id", description = "数据主键")
    private Long soilId;
    /** 大棚id */
    @Excel(name = "house_id", description = "大棚id")
    private Long houseId;
    /** 大棚编号 */
    @Excel(name = "house_name", description = "大棚编号")
    private String houseName;
    /** 节点ID */
    @Excel(name = "n", description = "节点ID")
    private String n;
    /** 采集时间 */
    @Excel(name = "d", description = "采集时间")
    private String d;
    /** 环境传感器和网关检测到的环境温度,只有当节点有该传感器时，这个属性才会被发送 */
    @Excel(name = "t", description = "环境传感器和网关检测到的环境温度,只有当节点有该传感器时，这个属性才会被发送")
    private BigDecimal t;
    /** 环境光照 */
    @Excel(name = "l", description = "环境光照")
    private BigDecimal l;
    /** 环境传感器和网关检测到的环境湿度,只有当节点有该传感器时，这个属性才会被发送 */
    @Excel(name = "q", description = "环境传感器和网关检测到的环境湿度,只有当节点有该传感器时，这个属性才会被发送")
    private BigDecimal q;
    /** 土壤EC,只有当节点有该传感器时，这个属性才会被发送 */
    @Excel(name = "E", description = "土壤EC,只有当节点有该传感器时，这个属性才会被发送")
    private Integer e;
    /** 土壤传感器测得的土壤TAO（温度）,只有当节点有该传感器时，这个属性才会被发送 */
    @Excel(name = "A", description = "土壤传感器测得的土壤TAO（温度）,只有当节点有该传感器时，这个属性才会被发送")
    private Integer a;
    /** 壤传感器测得的土壤MO（湿度）,只有当节点有该传感器时，这个属性才会被发送 */
    @Excel(name = "M", description = "壤传感器测得的土壤MO（湿度）,只有当节点有该传感器时，这个属性才会被发送")
    private Integer m;
    /** 土壤PH测量范围：0～14，精度：±0.3 */
    @Excel(name = "ph", description = "土壤PH测量范围：0～14，精度：±0.3")
    private BigDecimal ph;
    /** 土壤传感器电量 */
    @Excel(name = "b", description = "土壤传感器电量")
    private Integer b;
    /** 删除标志 */
    @Excel(name = "deleted", description = "删除标志")
    private Integer deleted;
    /** 创建人 */
    @Excel(name = "create_by", description = "创建人")
    private Long createBy;
    /** 创建时间 */
    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;
    /** 更新人 */
    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;
    /** 更新时间 */
    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;
}
