package com.jorchi.business.po;

import java.io.Serializable;
import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.util.Date;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表ns_use_application_item的PO对象<br/>
 * 对应表名：ns_use_application_item，备注：领用申请子项表
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "ns_use_application_item")
public class NsUseApplicationItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 子项ID */
    @Excel(name = "item_id", description = "子项ID")
    private Long itemId;
    /** 采购单号 */
    @Excel(name = "use_id", description = "采购单号")
    private Long useId;
    /** 投入品大类 */
    @Excel(name = "input_type", description = "投入品大类")
    private Integer inputType;
    /** 投入品子类 */
    @Excel(name = "input_category", description = "投入品子类")
    private String inputCategory;
    /** 商品ID */
    @Excel(name = "business_id", description = "商品ID")
    private Long businessId;
    /** 投入品名称 */
    @Excel(name = "input_name", description = "投入品名称")
    private String inputName;
    /** 领用数量 */
    @Excel(name = "quantity", description = "领用数量")
    private BigDecimal quantity;
    /** 单位 */
    @Excel(name = "unit", description = "单位")
    private String unit;
    /** 删除标志（0代表存在，2代表删除） */
    @Excel(name = "deleted", description = "删除标志（0代表存在，2代表删除）")
    private Integer deleted;
    /** 创建人 */
    @Excel(name = "create_by", description = "创建人")
    private Long createBy;
    /** 创建时间 */
    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;
    /** 更新人 */
    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;
    /** 更新时间 */
    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;
}
