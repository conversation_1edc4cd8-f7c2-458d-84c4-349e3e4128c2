package com.jorchi.business.po;

import java.io.Serializable;
import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表ns_vacation_transfer的PO对象<br/>
 * 对应表名：ns_vacation_transfer，备注：休假移交
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "ns_vacation_transfer")
public class NsVacationTransfer implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 休假移交主键 */
    @Excel(name = "vacation_transfer_id", description = "休假移交主键")
    private Long vacationTransferId;
    /** 生产技术人员 */
    @Excel(name = "production_technicians", description = "生产技术人员")
    private String productionTechnicians;
    /** 技术人员ID */
    @Excel(name = "artisan_id", description = "技术人员ID")
    private Long artisanId;
    /** 申请时间 */
    @Excel(name = "application_time", description = "申请时间")
    private Date applicationTime;
    /** 休假开始时间 */
    @Excel(name = "vacation_start_time", description = "休假开始时间")
    private Date vacationStartTime;
    /** 休假结束时间 */
    @Excel(name = "vacation_end_time", description = "休假结束时间")
    private Date vacationEndTime;
    /** 移交状态（0表示待审核，1表示审核通过，2表示审核拒绝） */
    @Excel(name = "transfer_state", description = "移交状态（0表示待审核，1表示审核通过，2表示审核拒绝）")
    private Integer transferState;
    /** 任务数量 */
    @Excel(name = "number_tasks", description = "任务数量")
    private Long numberTasks;
    /** 申请备注 */
    @Excel(name = "application_remarks", description = "申请备注")
    private String applicationRemarks;
    /** 删除标志（0代表存在2代表删除） */
    @Excel(name = "deleted", description = "删除标志（0代表存在2代表删除）")
    private Integer deleted;
    /** 创建人 */
    @Excel(name = "create_by", description = "创建人")
    private Long createBy;
    /** 创建时间 */
    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;
    /** 更新人 */
    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;
    /** 更新时间 */
    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;
    /** 农场id */
    @Excel(name = "region_dept_id", description = "农场id")
    private Long regionDeptId;

}
