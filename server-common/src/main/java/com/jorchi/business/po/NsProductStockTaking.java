package com.jorchi.business.po;

import java.io.Serializable;
import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.util.Date;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表ns_product_stock_taking的PO对象<br/>
 * 对应表名：ns_product_stock_taking，备注：农产品库存盘点表
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "ns_product_stock_taking")
public class NsProductStockTaking implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 投入品库存盘点ID */
    @Excel(name = "taking_id", description = "投入品库存盘点ID")
    private Long takingId;
    /** 农产品ID */
    @Excel(name = "product_stock_id", description = "农产品ID")
    private Long productStockId;
    /** 农产品类别 */
    @Excel(name = "crop_type", description = "农产品类别")
    private String cropType;
    /** 科别 */
    @Excel(name = "crop_category", description = "科别")
    private String cropCategory;
    /** 品种 */
    @Excel(name = "breeds", description = "品种")
    private String breeds;
    /** 农产品名称 */
    @Excel(name = "crop_name", description = "农产品名称")
    private String cropName;
    /** 库存量 */
    @Excel(name = "stock_quantity", description = "库存量")
    private BigDecimal stockQuantity;
    /** 盘点数量 */
    @Excel(name = "taking_stock_quantity", description = "盘点数量")
    private BigDecimal takingStockQuantity;
    /** 仓库ID */
    @Excel(name = "house_id", description = "仓库ID")
    private Long houseId;
    /** 仓库名称 */
    @Excel(name = "house_name", description = "仓库名称")
    private String houseName;
    /** 盘点结果:0-正常,1-盘亏，2-盘盈 */
    @Excel(name = "taking_result", description = "盘点结果:0-正常,1-盘亏，2-盘盈")
    private Integer takingResult;
    /** 库存预警状态 */
    @Excel(name = "warning_status", description = "库存预警状态")
    private String warningStatus;
    /** 盘点日期 */
    @Excel(name = "taking_date", description = "盘点日期")
    private Date takingDate;
    /** 盘点人 */
    @Excel(name = "taking_operator", description = "盘点人")
    private String takingOperator;
    /** 盘点人ID */
    @Excel(name = "taking_operator_id", description = "盘点人ID")
    private Long takingOperatorId;
    /** 删除标记 */
    @Excel(name = "deleted", description = "删除标记")
    private Integer deleted;
    /** 创建人 */
    @Excel(name = "create_by", description = "创建人")
    private Long createBy;
    /** 创建时间 */
    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;
    /** 更新人 */
    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;
    /** 更新时间 */
    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;
    /** 农场id */
    @Excel(name = "region_dept_id", description = "农场id")
    private Long regionDeptId;
    /** 盘点结果明细，json格式 */
    @Excel(name = "taking_items", description = "盘点结果明细，json格式")
    private String takingItems;
}
