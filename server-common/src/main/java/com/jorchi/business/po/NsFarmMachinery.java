package com.jorchi.business.po;

import java.io.Serializable;
import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.util.Date;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表ns_farm_machinery的PO对象<br/>
 * 对应表名：ns_farm_machinery，备注：农机管理
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "ns_farm_machinery")
public class NsFarmMachinery implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 农机主键 */
    @Excel(name = "machinery_id", description = "农机主键")
    private Long machineryId;
    /** 供应商 */
    @Excel(name = "supplier_name", description = "供应商")
    private String supplierName;
    /** 农机类型 */
    @Excel(name = "machinery_type", description = "农机类型")
    private String machineryType;
    /** 农机信息 */
    @Excel(name = "details", description = "农机信息")
    private String details;
    /** 使用方法 */
    @Excel(name = "usage_method", description = "使用方法")
    private String usageMethod;
    /** 农机单位 */
    @Excel(name = "machinery_unit", description = "农机单位")
    private String machineryUnit;
    /** 备注 */
    @Excel(name = "remark", description = "备注")
    private String remark;
    /** 删除标志（0代表存在2代表删除） */
    @Excel(name = "deleted", description = "删除标志（0代表存在2代表删除）")
    private Integer deleted;
    /** 创建人 */
    @Excel(name = "create_by", description = "创建人")
    private Long createBy;
    /** 创建时间 */
    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;
    /** 更新人 */
    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;
    /** 更新时间 */
    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;
    /** 库存预警线 */
    @Excel(name = "stock_warning_line", description = "库存预警线")
    private BigDecimal stockWarningLine;
    /** 农场id */
    @Excel(name = "region_dept_id", description = "农场id")
    private Long regionDeptId;
}
