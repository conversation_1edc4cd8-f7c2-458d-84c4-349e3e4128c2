package com.jorchi.business.po;

import java.io.Serializable;
import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表ns_audit_maintain的PO对象<br/>
 * 对应表名：ns_audit_maintain，备注：稽核维护
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "ns_audit_maintain")
public class NsAuditMaintain implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 稽核ID */
    @Excel(name = "audit_id", description = "稽核ID")
    private Long auditId;
    /** 稽核总项 */
    @Excel(name = "audit_total", description = "稽核总项")
    private String auditTotal;
    /** 稽核事项 */
    @Excel(name = "audit_name", description = "稽核事项")
    private String auditName;
    /** 描述信息 */
    @Excel(name = "audit_description", description = "描述信息")
    private String auditDescription;
    /** 删除标志（0代表存在2代表删除） */
    @Excel(name = "deleted", description = "删除标志（0代表存在2代表删除）")
    private Integer deleted;
    /** 创建人 */
    @Excel(name = "create_by", description = "创建人")
    private Long createBy;
    /** 创建时间 */
    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;
    /** 更新人 */
    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;
    /** 更新时间 */
    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;
    /** 农场id */
    @Excel(name = "region_dept_id", description = "农场id")
    private Long regionDeptId;
}
