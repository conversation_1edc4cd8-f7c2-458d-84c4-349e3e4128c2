package com.jorchi.business.po;

import java.io.Serializable;
import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表ns_supplier的PO对象<br/>
 * 对应表名：ns_supplier，备注：供应商表
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "ns_supplier")
public class NsSupplier implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 供应商ID */
    @Excel(name = "supplier_id", description = "供应商ID")
    private Long supplierId;
    /** 供应商类别 */
    @Excel(name = "supplier_category", description = "供应商类别")
    private String supplierCategory;
    /** 供应商名称 */
    @Excel(name = "supplier_name", description = "供应商名称")
    private String supplierName;
    /** 税号 */
    @Excel(name = "tax_number", description = "税号")
    private String taxNumber;
    /** 地址 */
    @Excel(name = "address", description = "地址")
    private String address;
    /** 开户银行 */
    @Excel(name = "bank_name", description = "开户银行")
    private String bankName;
    /** 银行账号 */
    @Excel(name = "bank_account", description = "银行账号")
    private String bankAccount;
    /** 联系人 */
    @Excel(name = "contact_person", description = "联系人")
    private String contactPerson;
    /** 联系人电话 */
    @Excel(name = "contact_phone", description = "联系人电话")
    private String contactPhone;
    /** 删除标志（0代表存在，2代表删除） */
    @Excel(name = "deleted", description = "删除标志（0代表存在，2代表删除）")
    private Integer deleted;
    /** 创建人 */
    @Excel(name = "create_by", description = "创建人")
    private Long createBy;
    /** 创建时间 */
    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;
    /** 更新人 */
    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;
    /** 更新时间 */
    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;
    /** 备注 */
    @Excel(name = "note", description = "备注")
    private String note;
    /** 农场id */
    @Excel(name = "region_dept_id", description = "农场id")
    private Long regionDeptId;
}
