package com.jorchi.business.po;

import java.io.Serializable;
import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.util.Date;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表ns_purchase_application_item的PO对象<br/>
 * 对应表名：ns_purchase_application_item，备注：采购申请子项表
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "ns_purchase_application_item")
public class NsPurchaseApplicationItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 子项ID */
    @Excel(name = "item_id", description = "子项ID")
    private Long itemId;
    /** 采购单号 */
    @Excel(name = "purchase_id", description = "采购单号")
    private Long purchaseId;
    /** 供应商ID */
    @Excel(name = "supplier_id", description = "供应商ID")
    private Long supplierId;
    /** 供应商名称 */
    @Excel(name = "supplier_name", description = "供应商名称")
    private String supplierName;
    /** 投入品大类 */
    @Excel(name = "input_type", description = "投入品大类")
    private Integer inputType;
    /** 投入品子类 */
    @Excel(name = "input_category", description = "投入品子类")
    private String inputCategory;
    /** 商品ID */
    @Excel(name = "business_id", description = "商品ID")
    private Long businessId;
    /** 采购名称 */
    @Excel(name = "input_name", description = "采购名称")
    private String inputName;
    /** 生产日期 */
    @Excel(name = "production_date", description = "生产日期")
    private Date productionDate;
    /** 有效日期 */
    @Excel(name = "effective_date", description = "有效日期")
    private Date effectiveDate;
    /** 采购单价 */
    @Excel(name = "unit_price", description = "采购单价")
    private BigDecimal unitPrice;
    /** 采购单位 */
    @Excel(name = "unit", description = "采购单位")
    private String unit;
    /** 数量 */
    @Excel(name = "quantity", description = "数量")
    private BigDecimal quantity;
    /** 验收数量 */
    @Excel(name = "check_quantity", description = "验收数量")
    private BigDecimal checkQuantity;
    /** 验收时间 */
    @Excel(name = "check_date", description = "验收时间")
    private Date checkDate;
    /** 实付金额 */
    @Excel(name = "pay_amount", description = "实付金额")
    private BigDecimal payAmount;
    /** 小计（unit_price*quantity） */
    @Excel(name = "subtotal", description = "小计（unit_price*quantity）")
    private BigDecimal subtotal;
    /** 删除标志（0代表存在，2代表删除） */
    @Excel(name = "deleted", description = "删除标志（0代表存在，2代表删除）")
    private Integer deleted;
    /** 创建人 */
    @Excel(name = "create_by", description = "创建人")
    private Long createBy;
    /** 创建时间 */
    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;
    /** 更新人 */
    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;
    /** 更新时间 */
    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;
}
