package com.jorchi.business.form;

import com.jorchi.business.po.NsInputStockTaking;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class SaveInputStockTakingForm {

    // 投入品库存盘点
    List<NsInputStockTaking> list;

    //农场id
    private Long regionDeptId;

    /**
     * 新的库存盘点提交表单
     */
    @Data
    public static class NewInputStockTakingForm {
        /** 投入品库存ID */
        private Long inputStockId;
        /** 仓库ID */
        private Long houseId;
        /** 盘点人 */
        private String takingOperator;
        /** 盘点日期 */
        private String takingDate;
        /** 库存项目列表 */
        private List<StockItemForm> stockItems;
        /** 农场ID */
        private String regionDeptId;
    }

    /**
     * 库存项目表单
     */
    @Data
    public static class StockItemForm {
        /** 投入品库存ID */
        private Long inputStockId;
        /** 业务ID */
        private Long businessId;
        /** 投入品类型 */
        private Integer inputType;
        /** 规格 */
        private String spec;
        /** 单位 */
        private String unit;
        /** 原库存数量 */
        private BigDecimal stockQuantity;
        /** 盘点数量 */
        private BigDecimal takingStockQuantity;
    }
}
