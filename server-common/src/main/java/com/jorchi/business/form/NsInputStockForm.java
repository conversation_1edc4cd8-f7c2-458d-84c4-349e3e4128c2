package com.jorchi.business.form;

import com.jorchi.framework.aspectj.lang.annotation.Excel;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import com.jorchi.framework.web.page.PageDomain;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * NsInputStock 请求参数对象。
 * 对应表名：ns_input_stock，备注：投入品库存表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NsInputStockForm extends PageDomain implements Serializable {
    private static final long serialVersionUID = 1L;

    @Excel(name = "input_id", description = "投入品库存id")
    private Long inputId;

    @Excel(name = "input_type", description = "投入品类型：2=肥料，3=植保剂，4=种子，5=农膜，6=其他")
    private Integer inputType;

    @Excel(name = "input_category", description = "投入品分类")
    private String inputCategory;

    @Excel(name = "input_sub_category", description = "投入品类别")
    private String inputSubCategory;

    @Excel(name = "input_name", description = "投入品名称")
    private String inputName;

    @Excel(name = "业务id")
    private Integer businessId;

    @Excel(name = "stock_warning_line", description = "库存预警线")
    private BigDecimal stockWarningLine;

    @Excel(name = "stock_quantity", description = "库存量")
    private BigDecimal stockQuantity;

    @Excel(name = "warning_status", description = "预警状态")
    private String warningStatus;

    @Excel(name = "deleted", description = "删除标记")
    private Integer deleted;

    @Excel(name = "create_by", description = "创建人")
    private Long createBy;

    @Excel(name = "create_time", description = "创建时间")
    private Date createTime;

    @Excel(name = "create_time_from", description = "创建时间-查询条件起始时间")
    private Date createTimeFrom;

    @Excel(name = "create_time_to", description = "创建时间-查询条件结束时间")
    private Date createTimeTo;

    @Excel(name = "update_by", description = "更新人")
    private Long updateBy;

    @Excel(name = "update_time", description = "更新时间")
    private Date updateTime;

    @Excel(name = "update_time_from", description = "更新时间-查询条件起始时间")
    private Date updateTimeFrom;

    @Excel(name = "update_time_to", description = "更新时间-查询条件结束时间")
    private Date updateTimeTo;
    @Excel(name = "region_dept_id", description = "农场id")
    private Long regionDeptId;
}
