package com.jorchi.server.po;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表T_POLICY_INSURANCE的PO对象<br/>
 * 对应表名：T_POLICY_INSURANCE,备注：保单表
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "T_POLICY_INSURANCE")
public class TPolicyInsurance implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 对应字段：ID,备注：主键 */
    private BigDecimal id;
    /** 对应字段：NAME_INSURED,备注：被保险人名称 */
    private String nameInsured;
    /** 对应字段：POLICY_HOLDER,备注：投保人 */
    private String policyHolder;
    /** 对应字段：POLICY_NUMBER,备注：投保单号 */
    private String policyNumber;
    /** 对应字段：POLICY_NO,备注：保单号 */
    private String policyNo;
    /** 对应字段：PREMIUM,备注：保费 */
    private String premium;
    /** 对应字段：INSURED_AMOUNT,备注：保额 */
    private String insuredAmount;
    /** 对应字段：PAYMENT_CODE,备注：缴费二维码 */
    private String paymentCode;
    /** 对应字段：PAYMENT_STATUS,备注：缴费状态  1已缴费  2未交费 */
    private String paymentStatus;
    /** 对应字段：POLICY_STATUS,备注：保单状态(1:农户提交申请；2:农户申请待确认(专员);3:农户申请待处理(出单员);4:农户确认申请；5:待上传缴费二维码(出单员);6:待修改缴费状态(出单员);7:投保成功) */
    private String policyStatus;
    /** 对应字段：POLICY_START_TIME,备注：最新起保时间 */
    private String policyStartTime;
    /** 对应字段：POLICY_END_TIME,备注：最新终保时间 */
    private String policyEndTime;
    /** 对应字段：INSURANCE_ADDRESS,备注：保险地址 */
    private String insuranceAddress;
    /** 对应字段：INSURED_PHONE,备注：联系电话(被保险人) */
    private String insuredPhone;
    /** 对应字段：COMPANY_POLICY,备注：保单所属公司 */
    private String companyPolicy;
    /** 对应字段：RENEWAL_MARK,备注：续保标志 0 是  1否 */
    private String renewalMark;
    /** 对应字段：PICTURE,备注：图片 */
    private String picture;
    /** 对应字段：NEXTNODE_USER_ID,备注：下一节点用户id */
    private BigDecimal nextnodeUserId;
    /** 对应字段：NEXTNODE_USER_NAME,备注：下一节点用户工号 */
    private String nextnodeUserName;
    /** 对应字段：NEXTNODE_NICK_NAME,备注：下一节点用户昵称 */
    private String nextnodeNickName;
    /** 对应字段：CREDATE_TIME,备注：创建时间 */
    private Date credateTime;
    /** 对应字段：HOLDER_PHONE,备注：联系电话(投保人) */
    private String holderPhone;
    /** 对应字段：PRODUCT_ID,备注：产品ID */
    private BigDecimal productId;
    /** 对应字段：USER_ID,备注：用户ID */
    private BigDecimal userId;
    /** 对应字段：DEPT_ID,备注：部门ID */
    private BigDecimal deptId;
    /** 对应字段：DEPT_NAME,备注：部门名称 */
    private String deptName;
    /** 对应字段：SNO,备注：组织代码 */
    private String sno;
    /** 对应字段：POLICY_NO_BEFORE,备注：上一年保单号时间 */
    private String policyNoBefore;
    /** 对应字段：PRODUCT_NAME,备注：产品名称 */
    private String productName;
}
