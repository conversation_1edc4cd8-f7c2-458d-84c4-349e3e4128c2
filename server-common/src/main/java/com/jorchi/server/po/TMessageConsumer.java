package com.jorchi.server.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 表ns_message_consumer的PO对象<br/>
 * 对应表名：ns_message_consumer
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "ns_message_consumer")
public class TMessageConsumer implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 对应字段：message_id */
    private String messageId;
    /** 对应字段：message_type */
    private String messageType;
    /** 对应字段：message_value */
    private String messageValue;
    /** 对应字段：run_node_name */
    private String runNodeName;
    /** 对应字段：next_run_time */
    private Date nextRunTime;
    /** 对应字段：retry_count */
    private Integer retryCount;
    /** 对应字段：retry_diff_ms */
    private Long retryDiffMs;
    /** 对应字段：max_retry_count,备注：累计最大重复消费次数 */
    private Integer maxRetryCount;

    /** 本结点正在处理 */
    boolean myNodeRunning;
}
