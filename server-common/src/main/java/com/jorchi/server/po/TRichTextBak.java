package com.jorchi.server.po;

import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表t_rich_text_bak的PO对象<br/>
 * 对应表名：t_rich_text_bak,备注：富文本内容（备份表）
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "t_rich_text_bak")
public class TRichTextBak implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 对应字段：id,备注：t_rich_text 表的主键 */
    private Long id;
    /** 对应字段：backup_time,备注：备份时间 */
    private Date backupTime;
    /** 对应字段：author,备注：操作人员 */
    private String author;
    /** 对应字段：text,备注：富文本内容 */
    private String text;
}
