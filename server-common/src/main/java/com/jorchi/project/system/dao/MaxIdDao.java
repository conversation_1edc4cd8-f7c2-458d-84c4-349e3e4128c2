package com.jorchi.project.system.dao;

import com.jorchi.project.system.domain.MaxId;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2021-02-25
 */
@Mapper
public interface MaxIdDao
{
    /**
     * 查询【请填写功能名称】
     *
     * @param idType 【请填写功能名称】ID
     * @return 【请填写功能名称】
     */
    public MaxId selectMaxIdById(String idType);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param maxId 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<MaxId> selectMaxIdList(MaxId maxId);

    /**
     * 新增【请填写功能名称】
     *
     * @param maxId 【请填写功能名称】
     * @return 结果
     */
    public int insertMaxId(MaxId maxId);

    /**
     * 修改【请填写功能名称】
     *
     * @param maxId 【请填写功能名称】
     * @return 结果
     */
    public int updateMaxId(MaxId maxId);

    /**
     * 删除【请填写功能名称】
     *
     * @param idType 【请填写功能名称】ID
     * @return 结果
     */
    public int deleteMaxIdById(String idType);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param idTypes 需要删除的数据ID
     * @return 结果
     */
    public int deleteMaxIdByIds(String[] idTypes);
}
