package com.jorchi.project.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jorchi.framework.web.domain.BaseEntity;
import pdfc.claim.common.CommonUtil;

import java.util.Date;

/**
 * 【各表主键值】对象 max_id
 *
 * <AUTHOR>
 * @date 2021-02-25
 */
public class MaxId extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID 类型，建议使用表名 */
    private String idType;

    /** 下一个最大值 */
    private Long maxValue;

    /** 有效期，空表示永久有效 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expiryDate;

    public void setIdType(String idType)
    {
        this.idType = idType;
    }

    public String getIdType()
    {
        return idType;
    }
    public void setMaxValue(Long maxValue)
    {
        this.maxValue = maxValue;
    }

    public Long getMaxValue()
    {
        return maxValue;
    }
    public void setExpiryDate(Date expiryDate)
    {
        this.expiryDate = expiryDate;
    }

    public Date getExpiryDate()
    {
        return expiryDate;
    }

    @Override
    public String toString() {
        return CommonUtil.append(
            " idType:", getIdType(),
            " maxValue:", getMaxValue(),
            " expiryDate:", getExpiryDate());
    }
}
