package com.jorchi.project.system.service;


import com.jorchi.common.constant.CommonDef;
import com.jorchi.common.utils.DictUtils;
import com.jorchi.framework.web.domain.AjaxResult;
import com.jorchi.project.system.dao.SysDictDataDao;
import com.jorchi.project.system.domain.SysDictData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import pdfc.claim.common.ClientException;

import javax.annotation.Resource;
import java.util.List;

/**
 * 字典 业务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysDictDataServiceImpl
{
    @Resource
    private SysDictDataDao dictDataMapper;
    @Autowired
    private MaxIdServiceImpl maxIdService;

    /**
     * 根据条件分页查询字典数据
     */
    public List<SysDictData> selectDictDataList(SysDictData dictData)
    {
        return dictDataMapper.selectDictDataList(dictData);
    }

    /**
     * 获取数据字典 Key 对应的值
     */
    public String getValueByKey(String dictType, String dictKey) {
        SysDictData dictData = new SysDictData();
        dictData.setDictType(dictType);
        dictData.setDictLabel(dictKey);
        dictData.setStatus(CommonDef.UN_VALID);
        List<SysDictData> resultList = dictDataMapper.selectDictDataList(dictData);
        return resultList == null || resultList.isEmpty() ? null : resultList.get(0).getDictValue();
    }

    /**
     * 根据字典数据ID查询信息
     */
    public SysDictData selectDictDataById(Long dictCode)
    {
        return dictDataMapper.selectDictDataById(dictCode);
    }

    /**
     * 批量删除字典数据信息
     */
    public int deleteDictDataByIds(Long[] dictCodes)
    {
        int row = dictDataMapper.deleteDictDataByIds(dictCodes);
        if (row > 0)
        {
            DictUtils.clearDictCache();
        }
        return row;
    }

    /**
     * 新增保存字典数据信息
     */
    public AjaxResult insertDictData(SysDictData dictData)
    {
        // 已存在
        List<SysDictData> dictList = dictDataMapper.selectDictByLabel(dictData.getDictType(), dictData.getDictLabel());
        if (dictList != null && !dictList.isEmpty() && !dictList.get(0).getDictCode().equals(dictData.getDictCode())) {
            return AjaxResult.clientError("字典标签“", dictData.getDictLabel(), "”已存在！请重新输入");
        }
        dictData.setDictCode(maxIdService.getAndIncrement("sys_dict_code"));
        int row = dictDataMapper.insertDictData(dictData);
        if (row > 0)
        {
            DictUtils.clearDictCache();
        }
        return AjaxResult.success(row);
    }

    /**
     * 修改保存字典数据信息
     */
    public AjaxResult updateDictData(SysDictData dictData)
    {
        // 已存在
        List<SysDictData> dictList = dictDataMapper.selectDictByLabel(dictData.getDictType(), dictData.getDictLabel());
        if (dictList != null && !dictList.isEmpty() && !dictList.get(0).getDictCode().equals(dictData.getDictCode())) {
            return AjaxResult.clientError("字典标签“", dictData.getDictLabel(), "”已存在！请重新输入");
        }
        int row = dictDataMapper.updateDictData(dictData);
        if (row > 0)
        {
            DictUtils.clearDictCache();
        }
        return AjaxResult.success(row);
    }

    /**
     * 数据字典的值也需要唯一性检查
     */
    public void checkValueUnique(SysDictData dict) {
        // 服务项的 值可以不唯一，其它的都要唯一
        if (dict.getDictType().equals("04"))
            return;

        List<SysDictData> dictDatas = dictDataMapper.selectDictLabelValueUnique(dict.getDictType(),
                dict.getDictLabel(), dict.getDictValue());
        if (dictDatas != null && !dictDatas.isEmpty()) {
            if (dictDatas.get(0).getDictCode().equals(dict.getDictCode())) // it's me
                return;
            throw ClientException.of("在此数据字典中已经存在值：", dict.getDictValue());
        }
    }
}
