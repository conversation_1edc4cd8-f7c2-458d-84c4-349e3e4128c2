package com.jorchi.project.system.po;

import java.io.Serializable;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 表sys_region的PO对象<br/>
 * 对应表名：sys_region,备注：省市区域信息表
 * 通过 CodeGenerator 工具自动生成，请勿手工修改！！！
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
// @Table(name = "sys_region")
public class SysRegion implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 对应字段：region_id,备注：主键 */
    private Long regionId;
    /** 对应字段：parent_id,备注：父级区域id */
    private Long parentId;
    /** 对应字段：is_leaf,备注：状态（0非叶子结点, 1叶子结点） */
    private String isLeaf;
    /** 对应字段：ancestors,备注：祖级列表 */
    private String ancestors;
    /** 对应字段：region_name,备注：区域名称 */
    private String regionName;
    /** 对应字段：order_num,备注：显示顺序 */
    private Integer orderNum;
    /** 对应字段：leader,备注：负责人 */
    private String leader;
    /** 对应字段：phone,备注：联系电话 */
    private String phone;
    /** 对应字段：email,备注：邮箱 */
    private String email;
    /** 对应字段：status,备注：状态（0正常 1停用） */
    private String status;
    /** 对应字段：del_flag,备注：删除标志（0代表存在 2代表删除） */
    private String delFlag;
    /** 对应字段：create_by,备注：创建者 */
    private String createBy;
    /** 对应字段：create_time,备注：创建时间 */
    private Date createTime;
    /** 对应字段：update_by,备注：更新者 */
    private String updateBy;
    /** 对应字段：update_time,备注：更新时间 */
    private Date updateTime;
    /** 对应字段：sno,备注：组织代码 */
    private String sno;
    /** 对应字段：pin_yin_1,备注：区域名称拼音各字首字母 */
    private String pinYin1;
    /** 对应字段：pin_yin_2,备注：区域名称拼音 */
    private String pinYin2;
}
