package com.jorchi.project.system.dao;

import org.apache.ibatis.annotations.Mapper;

import com.jorchi.project.system.domain.SystemVar;
import com.jorchi.common.MybatisBaseDao;
import org.apache.ibatis.annotations.Param;

/**
 *
 * 表ns_system_var对应的基于MyBatis实现的Dao接口<br/>
 * 在其中添加自定义方法
 *
 */
@Mapper
public interface SystemVarDao extends MybatisBaseDao<SystemVar, Integer> {
    SystemVar selectByName(@Param("name") String name);
}
