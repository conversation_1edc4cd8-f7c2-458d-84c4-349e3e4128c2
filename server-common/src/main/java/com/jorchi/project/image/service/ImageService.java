package com.jorchi.project.image.service;

import com.jorchi.common.constant.CommonDef;
import com.jorchi.common.utils.IdUtils;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import com.jorchi.project.image.dao.TImageDao;
import com.jorchi.project.image.dao.TImageTmpDao;
import com.jorchi.project.image.po.TImage;
import com.jorchi.project.image.po.TImageTmp;
import com.jorchi.project.image.po.Source64;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import pdfc.claim.common.ClientException;
import pdfc.claim.common.CommonUtil;
import pdfc.claim.common.FileUtil;
import pdfc.framework.utils.Beans;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;

/**
 * @auther Sugar
 * @date 2021-03-16 16:53
 */
@Service
public class ImageService {
    private static final Log log = LogFactory.getLog(ImageService.class);

    @Resource
    TImageTmpDao imageTmpDao;
    @Resource
    TImageDao imageDao;

    @Autowired
    MaxIdServiceImpl maxIdService;

    /**
     * 删除过期资源
     */
    public void clearOverDue() {
        // 过期时间
        Date overDue = new Date();

        // 删除过期临时图片
        List<TImageTmp> overDueImageTmpList = imageTmpDao.selectOverDue(overDue);
        if (overDueImageTmpList != null && !overDueImageTmpList.isEmpty())
            removeFileAndRecord(overDueImageTmpList, true);

        // 删除过期图片
        List<TImageTmp> overDueImageList = imageDao.selectOverDue(overDue);
        if (overDueImageList != null && !overDueImageList.isEmpty())
            removeFileAndRecord(overDueImageList, false);
    }

    /**
     * 物理删除图片 并删除数据库记录
     * @param overDueImageList
     * @param isTmpTable
     */
    private void removeFileAndRecord(List<TImageTmp> overDueImageList, boolean isTmpTable) {
        for (TImageTmp src : overDueImageList) {
            // 防止误删除，把资源先备份几个月后再删除
            if (!src.getPath().endsWith(".bak")) {
                removeToBackup(src, isTmpTable);
                continue;
            }

            // 物理删除图片
            removeFile(src.getPath().substring(0, src.getPath().lastIndexOf(".bak")));

            // 数据库删除记录
            if (isTmpTable)
                imageTmpDao.deleteByPrimaryKey(src.getCode());
            else
                imageDao.deleteByPrimaryKey(src.getCode());
        }
    }

    /**
     * 防止误删除，把资源先备份几个月后再删除
     */
    public void removeToBackup(TImageTmp src, boolean isTmpTable) {
        // 把 code 变一下
        src.setPath(CommonUtil.append(src.getPath(), ".bak"));
        // 把时间往后推几个月
        src.setExpiredTime(new Date(src.getExpiredTime().getTime() + CommonDef.ONE_DAY * 240));
        // 数据库删除记录
        if (isTmpTable)
            imageTmpDao.updateSelectiveByPrimaryKey(src);
        else
            imageDao.updateSelectiveByPrimaryKey(src);
    }

    /**
     * 设置案件图片正式使用，5年后过期
     * path 是一个http 地址
     */
    public void setImageOverDueByPath(String path) {
        setImageOverDue(getCodeFromPath(path), System.currentTimeMillis() + CommonDef.ONE_DAY * 365 * 5);
    }

    /**
     * 设置为系统图片，永不过期
     */
    public void setSystemImageNotOverDueByPath(String path) {
        setImageOverDue(getCodeFromPath(path), null);
    }

    /**
     * 从 http 地址找出 code 的值
     */
    public String getCodeFromPath(String path) {
        if (path.contains("\\u003d")) // \u003d 是 = 符号
            path = path.replace("\\u003d", "=");
        int start = path.indexOf("=");
        if (start == -1) // path is a code
            return path;

        start = path.indexOf("code=");
        if (start == -1) {
            log.warn(CommonUtil.append("Code can not found, path:", path));
            return null;
        }
        start += 5;
        int end = path.indexOf("&");
        if (end == -1)
            end = path.length();
        if (end == -1) {
            log.error(CommonUtil.append("Code not found error, path:", path));
            return null;
        }

        return path.substring(start, end).replace("\"", "");
    }

    /**
     * 设置资源图片已被使用，5年后过期
     */
    public void setImageOverDue5Years(String code) {
        setImageOverDue(code, System.currentTimeMillis() + CommonDef.ONE_DAY * 365 * 5);
    }

    /**
     * 设置资源图片已被使用，永不过期
     */
    @Deprecated
    public void setImageOverDueNever(String code) {
        setImageOverDue(code, null);
    }

    /**
     * 设置资源图片已被使用
     */
    public void setImageOverDue(String code, Long expiredTime) {
        if (code == null) {
            log.error("setImageOverDue, code can not empty error!!!");
            return;
        }

        // 临时表
        TImageTmp tmp = imageTmpDao.selectByPrimaryKey(code);
        if (tmp == null) {
            log.error(CommonUtil.append("TImageTmp not found warn. code:", code));
            return;
        }

        // 保存到正式表
        TImage img = imageDao.selectByPrimaryKey(code);
        if (img == null) {
            img = new TImage();
            Beans.copy(tmp, img);
            // 过期时间
            img.setExpiredTime(expiredTime == null ? null : new Date(expiredTime));
            imageDao.insert(img);
        }

        // 删除临时表
        imageTmpDao.delete(tmp);
    }

    /** 把资源文件从硬盘删除 */
    private void removeFile(String filePath) {
        if (filePath == null || filePath.isEmpty())
            return;

        // 把资源文件从硬盘删除
        File image = new File(CommonDef.UPLOAD_FOLDER, filePath);
        if (image.exists()) {
            log.info(CommonUtil.append("remove image file, filePath:", filePath));
            image.delete();
        }
    }

    /**
     * 保存图片到正式表
     */
    public String saveTmp(String base64Data) throws Exception {
        return saveTmp(base64Data, false);
    }

    /**
     * 保存为临时图片
     * @param base64Data
     */
    public String saveTmp(String base64Data, boolean save2Tmp) throws Exception {
        Source64 sourceBean = getSourceBeanFromBase64(base64Data);
        String suffix = getSuffix(sourceBean.getDataPrix());
        // 生成主键ID
        String code = genCode();
        // 生成文件名
        String saveFileName = genSaveFileName(suffix, code);

        //因为BASE64Decoder的jar问题，此处使用spring框架提供的工具包
        byte[] bs = Base64Utils.decodeFromString(sourceBean.getData());
        // 保存到临时表
        if (save2Tmp)
            saveTmp2db(code, saveFileName,null);
        else
            save2db(code, saveFileName,null);

        // 保存文件
        FileUtil.writeByteArrayToFile(CommonDef.UPLOAD_FOLDER, saveFileName, bs);

        // 返回HTTP图片地址
        /*String httpUrl = CommonUtil.append(IMAGE_URI, CommonDef.IMAGE_URL_PREFIX, code);
        JSONObject jsonRe = new JSONObject();
        jsonRe.put("code", code);
        jsonRe.put("url", httpUrl);*/
        return code;
    }

    /**
     * 保存为图片（正式表）
     */
    public String saveTmp(MultipartFile file) throws Exception {
        return saveTmp(file, false,file.getOriginalFilename());
    }

    /**
     * 根据id查询图片信息
     * @param codes
     * @return
     */
    public List<TImage> selectByCodes(List<String> codes){
        List<TImage> list = imageDao.selectByPrimaryKeys(codes);

        List<TImageTmp> tImageTmps = imageTmpDao.selectByPrimaryKeys(codes);

        if (!CollectionUtils.isEmpty(tImageTmps)){
            for (TImageTmp tImageTmp : tImageTmps) {
                TImage tImage = new TImage();
                BeanUtils.copyProperties(tImageTmp,tImage);
                list.add(tImage);
            }
        }
        return list;
    }

    /**
     * 保存为临时图片
     */
    public String saveTmp(MultipartFile file, boolean save2Tmp,String fileName) throws Exception {
        // 生成主键ID
        String code = genCode();

        // 生成文件名
        String saveFileName = genSaveFileName(file.getOriginalFilename(), code);

        // 保存到临时表
        if (save2Tmp)
            saveTmp2db(code, saveFileName,fileName);
        else
            save2db(code, saveFileName,fileName);

        FileUtil.writeByteArrayToFile(CommonDef.UPLOAD_FOLDER, saveFileName, file.getBytes());

        // 返回HTTP图片地址
        /*String httpUrl = CommonUtil.append(IMAGE_URI, CommonDef.IMAGE_URL_PREFIX, code);
        JSONObject jsonRe = new JSONObject();
        jsonRe.put("code", code);
        jsonRe.put("url", httpUrl);*/
        return code;
    }

    /**
     * 生成Code
     */
    public String genCode() {
        String code = IdUtils.simpleUUID();
        // 检查数据库主键是否已经存在
        TImageTmp tmp_ = imageTmpDao.selectByPrimaryKey(code);
        if (tmp_ != null)
            return String.valueOf(System.currentTimeMillis());

        TImage image_ = imageDao.selectByPrimaryKey(code);
        if (image_ != null)
            return String.valueOf(System.currentTimeMillis());
        return code;
    }

    /**
     * 保存到临时表
     */
    public int saveTmp2db(String code, String saveFileName,String fileName) {
        // long imageId = maxIdService.getAndIncrement("image_tmp.id");
        TImageTmp tmp = new TImageTmp();
        tmp.setCode(code);
        tmp.setCreateTime(new Date(System.currentTimeMillis()));
        tmp.setExpiredTime(new Date(System.currentTimeMillis() + CommonDef.ONE_HOUR * 8));
        tmp.setPath(saveFileName);
        tmp.setUsedFlag("0");
        tmp.setFileName(fileName);
        return imageTmpDao.insert(tmp);
        //return imageTmpDao.save(tmp);
    }

    /**
     * 保存到正式表
     * @author: TanShunFu
     * @date: 2021.09.17
     */
    public int save2db(String code, String saveFileName,String fileName) {
        TImage tmp = new TImage();
        tmp.setCode(code);
        tmp.setCreateTime(new Date(System.currentTimeMillis()));
        tmp.setExpiredTime(null);
        tmp.setPath(saveFileName);
        tmp.setFileName(fileName);
        return imageDao.insert(tmp);
    }

    /** set data and preFix */
    private Source64 getSourceBeanFromBase64(String base64Data) throws Exception
    {
        if (base64Data == null || "".equals(base64Data))
        {
            throw new ClientException("上传失败，上传图片数据不能为空");
        }
        else
        {
            String[] d = base64Data.split("base64,");
            if (d != null && d.length == 2)
            {
                Source64 bean = new Source64();
                bean.setDataPrix(d[0]);
                bean.setData(d[1]);
                return bean;
            }
            else
            {
                log.error("base 64 data error!");
                throw new Exception("数据不合法。base64 data must start width：'data:image/jpeg;base64,' or 'data:image/png;base64,' and so on.");
            }
        }
    }

    private String getSuffix(String dataPrix) throws Exception
    {
        String suffix = "";
        if ("data:image/jpeg;".equalsIgnoreCase(dataPrix))
        {//data:image/jpeg;base64,base64编码的jpeg图片数据
            suffix = ".jpg";
        }
        else if ("data:image/x-icon;".equalsIgnoreCase(dataPrix))
        {//data:image/x-icon;base64,base64编码的icon图片数据
            suffix = ".ico";
        }
        else if ("data:image/gif;".equalsIgnoreCase(dataPrix))
        {//data:image/gif;base64,base64编码的gif图片数据
            suffix = ".gif";
        }
        else if ("data:image/png;".equalsIgnoreCase(dataPrix))
        {//data:image/png;base64,base64编码的png图片数据
            suffix = ".png";
        }
        else if ("data:image/png;".equalsIgnoreCase(dataPrix))
        {//data:image/png;base64,base64编码的png图片数据
            suffix = ".jpeg";
        }
        else
        {
            log.error(CommonUtil.append("Unknown image file type. dataProx:", dataPrix));
            throw new Exception("Unknown image file type. 上传图片格式不合法");
        }
        return suffix;
    }

    public String genSaveFileName(String suffix, String code)
    {
        StringBuilder sb = new StringBuilder(22);
        Calendar cal = Calendar.getInstance();
        // 年/月/日/时.分.秒
        sb.append(cal.get(Calendar.YEAR)-2000).append(File.separator);
        sb.append(addZero(cal.get(Calendar.MONTH)+1)).append(File.separator);
        sb.append(addZero(cal.get(Calendar.DAY_OF_MONTH))).append(File.separator);
        sb.append(addZero(cal.get(Calendar.HOUR_OF_DAY))).append('.');
        sb.append(addZero(cal.get(Calendar.MINUTE))).append('.');
        sb.append(addZero(cal.get(Calendar.SECOND))).append("-");
        sb.append(code); // IdUtils.simpleUUID()
        sb.append(suffix); // fileName.jpg
        return sb.toString();
    }

    private String addZero(int i)
    {
        if (i > 9)
            return String.valueOf(i);
        else
            return "0" + i;
    }

    /**
     * 通过 code 获取图片文件
     */
    public File getImageFileByCode(String code) {
        if (code == null || code.trim().isEmpty())
            return null;

        // 先访问临时表，一般临时表的记录数要少一些
        TImageTmp tmp_ = imageTmpDao.selectByPrimaryKey(code);
        if (tmp_ != null)
            return getImageFileByPath(tmp_.getPath());

        // 临时表不存在，再查询正式表
        TImage image_ = imageDao.selectByPrimaryKey(code);
        if (image_ != null)
            return getImageFileByPath(image_.getPath());
        return null;
    }

    /**
     * 通过文件路径获取图片文件
     */
    private File getImageFileByPath(String path) {
        if (path == null)
            return null;

        if (path.startsWith(CommonDef.UPLOAD_FOLDER)) // 数据库保存了前缀
            return new File(path);
        return new File(CommonDef.UPLOAD_FOLDER, path); // 数据库未保存前缀
    }

  /*  public static void main(String[] args){
        String source = "/^[0-9a-z]*$/";
        String reg= "/^(?!^[0-9]*$)^([a-z0-9]{8,16}$)/";
        String s = "c5412a2ea9aa4b128a93934560890e95";

        String str="/(.*)\\.(jpg|bmp|gif|ico|pcx|jpeg|tif|png|raw|tga)$/";

        String str1="/(\\.jpeg | \\.png | \\.jpg)/i";

        String str2 =  "(((jpg|png|gif|JPG|PNG|GIF))$)";
        //String str2 =  "([^\\s]+(\\.(?i)(jpg|png|gif|bmp))$)";
        System.out.println("png".matches(str2));
    }*/

    /**
     * 把图片地址改为 code 列表来存储
     * @param urlArray 前端 h5 页面的 图片地址，期望是以;隔开的
     * @return  code 列表（;隔开）
     */
    public String list2ImageCodes(String urlArray) {
        if (urlArray == null || urlArray.trim().isEmpty())
            return null;

        // 去除[], 把,替换为;
        urlArray = getImageByRule(urlArray);

        StringBuilder result = new StringBuilder(120);
        String code;
        String[] httpArray = urlArray.split(";");
        for (String url : httpArray) {
            code = getCodeFromPath(url);
            if (code == null || code.trim().isEmpty()) {
                log.error("code not found error!!");
                throw new ClientException("image code not found error!!!");
            }
            result.append(code).append(";");

            // 该图片已被使用，永不过期
            setImageOverDue(code, null);
        }
        return result.substring(0, result.length() - 1);
    }

    /**
     * 去除[], 把,替换为;
     * @author: Sugar.Tan
     * @date: 2021.05.07 10:19
     */
    public String getImageByRule(String urlArray) {
        if (urlArray.charAt(0) == '[')
            urlArray = urlArray.substring(1, urlArray.length()-1);
        if (urlArray.contains(","))
            urlArray = urlArray.replace(",", ";");
        return urlArray;
    }

    /**
     * 把图片的 code 字符串列表转换为 http 地址，以便前端显示
     * @auther Sugar.Tan
     */
    /*public List<String> codes2ImageList(String imageCodes) {
        if (imageCodes == null || imageCodes.trim().isEmpty())
            return new ArrayList<>(0);

        // 去除[], 把,替换为;
        imageCodes = getImageByRule(imageCodes);

        String[] codeArray = imageCodes.split(";");
        ArrayList result = new ArrayList(codeArray.length);
        String httpUrl;
        for (String code : codeArray) {
            if (code == null || code.trim().isEmpty())
                continue;

            if (code.contains("code=")) { // 旧版本(code 已经是 http 地址)
                result.add(code);
                continue;
            }

            httpUrl = CommonUtil.append(IMAGE_URI, CommonDef.IMAGE_URL_PREFIX, code);
            result.add(httpUrl);
        }
        return result;
    }*/
}
