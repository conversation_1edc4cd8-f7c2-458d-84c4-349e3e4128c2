package com.jorchi.project.monitor.service;

import com.jorchi.project.monitor.dao.SysLogininforDao;
import com.jorchi.project.monitor.domain.SysLogininfor;
import com.jorchi.project.system.service.MaxIdServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 系统访问日志情况信息 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysLogininforServiceImpl
{

    @Resource
    private SysLogininforDao logininforMapper;

    @Autowired
    private MaxIdServiceImpl maxIdService;

    /**
     * 新增系统登录日志
     *
     * @param logininfor 访问日志对象
     */
    public void insertLogininfor(SysLogininfor logininfor)
    {
        logininfor.setInfoId(maxIdService.getAndIncrement("sysLoginInfo.id"));
        logininforMapper.insertLogininfor(logininfor);
    }

    /**
     * 查询系统登录日志集合
     *
     * @param logininfor 访问日志对象
     * @return 登录记录集合
     */
    public List<SysLogininfor> selectLogininforList(SysLogininfor logininfor)
    {
        return logininforMapper.selectLogininforList(logininfor);
    }

    /**
     * 批量删除系统登录日志
     *
     * @param infoIds 需要删除的登录日志ID
     * @return
     */
    public int deleteLogininforByIds(Long[] infoIds)
    {
        return logininforMapper.deleteLogininforByIds(infoIds);
    }

    /**
     * 清空系统登录日志
     */
    public void cleanLogininfor()
    {
        logininforMapper.cleanLogininfor();
    }
}
